package enums

import (
	"database/sql/driver"
)

type Status int

const (
	Inactive Status = iota
	Active
	Unknown
)

func (us Status) String() string {
	switch us {
	case Inactive:
		return "INACTIVE"
	case Active:
		return "ACTIVE"
	default:
		return "UNKNOWN"
	}
}

func (us Status) Values() []string {
	return []string{Inactive.String(), Active.String(), Unknown.String()}
}

func (us Status) Value() (driver.Value, error) {
	return int64(us), nil
}

func (us *Status) Scan(val any) error {
	var s string
	switch v := val.(type) {
	case nil:
		return nil
	case string:
		s = v
	case int64:
		s = Status(v).String()
	}

	switch s {
	case "INACTIVE":
		*us = Inactive
	case "ACTIVE":
		*us = Active
	default:
		*us = Unknown
	}
	return nil
}
