/*
Copyright © 2025 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"context"
	"os"
	"path/filepath"
	"resflow/database"
	infrastructure2 "resflow/internal/common/infrastructure"
	"resflow/internal/plugin/application"
	"resflow/internal/plugin/domain"
	"resflow/internal/plugin/infrastructure"
	"resflow/utils"

	"github.com/spf13/cobra"
)

var builtin = new(bool)

// installCmd represents the install command
var installCmd = &cobra.Command{
	Use:   "install",
	Short: "install plugin",
	Long: `Install plugin command
Examples:
	Install builtin plugin
	$ resflow plugin install --builtin
	Install user plugin
	$resflow plugin install`,
	Run: func(cmd *cobra.Command, args []string) {
		installPlugin(*builtin)
	},
}

func init() {
	pluginCmd.AddCommand(installCmd)

	installCmd.Flags().BoolVarP(builtin, "builtin", "b", false, "install builtin plugin")
}

func installPlugin(builtin bool) {
	// 获取当前目录
	workDirPath, err := os.Getwd()
	if err != nil {
		utils.Logger.Error("获取当前目录失败", err)
		return
	}

	var pluginsPath string

	if builtin {
		utils.Logger.Info("开始安装内置插件")
		pluginsPath = filepath.Join(workDirPath, "plugins")
		_, err = os.Stat(pluginsPath)
		if err != nil {
			if os.IsNotExist(err) {
				utils.Logger.Error("plugins目录不存在")
				return
			}
			utils.Logger.Error("获取plugins目录信息失败", err)
			return
		}
	} else {
		utils.Logger.Info("开始安装插件")
	}
	// 遍历插件目录
	pluginDirs, err := os.ReadDir(pluginsPath)
	if err != nil {
		utils.Logger.Error("读取插件目录失败", err)
		return
	}
	pluginStore := infrastructure.NewPluginStore(database.Client)
	pluginInstaller := application.NewPluginInstaller(
		domain.NewPluginScanner(infrastructure.NewManifestLoader()),
		pluginStore,
		domain.NewPluginManager(pluginStore),
		domain.NewNodeDefinitionManager(infrastructure.NewNodeDefinitionStore(database.Client)),
		infrastructure2.NewEntTransactionManager(database.Client),
	)

	for _, pluginDir := range pluginDirs {
		if pluginDir.IsDir() {
			// 获取各个版本
			versionDirs, err := os.ReadDir(filepath.Join(pluginsPath, pluginDir.Name()))
			if err != nil {
				utils.Logger.Error("读取插件版本目录失败", err)
				continue
			}
			for _, versionDir := range versionDirs {
				// 安装插件
				if versionDir.IsDir() {
					pluginPath := filepath.Join(pluginsPath, pluginDir.Name(), versionDir.Name())
					err := pluginInstaller.InstallPluginFromDirectory(context.Background(), pluginPath, builtin)
					if err != nil {
						utils.Logger.Errorf("安装插件%s失败：%s", pluginPath, err)
					}
				}
			}
		}
	}

}
