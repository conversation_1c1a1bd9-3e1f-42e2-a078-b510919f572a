package http

import (
	"resflow/configs"
	"resflow/database"
	"resflow/internal/authentication/application"
	authenticationinfra "resflow/internal/authentication/infrastructure"
	"resflow/internal/plugin/infrastructure"
	pluginquery "resflow/internal/plugin/query"
	userinfra "resflow/internal/user/infrastructure"
	"resflow/internal/user/query"
	"time"

	"github.com/gofiber/fiber/v2"
)

type Handlers []Handler

type Handler interface {
	RegisterRoutes(app fiber.Router)
}

func NewHandlers() Handlers {
	return []Handler{
		NewLoginHandler(application.NewLoginService(userinfra.NewEntUserStore(database.Client), authenticationinfra.NewJWTTokenService(configs.ParsedConfig.JwtSecret, time.Hour*2, time.Hour*24*7, configs.ParsedConfig.AppConfig.Name))),
		NewUserHandler(query.NewUserQueryService(userinfra.NewEntUserStore(database.Client))),
		NewPluginHandler(pluginquery.NewPluginQueryService(infrastructure.NewPluginStore(database.Client))),
	}
}

func (h Handlers) RegisterRoutes(app *fiber.App) {
	for _, handler := range h {
		handler.RegisterRoutes(app)
	}
}
