package http

import (
	"resflow/internal/authentication/application"
	"slices"

	"github.com/gofiber/fiber/v2"
)

var whiteList = []string{
	"/api/login",
	"/api/token/refresh",
}

const CurrentUserKey = "current-user-id"

func NewAuthMiddleware(authenticationService *application.AuthenticationService) fiber.Handler {
	return func(c *fiber.Ctx) error {
		if slices.Contains(whiteList, c.Path()) {
			return c.Next()
		}
		authorizationHeader := c.Get("Authorization")
		if authorizationHeader == "" {
			return NewUnauthorizedError()
		}

		tokenString := authorizationHeader[len("Bearer "):]
		if tokenString == "" {
			return NewUnauthorizedError()
		}

		userId, err := authenticationService.GetUserIdByToken(c.Context(), tokenString)
		if err != nil {
			return NewUnauthorizedError()
		}

		c.Locals(CurrentUserKey, userId)

		return c.Next()
	}
}
