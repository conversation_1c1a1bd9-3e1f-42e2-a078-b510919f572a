package http

import (
	"resflow/internal/plugin/query"

	"github.com/gofiber/fiber/v2"
)

type PluginHandler struct {
	queryService *query.PluginQueryService
}

func NewPluginHandler(queryService *query.PluginQueryService) *PluginHandler {
	return &PluginHandler{
		queryService: queryService,
	}
}

func (handler *PluginHandler) RegisterRoutes(router fiber.Router) {
	router.Get("/plugins", handler.List)
}

func (handler *PluginHandler) List(c *fiber.Ctx) error {
	list, err := handler.queryService.List(c.Context())
	if err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, err.Error())
	}
	return c.Status(fiber.StatusOK).JSON(list)
}
