package http

import (
	"net/http"
	"resflow/internal/common/errors"
)

type Error struct {
	Code    string `json:"code"`
	Message string `json:"msg"`
	Status  int    `json:"status"`
}

func NewError(status int, code string, msg string) *Error {
	return &Error{Status: status, Code: code, Message: msg}
}

func NewErrorFromAppError(appErr *errors.AppError) *Error {
	if appErr.Type == errors.ErrTypeUser {
		return &Error{Status: http.StatusBadRequest, Code: appErr.Code, Message: appErr.Message}
	}
	return &Error{Status: http.StatusInternalServerError, Code: appErr.Code, Message: appErr.Message}
}

func NewValidationError(msg string) *Error {
	return &Error{Status: 419, Code: "VALIDATION_ERROR", Message: msg}
}

func NewArgumentError(msg string) *Error {
	return &Error{Status: http.StatusBadRequest, Code: "ARGUMENT_ERROR", Message: msg}
}

func NewUnauthorizedError() *Error {
	return &Error{Status: http.StatusUnauthorized, Code: "UNAUTHORIZED", Message: "未登录"}
}

func (e *Error) Error() string {
	return e.Message
}
