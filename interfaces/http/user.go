package http

import (
	"resflow/internal/user/query"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

type UserHandler struct {
	userQueryService *query.UserQueryService
}

func NewUserHandler(userQueryService *query.UserQueryService) *UserHandler {
	return &UserHandler{
		userQueryService: userQueryService,
	}
}

func (handler *UserHandler) RegisterRoutes(router fiber.Router) {
	userApi := router.Group("/user")
	userApi.Get("/current", handler.CurrentUser)
}

func (handler *UserHandler) CurrentUser(c *fiber.Ctx) error {
	userId := c.Locals(CurrentUserKey).(uuid.UUID)
	user, err := handler.userQueryService.GetUser(c.Context(), userId)
	if err != nil {
		return NewErrorFromAppError(err)
	}
	return c.JSON(NewResponse("ok", user))
}
