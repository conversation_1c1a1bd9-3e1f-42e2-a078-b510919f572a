package http

import (
	"net"
	"resflow/configs"
	"resflow/internal/authentication/application"
	"resflow/internal/authentication/infrastructure"
	"time"

	"github.com/bytedance/sonic"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/compress"
	"github.com/pkg/errors"
)

type FiberServer struct {
	app *fiber.App
}

func NewFiberServer(appName string) *FiberServer {
	app := fiber.New(fiber.Config{
		AppName:               appName,
		DisableStartupMessage: !configs.ParsedConfig.Debug,
		CaseSensitive:         true,
		ErrorHand<PERSON>:          default<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
		JSONEncoder:           sonic.Marshal,
		JSONDecoder:           sonic.Unmarshal,
	})

	app.Use(compress.New())
	app.Use(NewAuthMiddleware(application.NewAuthenticationService(infrastructure.NewJWTTokenService(configs.ParsedConfig.JwtSecret, time.Hour*2, time.Hour*24*7, configs.ParsedConfig.AppConfig.Name))))

	return &FiberServer{
		app: app,
	}
}

func (s *FiberServer) RegisterRoutes(handlers Handlers) {
	api := s.app.Group("/api")
	for _, handler := range handlers {
		handler.RegisterRoutes(api)
	}
}

func (s *FiberServer) RegisterMiddlewares(handlers Handlers) {
	for _, handler := range handlers {
		s.app.Use(handler)
	}
}

func (s *FiberServer) Listen(listener net.Listener) error {
	return s.app.Listener(listener)
}

func (s *FiberServer) Shutdown() error {
	return s.app.Shutdown()
}

func defaultErrorHandler(ctx *fiber.Ctx, err error) error {
	var httpError *Error
	if errors.As(err, &httpError) {
		return ctx.Status(httpError.Status).JSON(fiber.Map{
			"code":    httpError.Code,
			"message": httpError.Message,
		})
	}
	var fiberError *fiber.Error
	if errors.As(err, &fiberError) {
		return ctx.Status(fiberError.Code).JSON(fiber.Map{
			"message": err.Error(),
		})
	}
	return ctx.Status(500).JSON(fiber.Map{
		"message": err.Error(),
	})
}
