package http

import (
	"resflow/common"
	"resflow/internal/authentication/application"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
)

type LoginHandler struct {
	loginService *application.LoginService
}

func NewLoginHandler(loginService *application.LoginService) *LoginHandler {
	return &LoginHandler{
		loginService: loginService,
	}
}

type LoginRequest struct {
	Username string `json:"username" form:"username" validate:"required" msg_required:"用户名不能为空"`
	Password string `json:"password" form:"password" validate:"required" msg_required:"密码不能为空"`
}

func (handler *LoginHandler) RegisterRoutes(app fiber.Router) {
	app.Post("/login", handler.Login)
	app.Post("/token/refresh", handler.Refresh)
}

func (handler *LoginHandler) Login(ctx *fiber.Ctx) error {
	request := new(LoginRequest)

	if err := ctx.BodyParser(request); err != nil {
		return NewArgumentError("参数错误")
	}

	validate, trans := common.GetValidate()

	if errs := validate.Struct(*request); errs != nil {
		return NewValidationError(common.GetValidationErrMsg(errs.(validator.ValidationErrors), trans, *request))
	}

	token, err := handler.loginService.Login(ctx.Context(), request.Username, request.Password)
	if err != nil {
		return NewErrorFromAppError(err)
	}

	return ctx.JSON(NewResponse("登录成功", token))
}

type RefreshRequest struct {
	RefreshToken string `json:"refresh_token" form:"refresh_token" validate:"required" msg_required:"refresh_token不能为空"`
}

func (handler *LoginHandler) Refresh(ctx *fiber.Ctx) error {
	request := new(RefreshRequest)

	if err := ctx.BodyParser(request); err != nil {
		return NewArgumentError("参数错误")
	}

	validate, trans := common.GetValidate()

	if errs := validate.Struct(*request); errs != nil {
		return NewValidationError(common.GetValidationErrMsg(errs.(validator.ValidationErrors), trans, *request))
	}

	token, err := handler.loginService.RefreshToken(ctx.Context(), request.RefreshToken)
	if err != nil {
		return NewErrorFromAppError(err)
	}

	return ctx.JSON(NewResponse("刷新成功", token))
}
