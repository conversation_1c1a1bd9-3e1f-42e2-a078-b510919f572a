// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: v1/auth_service.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthErrCode int32

const (
	AuthErrCode_OK           AuthErrCode = 0
	AuthErrCode_PASSWORD_ERR AuthErrCode = 1000
)

// Enum value maps for AuthErrCode.
var (
	AuthErrCode_name = map[int32]string{
		0:    "OK",
		1000: "PASSWORD_ERR",
	}
	AuthErrCode_value = map[string]int32{
		"OK":           0,
		"PASSWORD_ERR": 1000,
	}
)

func (x AuthErrCode) Enum() *AuthErrCode {
	p := new(AuthErrCode)
	*p = x
	return p
}

func (x AuthErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_auth_service_proto_enumTypes[0].Descriptor()
}

func (AuthErrCode) Type() protoreflect.EnumType {
	return &file_v1_auth_service_proto_enumTypes[0]
}

func (x AuthErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthErrCode.Descriptor instead.
func (AuthErrCode) EnumDescriptor() ([]byte, []int) {
	return file_v1_auth_service_proto_rawDescGZIP(), []int{0}
}

type LoginRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginRequest) Reset() {
	*x = LoginRequest{}
	mi := &file_v1_auth_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginRequest) ProtoMessage() {}

func (x *LoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_auth_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginRequest.ProtoReflect.Descriptor instead.
func (*LoginRequest) Descriptor() ([]byte, []int) {
	return file_v1_auth_service_proto_rawDescGZIP(), []int{0}
}

func (x *LoginRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *LoginRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type LoginResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *User                  `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginResponse) Reset() {
	*x = LoginResponse{}
	mi := &file_v1_auth_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginResponse) ProtoMessage() {}

func (x *LoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_auth_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginResponse.ProtoReflect.Descriptor instead.
func (*LoginResponse) Descriptor() ([]byte, []int) {
	return file_v1_auth_service_proto_rawDescGZIP(), []int{1}
}

func (x *LoginResponse) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *LoginResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

var File_v1_auth_service_proto protoreflect.FileDescriptor

const file_v1_auth_service_proto_rawDesc = "" +
	"\n" +
	"\x15v1/auth_service.proto\x12\x02v1\x1a\x15v1/user_service.proto\"F\n" +
	"\fLoginRequest\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\"C\n" +
	"\rLoginResponse\x12\x1c\n" +
	"\x04user\x18\x01 \x01(\v2\b.v1.UserR\x04user\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token*(\n" +
	"\vAuthErrCode\x12\x06\n" +
	"\x02OK\x10\x00\x12\x11\n" +
	"\fPASSWORD_ERR\x10\xe8\a2;\n" +
	"\vAuthService\x12,\n" +
	"\x05Login\x12\x10.v1.LoginRequest\x1a\x11.v1.LoginResponseB\x1fZ\x1dresflow/proto/generated_go/v1b\x06proto3"

var (
	file_v1_auth_service_proto_rawDescOnce sync.Once
	file_v1_auth_service_proto_rawDescData []byte
)

func file_v1_auth_service_proto_rawDescGZIP() []byte {
	file_v1_auth_service_proto_rawDescOnce.Do(func() {
		file_v1_auth_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_v1_auth_service_proto_rawDesc), len(file_v1_auth_service_proto_rawDesc)))
	})
	return file_v1_auth_service_proto_rawDescData
}

var file_v1_auth_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_v1_auth_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_v1_auth_service_proto_goTypes = []any{
	(AuthErrCode)(0),      // 0: v1.AuthErrCode
	(*LoginRequest)(nil),  // 1: v1.LoginRequest
	(*LoginResponse)(nil), // 2: v1.LoginResponse
	(*User)(nil),          // 3: v1.User
}
var file_v1_auth_service_proto_depIdxs = []int32{
	3, // 0: v1.LoginResponse.user:type_name -> v1.User
	1, // 1: v1.AuthService.Login:input_type -> v1.LoginRequest
	2, // 2: v1.AuthService.Login:output_type -> v1.LoginResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_v1_auth_service_proto_init() }
func file_v1_auth_service_proto_init() {
	if File_v1_auth_service_proto != nil {
		return
	}
	file_v1_user_service_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_v1_auth_service_proto_rawDesc), len(file_v1_auth_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_v1_auth_service_proto_goTypes,
		DependencyIndexes: file_v1_auth_service_proto_depIdxs,
		EnumInfos:         file_v1_auth_service_proto_enumTypes,
		MessageInfos:      file_v1_auth_service_proto_msgTypes,
	}.Build()
	File_v1_auth_service_proto = out.File
	file_v1_auth_service_proto_goTypes = nil
	file_v1_auth_service_proto_depIdxs = nil
}
