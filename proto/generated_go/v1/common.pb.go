// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: v1/common.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Status int32

const (
	Status_INACTIVE Status = 0
	Status_ACTIVE   Status = 1
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "INACTIVE",
		1: "ACTIVE",
	}
	Status_value = map[string]int32{
		"INACTIVE": 0,
		"ACTIVE":   1,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_common_proto_enumTypes[0].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_v1_common_proto_enumTypes[0]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_v1_common_proto_rawDescGZIP(), []int{0}
}

type CommonResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommonResponse) Reset() {
	*x = CommonResponse{}
	mi := &file_v1_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponse) ProtoMessage() {}

func (x *CommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponse.ProtoReflect.Descriptor instead.
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return file_v1_common_proto_rawDescGZIP(), []int{0}
}

func (x *CommonResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type OnlyIdRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// @gotags: validate:"required" msg_required:"id不能为空"
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required" msg_required:"id不能为空"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OnlyIdRequest) Reset() {
	*x = OnlyIdRequest{}
	mi := &file_v1_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OnlyIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnlyIdRequest) ProtoMessage() {}

func (x *OnlyIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnlyIdRequest.ProtoReflect.Descriptor instead.
func (*OnlyIdRequest) Descriptor() ([]byte, []int) {
	return file_v1_common_proto_rawDescGZIP(), []int{1}
}

func (x *OnlyIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

var File_v1_common_proto protoreflect.FileDescriptor

const file_v1_common_proto_rawDesc = "" +
	"\n" +
	"\x0fv1/common.proto\x12\x02v1\">\n" +
	"\x0eCommonResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x1f\n" +
	"\rOnlyIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id*\"\n" +
	"\x06Status\x12\f\n" +
	"\bINACTIVE\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01B\x1fZ\x1dresflow/proto/generated_go/v1b\x06proto3"

var (
	file_v1_common_proto_rawDescOnce sync.Once
	file_v1_common_proto_rawDescData []byte
)

func file_v1_common_proto_rawDescGZIP() []byte {
	file_v1_common_proto_rawDescOnce.Do(func() {
		file_v1_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_v1_common_proto_rawDesc), len(file_v1_common_proto_rawDesc)))
	})
	return file_v1_common_proto_rawDescData
}

var file_v1_common_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_v1_common_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_v1_common_proto_goTypes = []any{
	(Status)(0),            // 0: v1.Status
	(*CommonResponse)(nil), // 1: v1.CommonResponse
	(*OnlyIdRequest)(nil),  // 2: v1.OnlyIdRequest
}
var file_v1_common_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_v1_common_proto_init() }
func file_v1_common_proto_init() {
	if File_v1_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_v1_common_proto_rawDesc), len(file_v1_common_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v1_common_proto_goTypes,
		DependencyIndexes: file_v1_common_proto_depIdxs,
		EnumInfos:         file_v1_common_proto_enumTypes,
		MessageInfos:      file_v1_common_proto_msgTypes,
	}.Build()
	File_v1_common_proto = out.File
	file_v1_common_proto_goTypes = nil
	file_v1_common_proto_depIdxs = nil
}
