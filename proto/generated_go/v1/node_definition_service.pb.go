// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: v1/node_definition_service.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NodeDefinition struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Author        string                 `protobuf:"bytes,5,opt,name=author,proto3" json:"author,omitempty"`
	Description   string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	Icon          string                 `protobuf:"bytes,7,opt,name=icon,proto3" json:"icon,omitempty"`
	Type          string                 `protobuf:"bytes,8,opt,name=type,proto3" json:"type,omitempty"`
	Version       string                 `protobuf:"bytes,9,opt,name=version,proto3" json:"version,omitempty"`
	Category      string                 `protobuf:"bytes,10,opt,name=category,proto3" json:"category,omitempty"`
	InputParams   []*NodeParam           `protobuf:"bytes,11,rep,name=input_params,json=inputParams,proto3" json:"input_params,omitempty"`
	OutputParams  []*NodeParam           `protobuf:"bytes,12,rep,name=output_params,json=outputParams,proto3" json:"output_params,omitempty"`
	InputPorts    []*NodePort            `protobuf:"bytes,13,rep,name=input_ports,json=inputPorts,proto3" json:"input_ports,omitempty"`
	OutputPorts   []*NodePort            `protobuf:"bytes,14,rep,name=output_ports,json=outputPorts,proto3" json:"output_ports,omitempty"`
	Exception     bool                   `protobuf:"varint,15,opt,name=exception,proto3" json:"exception,omitempty"`
	Builtin       bool                   `protobuf:"varint,16,opt,name=builtin,proto3" json:"builtin,omitempty"`
	Enabled       bool                   `protobuf:"varint,17,opt,name=enabled,proto3" json:"enabled,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeDefinition) Reset() {
	*x = NodeDefinition{}
	mi := &file_v1_node_definition_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeDefinition) ProtoMessage() {}

func (x *NodeDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_v1_node_definition_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeDefinition.ProtoReflect.Descriptor instead.
func (*NodeDefinition) Descriptor() ([]byte, []int) {
	return file_v1_node_definition_service_proto_rawDescGZIP(), []int{0}
}

func (x *NodeDefinition) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *NodeDefinition) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NodeDefinition) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *NodeDefinition) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *NodeDefinition) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *NodeDefinition) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *NodeDefinition) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *NodeDefinition) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *NodeDefinition) GetInputParams() []*NodeParam {
	if x != nil {
		return x.InputParams
	}
	return nil
}

func (x *NodeDefinition) GetOutputParams() []*NodeParam {
	if x != nil {
		return x.OutputParams
	}
	return nil
}

func (x *NodeDefinition) GetInputPorts() []*NodePort {
	if x != nil {
		return x.InputPorts
	}
	return nil
}

func (x *NodeDefinition) GetOutputPorts() []*NodePort {
	if x != nil {
		return x.OutputPorts
	}
	return nil
}

func (x *NodeDefinition) GetException() bool {
	if x != nil {
		return x.Exception
	}
	return false
}

func (x *NodeDefinition) GetBuiltin() bool {
	if x != nil {
		return x.Builtin
	}
	return false
}

func (x *NodeDefinition) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type ListNodeDefinitionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNodeDefinitionsRequest) Reset() {
	*x = ListNodeDefinitionsRequest{}
	mi := &file_v1_node_definition_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNodeDefinitionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodeDefinitionsRequest) ProtoMessage() {}

func (x *ListNodeDefinitionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_node_definition_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodeDefinitionsRequest.ProtoReflect.Descriptor instead.
func (*ListNodeDefinitionsRequest) Descriptor() ([]byte, []int) {
	return file_v1_node_definition_service_proto_rawDescGZIP(), []int{1}
}

type ListNodeDefinitionsResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	NodeDefinitions []*NodeDefinition      `protobuf:"bytes,1,rep,name=node_definitions,json=nodeDefinitions,proto3" json:"node_definitions,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListNodeDefinitionsResponse) Reset() {
	*x = ListNodeDefinitionsResponse{}
	mi := &file_v1_node_definition_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNodeDefinitionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodeDefinitionsResponse) ProtoMessage() {}

func (x *ListNodeDefinitionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_node_definition_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodeDefinitionsResponse.ProtoReflect.Descriptor instead.
func (*ListNodeDefinitionsResponse) Descriptor() ([]byte, []int) {
	return file_v1_node_definition_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListNodeDefinitionsResponse) GetNodeDefinitions() []*NodeDefinition {
	if x != nil {
		return x.NodeDefinitions
	}
	return nil
}

type GetNodeDefinitionByTypeAndVersionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// @gotags: validate:"required" msg_required:"节点类型不能为空"
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty" validate:"required" msg_required:"节点类型不能为空"`
	// @gotags: validate:"required" msg_required:"节点版本不能为空"
	Version       string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty" validate:"required" msg_required:"节点版本不能为空"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNodeDefinitionByTypeAndVersionRequest) Reset() {
	*x = GetNodeDefinitionByTypeAndVersionRequest{}
	mi := &file_v1_node_definition_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNodeDefinitionByTypeAndVersionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeDefinitionByTypeAndVersionRequest) ProtoMessage() {}

func (x *GetNodeDefinitionByTypeAndVersionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_node_definition_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeDefinitionByTypeAndVersionRequest.ProtoReflect.Descriptor instead.
func (*GetNodeDefinitionByTypeAndVersionRequest) Descriptor() ([]byte, []int) {
	return file_v1_node_definition_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetNodeDefinitionByTypeAndVersionRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetNodeDefinitionByTypeAndVersionRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type GetNodeDefinitionByTypeAndVersionResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	NodeDefinition *NodeDefinition        `protobuf:"bytes,1,opt,name=node_definition,json=nodeDefinition,proto3" json:"node_definition,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetNodeDefinitionByTypeAndVersionResponse) Reset() {
	*x = GetNodeDefinitionByTypeAndVersionResponse{}
	mi := &file_v1_node_definition_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNodeDefinitionByTypeAndVersionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeDefinitionByTypeAndVersionResponse) ProtoMessage() {}

func (x *GetNodeDefinitionByTypeAndVersionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_node_definition_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeDefinitionByTypeAndVersionResponse.ProtoReflect.Descriptor instead.
func (*GetNodeDefinitionByTypeAndVersionResponse) Descriptor() ([]byte, []int) {
	return file_v1_node_definition_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetNodeDefinitionByTypeAndVersionResponse) GetNodeDefinition() *NodeDefinition {
	if x != nil {
		return x.NodeDefinition
	}
	return nil
}

var File_v1_node_definition_service_proto protoreflect.FileDescriptor

const file_v1_node_definition_service_proto_rawDesc = "" +
	"\n" +
	" v1/node_definition_service.proto\x12\x02v1\x1a\rv1/node.proto\"\xe4\x03\n" +
	"\x0eNodeDefinition\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x16\n" +
	"\x06author\x18\x05 \x01(\tR\x06author\x12 \n" +
	"\vdescription\x18\x06 \x01(\tR\vdescription\x12\x12\n" +
	"\x04icon\x18\a \x01(\tR\x04icon\x12\x12\n" +
	"\x04type\x18\b \x01(\tR\x04type\x12\x18\n" +
	"\aversion\x18\t \x01(\tR\aversion\x12\x1a\n" +
	"\bcategory\x18\n" +
	" \x01(\tR\bcategory\x120\n" +
	"\finput_params\x18\v \x03(\v2\r.v1.NodeParamR\vinputParams\x122\n" +
	"\routput_params\x18\f \x03(\v2\r.v1.NodeParamR\foutputParams\x12-\n" +
	"\vinput_ports\x18\r \x03(\v2\f.v1.NodePortR\n" +
	"inputPorts\x12/\n" +
	"\foutput_ports\x18\x0e \x03(\v2\f.v1.NodePortR\voutputPorts\x12\x1c\n" +
	"\texception\x18\x0f \x01(\bR\texception\x12\x18\n" +
	"\abuiltin\x18\x10 \x01(\bR\abuiltin\x12\x18\n" +
	"\aenabled\x18\x11 \x01(\bR\aenabled\"\x1c\n" +
	"\x1aListNodeDefinitionsRequest\"\\\n" +
	"\x1bListNodeDefinitionsResponse\x12=\n" +
	"\x10node_definitions\x18\x01 \x03(\v2\x12.v1.NodeDefinitionR\x0fnodeDefinitions\"X\n" +
	"(GetNodeDefinitionByTypeAndVersionRequest\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\"h\n" +
	")GetNodeDefinitionByTypeAndVersionResponse\x12;\n" +
	"\x0fnode_definition\x18\x01 \x01(\v2\x12.v1.NodeDefinitionR\x0enodeDefinition2\xd4\x01\n" +
	"\x15NodeDefinitionService\x12G\n" +
	"\x04List\x12\x1e.v1.ListNodeDefinitionsRequest\x1a\x1f.v1.ListNodeDefinitionsResponse\x12r\n" +
	"\x13GetByTypeAndVersion\x12,.v1.GetNodeDefinitionByTypeAndVersionRequest\x1a-.v1.GetNodeDefinitionByTypeAndVersionResponseB\x1fZ\x1dresflow/proto/generated_go/v1b\x06proto3"

var (
	file_v1_node_definition_service_proto_rawDescOnce sync.Once
	file_v1_node_definition_service_proto_rawDescData []byte
)

func file_v1_node_definition_service_proto_rawDescGZIP() []byte {
	file_v1_node_definition_service_proto_rawDescOnce.Do(func() {
		file_v1_node_definition_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_v1_node_definition_service_proto_rawDesc), len(file_v1_node_definition_service_proto_rawDesc)))
	})
	return file_v1_node_definition_service_proto_rawDescData
}

var file_v1_node_definition_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_v1_node_definition_service_proto_goTypes = []any{
	(*NodeDefinition)(nil),                            // 0: v1.NodeDefinition
	(*ListNodeDefinitionsRequest)(nil),                // 1: v1.ListNodeDefinitionsRequest
	(*ListNodeDefinitionsResponse)(nil),               // 2: v1.ListNodeDefinitionsResponse
	(*GetNodeDefinitionByTypeAndVersionRequest)(nil),  // 3: v1.GetNodeDefinitionByTypeAndVersionRequest
	(*GetNodeDefinitionByTypeAndVersionResponse)(nil), // 4: v1.GetNodeDefinitionByTypeAndVersionResponse
	(*NodeParam)(nil),                                 // 5: v1.NodeParam
	(*NodePort)(nil),                                  // 6: v1.NodePort
}
var file_v1_node_definition_service_proto_depIdxs = []int32{
	5, // 0: v1.NodeDefinition.input_params:type_name -> v1.NodeParam
	5, // 1: v1.NodeDefinition.output_params:type_name -> v1.NodeParam
	6, // 2: v1.NodeDefinition.input_ports:type_name -> v1.NodePort
	6, // 3: v1.NodeDefinition.output_ports:type_name -> v1.NodePort
	0, // 4: v1.ListNodeDefinitionsResponse.node_definitions:type_name -> v1.NodeDefinition
	0, // 5: v1.GetNodeDefinitionByTypeAndVersionResponse.node_definition:type_name -> v1.NodeDefinition
	1, // 6: v1.NodeDefinitionService.List:input_type -> v1.ListNodeDefinitionsRequest
	3, // 7: v1.NodeDefinitionService.GetByTypeAndVersion:input_type -> v1.GetNodeDefinitionByTypeAndVersionRequest
	2, // 8: v1.NodeDefinitionService.List:output_type -> v1.ListNodeDefinitionsResponse
	4, // 9: v1.NodeDefinitionService.GetByTypeAndVersion:output_type -> v1.GetNodeDefinitionByTypeAndVersionResponse
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_v1_node_definition_service_proto_init() }
func file_v1_node_definition_service_proto_init() {
	if File_v1_node_definition_service_proto != nil {
		return
	}
	file_v1_node_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_v1_node_definition_service_proto_rawDesc), len(file_v1_node_definition_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_v1_node_definition_service_proto_goTypes,
		DependencyIndexes: file_v1_node_definition_service_proto_depIdxs,
		MessageInfos:      file_v1_node_definition_service_proto_msgTypes,
	}.Build()
	File_v1_node_definition_service_proto = out.File
	file_v1_node_definition_service_proto_goTypes = nil
	file_v1_node_definition_service_proto_depIdxs = nil
}
