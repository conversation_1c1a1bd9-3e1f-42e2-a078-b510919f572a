// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: v1/workflow_service.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	WorkflowService_GetById_FullMethodName    = "/v1.WorkflowService/GetById"
	WorkflowService_Create_FullMethodName     = "/v1.WorkflowService/Create"
	WorkflowService_Update_FullMethodName     = "/v1.WorkflowService/Update"
	WorkflowService_DeleteById_FullMethodName = "/v1.WorkflowService/DeleteById"
	WorkflowService_List_FullMethodName       = "/v1.WorkflowService/List"
)

// WorkflowServiceClient is the client API for WorkflowService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WorkflowServiceClient interface {
	GetById(ctx context.Context, in *OnlyIdRequest, opts ...grpc.CallOption) (*WorkflowResponse, error)
	Create(ctx context.Context, in *CreateWorkflowRequest, opts ...grpc.CallOption) (*WorkflowResponse, error)
	Update(ctx context.Context, in *UpdateWorkflowRequest, opts ...grpc.CallOption) (*WorkflowResponse, error)
	DeleteById(ctx context.Context, in *OnlyIdRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	List(ctx context.Context, in *ListWorkflowsRequest, opts ...grpc.CallOption) (*ListWorkflowsResponse, error)
}

type workflowServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWorkflowServiceClient(cc grpc.ClientConnInterface) WorkflowServiceClient {
	return &workflowServiceClient{cc}
}

func (c *workflowServiceClient) GetById(ctx context.Context, in *OnlyIdRequest, opts ...grpc.CallOption) (*WorkflowResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkflowResponse)
	err := c.cc.Invoke(ctx, WorkflowService_GetById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) Create(ctx context.Context, in *CreateWorkflowRequest, opts ...grpc.CallOption) (*WorkflowResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkflowResponse)
	err := c.cc.Invoke(ctx, WorkflowService_Create_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) Update(ctx context.Context, in *UpdateWorkflowRequest, opts ...grpc.CallOption) (*WorkflowResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WorkflowResponse)
	err := c.cc.Invoke(ctx, WorkflowService_Update_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) DeleteById(ctx context.Context, in *OnlyIdRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, WorkflowService_DeleteById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) List(ctx context.Context, in *ListWorkflowsRequest, opts ...grpc.CallOption) (*ListWorkflowsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListWorkflowsResponse)
	err := c.cc.Invoke(ctx, WorkflowService_List_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WorkflowServiceServer is the server API for WorkflowService service.
// All implementations must embed UnimplementedWorkflowServiceServer
// for forward compatibility.
type WorkflowServiceServer interface {
	GetById(context.Context, *OnlyIdRequest) (*WorkflowResponse, error)
	Create(context.Context, *CreateWorkflowRequest) (*WorkflowResponse, error)
	Update(context.Context, *UpdateWorkflowRequest) (*WorkflowResponse, error)
	DeleteById(context.Context, *OnlyIdRequest) (*CommonResponse, error)
	List(context.Context, *ListWorkflowsRequest) (*ListWorkflowsResponse, error)
	mustEmbedUnimplementedWorkflowServiceServer()
}

// UnimplementedWorkflowServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedWorkflowServiceServer struct{}

func (UnimplementedWorkflowServiceServer) GetById(context.Context, *OnlyIdRequest) (*WorkflowResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetById not implemented")
}
func (UnimplementedWorkflowServiceServer) Create(context.Context, *CreateWorkflowRequest) (*WorkflowResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedWorkflowServiceServer) Update(context.Context, *UpdateWorkflowRequest) (*WorkflowResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedWorkflowServiceServer) DeleteById(context.Context, *OnlyIdRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteById not implemented")
}
func (UnimplementedWorkflowServiceServer) List(context.Context, *ListWorkflowsRequest) (*ListWorkflowsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedWorkflowServiceServer) mustEmbedUnimplementedWorkflowServiceServer() {}
func (UnimplementedWorkflowServiceServer) testEmbeddedByValue()                         {}

// UnsafeWorkflowServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WorkflowServiceServer will
// result in compilation errors.
type UnsafeWorkflowServiceServer interface {
	mustEmbedUnimplementedWorkflowServiceServer()
}

func RegisterWorkflowServiceServer(s grpc.ServiceRegistrar, srv WorkflowServiceServer) {
	// If the following call pancis, it indicates UnimplementedWorkflowServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&WorkflowService_ServiceDesc, srv)
}

func _WorkflowService_GetById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OnlyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_GetById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetById(ctx, req.(*OnlyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkflowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_Create_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).Create(ctx, req.(*CreateWorkflowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWorkflowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_Update_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).Update(ctx, req.(*UpdateWorkflowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_DeleteById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OnlyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).DeleteById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_DeleteById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).DeleteById(ctx, req.(*OnlyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkflowsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).List(ctx, req.(*ListWorkflowsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WorkflowService_ServiceDesc is the grpc.ServiceDesc for WorkflowService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WorkflowService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "v1.WorkflowService",
	HandlerType: (*WorkflowServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetById",
			Handler:    _WorkflowService_GetById_Handler,
		},
		{
			MethodName: "Create",
			Handler:    _WorkflowService_Create_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _WorkflowService_Update_Handler,
		},
		{
			MethodName: "DeleteById",
			Handler:    _WorkflowService_DeleteById_Handler,
		},
		{
			MethodName: "List",
			Handler:    _WorkflowService_List_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "v1/workflow_service.proto",
}
