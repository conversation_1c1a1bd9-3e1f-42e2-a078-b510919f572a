// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: v1/node.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NodeParam struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// @gotags: validate:"required" msg_required:"参数ID不能为空"
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required" msg_required:"参数ID不能为空"`
	// @gotags: validate:"required" msg_required:"参数类型不能为空"
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty" validate:"required" msg_required:"参数类型不能为空"`
	// @gotags: validate:"required" msg_required:"参数名称不能为空"
	Label string `protobuf:"bytes,3,opt,name=label,proto3" json:"label,omitempty" validate:"required" msg_required:"参数名称不能为空"`
	// @gotags: validate:"omitempty"
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty" validate:"omitempty"`
	// @gotags: validate:"omitempty"
	Value         string `protobuf:"bytes,5,opt,name=value,proto3" json:"value,omitempty" validate:"omitempty"`
	Required      bool   `protobuf:"varint,10,opt,name=required,proto3" json:"required,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeParam) Reset() {
	*x = NodeParam{}
	mi := &file_v1_node_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeParam) ProtoMessage() {}

func (x *NodeParam) ProtoReflect() protoreflect.Message {
	mi := &file_v1_node_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeParam.ProtoReflect.Descriptor instead.
func (*NodeParam) Descriptor() ([]byte, []int) {
	return file_v1_node_proto_rawDescGZIP(), []int{0}
}

func (x *NodeParam) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *NodeParam) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *NodeParam) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *NodeParam) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *NodeParam) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *NodeParam) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

type NodePort struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// @gotags: validate:"required" msg_required:"端口ID不能为空"
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required" msg_required:"端口ID不能为空"`
	Type          string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Position      string `protobuf:"bytes,3,opt,name=position,proto3" json:"position,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodePort) Reset() {
	*x = NodePort{}
	mi := &file_v1_node_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodePort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodePort) ProtoMessage() {}

func (x *NodePort) ProtoReflect() protoreflect.Message {
	mi := &file_v1_node_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodePort.ProtoReflect.Descriptor instead.
func (*NodePort) Descriptor() ([]byte, []int) {
	return file_v1_node_proto_rawDescGZIP(), []int{1}
}

func (x *NodePort) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *NodePort) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *NodePort) GetPosition() string {
	if x != nil {
		return x.Position
	}
	return ""
}

var File_v1_node_proto protoreflect.FileDescriptor

const file_v1_node_proto_rawDesc = "" +
	"\n" +
	"\rv1/node.proto\x12\x02v1\"\x99\x01\n" +
	"\tNodeParam\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12\x14\n" +
	"\x05label\x18\x03 \x01(\tR\x05label\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x14\n" +
	"\x05value\x18\x05 \x01(\tR\x05value\x12\x1a\n" +
	"\brequired\x18\n" +
	" \x01(\bR\brequired\"J\n" +
	"\bNodePort\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12\x1a\n" +
	"\bposition\x18\x03 \x01(\tR\bpositionB\x1fZ\x1dresflow/proto/generated_go/v1b\x06proto3"

var (
	file_v1_node_proto_rawDescOnce sync.Once
	file_v1_node_proto_rawDescData []byte
)

func file_v1_node_proto_rawDescGZIP() []byte {
	file_v1_node_proto_rawDescOnce.Do(func() {
		file_v1_node_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_v1_node_proto_rawDesc), len(file_v1_node_proto_rawDesc)))
	})
	return file_v1_node_proto_rawDescData
}

var file_v1_node_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_v1_node_proto_goTypes = []any{
	(*NodeParam)(nil), // 0: v1.NodeParam
	(*NodePort)(nil),  // 1: v1.NodePort
}
var file_v1_node_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_v1_node_proto_init() }
func file_v1_node_proto_init() {
	if File_v1_node_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_v1_node_proto_rawDesc), len(file_v1_node_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_v1_node_proto_goTypes,
		DependencyIndexes: file_v1_node_proto_depIdxs,
		MessageInfos:      file_v1_node_proto_msgTypes,
	}.Build()
	File_v1_node_proto = out.File
	file_v1_node_proto_goTypes = nil
	file_v1_node_proto_depIdxs = nil
}
