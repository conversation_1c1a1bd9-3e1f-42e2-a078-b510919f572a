// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: v1/workflow_service.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WorkflowState int32

const (
	WorkflowState_CLOSE WorkflowState = 0
	WorkflowState_OPEN  WorkflowState = 1
)

// Enum value maps for WorkflowState.
var (
	WorkflowState_name = map[int32]string{
		0: "CLOSE",
		1: "OPEN",
	}
	WorkflowState_value = map[string]int32{
		"CLOSE": 0,
		"OPEN":  1,
	}
)

func (x WorkflowState) Enum() *WorkflowState {
	p := new(WorkflowState)
	*p = x
	return p
}

func (x WorkflowState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkflowState) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_workflow_service_proto_enumTypes[0].Descriptor()
}

func (WorkflowState) Type() protoreflect.EnumType {
	return &file_v1_workflow_service_proto_enumTypes[0]
}

func (x WorkflowState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkflowState.Descriptor instead.
func (WorkflowState) EnumDescriptor() ([]byte, []int) {
	return file_v1_workflow_service_proto_rawDescGZIP(), []int{0}
}

type Workflow struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	User          *User                  `protobuf:"bytes,2,opt,name=user,proto3,oneof" json:"user,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Icon          string                 `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	Description   string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	Status        Status                 `protobuf:"varint,6,opt,name=status,proto3,enum=v1.Status" json:"status,omitempty"`
	Viewport      *WorkflowViewport      `protobuf:"bytes,7,opt,name=viewport,proto3" json:"viewport,omitempty"`
	Nodes         []*WorkflowNode        `protobuf:"bytes,8,rep,name=nodes,proto3" json:"nodes,omitempty"`
	Links         []*WorkflowLink        `protobuf:"bytes,9,rep,name=links,proto3" json:"links,omitempty"`
	CreatedAt     string                 `protobuf:"bytes,20,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     string                 `protobuf:"bytes,21,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Workflow) Reset() {
	*x = Workflow{}
	mi := &file_v1_workflow_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Workflow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Workflow) ProtoMessage() {}

func (x *Workflow) ProtoReflect() protoreflect.Message {
	mi := &file_v1_workflow_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Workflow.ProtoReflect.Descriptor instead.
func (*Workflow) Descriptor() ([]byte, []int) {
	return file_v1_workflow_service_proto_rawDescGZIP(), []int{0}
}

func (x *Workflow) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Workflow) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *Workflow) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Workflow) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *Workflow) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Workflow) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_INACTIVE
}

func (x *Workflow) GetViewport() *WorkflowViewport {
	if x != nil {
		return x.Viewport
	}
	return nil
}

func (x *Workflow) GetNodes() []*WorkflowNode {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *Workflow) GetLinks() []*WorkflowLink {
	if x != nil {
		return x.Links
	}
	return nil
}

func (x *Workflow) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *Workflow) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

type WorkflowViewport struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	X             float32                `protobuf:"fixed32,1,opt,name=x,proto3" json:"x,omitempty"`
	Y             float32                `protobuf:"fixed32,2,opt,name=y,proto3" json:"y,omitempty"`
	Zoom          float32                `protobuf:"fixed32,3,opt,name=zoom,proto3" json:"zoom,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkflowViewport) Reset() {
	*x = WorkflowViewport{}
	mi := &file_v1_workflow_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowViewport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowViewport) ProtoMessage() {}

func (x *WorkflowViewport) ProtoReflect() protoreflect.Message {
	mi := &file_v1_workflow_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowViewport.ProtoReflect.Descriptor instead.
func (*WorkflowViewport) Descriptor() ([]byte, []int) {
	return file_v1_workflow_service_proto_rawDescGZIP(), []int{1}
}

func (x *WorkflowViewport) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *WorkflowViewport) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *WorkflowViewport) GetZoom() float32 {
	if x != nil {
		return x.Zoom
	}
	return 0
}

type WorkflowNodePosition struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	X             float32                `protobuf:"fixed32,1,opt,name=x,proto3" json:"x,omitempty"`
	Y             float32                `protobuf:"fixed32,2,opt,name=y,proto3" json:"y,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkflowNodePosition) Reset() {
	*x = WorkflowNodePosition{}
	mi := &file_v1_workflow_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowNodePosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowNodePosition) ProtoMessage() {}

func (x *WorkflowNodePosition) ProtoReflect() protoreflect.Message {
	mi := &file_v1_workflow_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowNodePosition.ProtoReflect.Descriptor instead.
func (*WorkflowNodePosition) Descriptor() ([]byte, []int) {
	return file_v1_workflow_service_proto_rawDescGZIP(), []int{2}
}

func (x *WorkflowNodePosition) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *WorkflowNodePosition) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

type WorkflowNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Workflow      *Workflow              `protobuf:"bytes,2,opt,name=workflow,proto3,oneof" json:"workflow,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Icon          string                 `protobuf:"bytes,5,opt,name=icon,proto3" json:"icon,omitempty"`
	Type          string                 `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	Version       string                 `protobuf:"bytes,7,opt,name=version,proto3" json:"version,omitempty"`
	PluginName    string                 `protobuf:"bytes,8,opt,name=plugin_name,json=pluginName,proto3" json:"plugin_name,omitempty"`
	PluginVersion string                 `protobuf:"bytes,9,opt,name=plugin_version,json=pluginVersion,proto3" json:"plugin_version,omitempty"`
	InputParams   []*NodeParam           `protobuf:"bytes,15,rep,name=input_params,json=inputParams,proto3" json:"input_params,omitempty"`
	InputValues   *structpb.Struct       `protobuf:"bytes,16,opt,name=input_values,json=inputValues,proto3" json:"input_values,omitempty"`
	OutputParams  []*NodeParam           `protobuf:"bytes,17,rep,name=output_params,json=outputParams,proto3" json:"output_params,omitempty"`
	OutputValues  *structpb.Struct       `protobuf:"bytes,18,opt,name=output_values,json=outputValues,proto3" json:"output_values,omitempty"`
	InputPorts    []*NodePort            `protobuf:"bytes,19,rep,name=input_ports,json=inputPorts,proto3" json:"input_ports,omitempty"`
	OutputPorts   []*NodePort            `protobuf:"bytes,20,rep,name=output_ports,json=outputPorts,proto3" json:"output_ports,omitempty"`
	Position      *WorkflowNodePosition  `protobuf:"bytes,21,opt,name=position,proto3" json:"position,omitempty"`
	Data          *structpb.Struct       `protobuf:"bytes,22,opt,name=data,proto3" json:"data,omitempty"`
	CreatedAt     string                 `protobuf:"bytes,30,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     string                 `protobuf:"bytes,31,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkflowNode) Reset() {
	*x = WorkflowNode{}
	mi := &file_v1_workflow_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowNode) ProtoMessage() {}

func (x *WorkflowNode) ProtoReflect() protoreflect.Message {
	mi := &file_v1_workflow_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowNode.ProtoReflect.Descriptor instead.
func (*WorkflowNode) Descriptor() ([]byte, []int) {
	return file_v1_workflow_service_proto_rawDescGZIP(), []int{3}
}

func (x *WorkflowNode) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkflowNode) GetWorkflow() *Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

func (x *WorkflowNode) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WorkflowNode) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WorkflowNode) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *WorkflowNode) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *WorkflowNode) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *WorkflowNode) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *WorkflowNode) GetPluginVersion() string {
	if x != nil {
		return x.PluginVersion
	}
	return ""
}

func (x *WorkflowNode) GetInputParams() []*NodeParam {
	if x != nil {
		return x.InputParams
	}
	return nil
}

func (x *WorkflowNode) GetInputValues() *structpb.Struct {
	if x != nil {
		return x.InputValues
	}
	return nil
}

func (x *WorkflowNode) GetOutputParams() []*NodeParam {
	if x != nil {
		return x.OutputParams
	}
	return nil
}

func (x *WorkflowNode) GetOutputValues() *structpb.Struct {
	if x != nil {
		return x.OutputValues
	}
	return nil
}

func (x *WorkflowNode) GetInputPorts() []*NodePort {
	if x != nil {
		return x.InputPorts
	}
	return nil
}

func (x *WorkflowNode) GetOutputPorts() []*NodePort {
	if x != nil {
		return x.OutputPorts
	}
	return nil
}

func (x *WorkflowNode) GetPosition() *WorkflowNodePosition {
	if x != nil {
		return x.Position
	}
	return nil
}

func (x *WorkflowNode) GetData() *structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *WorkflowNode) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *WorkflowNode) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

type WorkflowLink struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Workflow      *Workflow              `protobuf:"bytes,2,opt,name=workflow,proto3,oneof" json:"workflow,omitempty"`
	FromNodeId    string                 `protobuf:"bytes,3,opt,name=from_node_id,json=fromNodeId,proto3" json:"from_node_id,omitempty"`
	ToNodeId      string                 `protobuf:"bytes,4,opt,name=to_node_id,json=toNodeId,proto3" json:"to_node_id,omitempty"`
	FromPortId    string                 `protobuf:"bytes,5,opt,name=from_port_id,json=fromPortId,proto3" json:"from_port_id,omitempty"`
	ToPortId      string                 `protobuf:"bytes,6,opt,name=to_port_id,json=toPortId,proto3" json:"to_port_id,omitempty"`
	Type          string                 `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkflowLink) Reset() {
	*x = WorkflowLink{}
	mi := &file_v1_workflow_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowLink) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowLink) ProtoMessage() {}

func (x *WorkflowLink) ProtoReflect() protoreflect.Message {
	mi := &file_v1_workflow_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowLink.ProtoReflect.Descriptor instead.
func (*WorkflowLink) Descriptor() ([]byte, []int) {
	return file_v1_workflow_service_proto_rawDescGZIP(), []int{4}
}

func (x *WorkflowLink) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkflowLink) GetWorkflow() *Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

func (x *WorkflowLink) GetFromNodeId() string {
	if x != nil {
		return x.FromNodeId
	}
	return ""
}

func (x *WorkflowLink) GetToNodeId() string {
	if x != nil {
		return x.ToNodeId
	}
	return ""
}

func (x *WorkflowLink) GetFromPortId() string {
	if x != nil {
		return x.FromPortId
	}
	return ""
}

func (x *WorkflowLink) GetToPortId() string {
	if x != nil {
		return x.ToPortId
	}
	return ""
}

func (x *WorkflowLink) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type WorkflowNodeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// @gotags: validate:"required,uuid4"
	Id string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty" validate:"required,uuid4"`
	// @gotags: validate:"required,max=50" msg_max:"节点名称不能超过50个字"
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty" validate:"required,max=50" msg_max:"节点名称不能超过50个字"`
	// @gotags: validate:"omitempty,max=200" msg_max:"节点描述不能超过200个字"
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty" validate:"omitempty,max=200" msg_max:"节点描述不能超过200个字"`
	// @gotags: validate:"omitempty"
	Icon string `protobuf:"bytes,5,opt,name=icon,proto3" json:"icon,omitempty" validate:"omitempty"`
	// @gotags: validate:"required" msg_required:"节点类型不能为空"
	Type string `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty" validate:"required" msg_required:"节点类型不能为空"`
	// @gotags: validate:"required" msg_required:"节点版本不能为空"
	Version string `protobuf:"bytes,7,opt,name=version,proto3" json:"version,omitempty" validate:"required" msg_required:"节点版本不能为空"`
	// @gotags: validate:"omitempty,required"
	PluginName *string `protobuf:"bytes,8,opt,name=plugin_name,json=pluginName,proto3,oneof" json:"plugin_name,omitempty" validate:"omitempty,required"`
	// @gotags: validate:"omitempty,required"
	PluginVersion *string `protobuf:"bytes,9,opt,name=plugin_version,json=pluginVersion,proto3,oneof" json:"plugin_version,omitempty" validate:"omitempty,required"`
	// @gotags: validate:"omitempty,dive,required"
	InputParams []*NodeParam `protobuf:"bytes,15,rep,name=input_params,json=inputParams,proto3" json:"input_params,omitempty" validate:"omitempty,dive,required"`
	// @gotags: validate:"omitempty,required"
	InputValues *structpb.Struct `protobuf:"bytes,16,opt,name=input_values,json=inputValues,proto3" json:"input_values,omitempty" validate:"omitempty,required"`
	// @gotags: validate:"omitempty,dive,required"
	OutputParams []*NodeParam `protobuf:"bytes,17,rep,name=output_params,json=outputParams,proto3" json:"output_params,omitempty" validate:"omitempty,dive,required"`
	// @gotags: validate:"omitempty,required"
	OutputValues *structpb.Struct `protobuf:"bytes,18,opt,name=output_values,json=outputValues,proto3" json:"output_values,omitempty" validate:"omitempty,required"`
	// @gotags: validate:"omitempty,dive,required"
	InputPorts []*NodePort `protobuf:"bytes,19,rep,name=input_ports,json=inputPorts,proto3" json:"input_ports,omitempty" validate:"omitempty,dive,required"`
	// @gotags: validate:"omitempty,dive,required"
	OutputPorts []*NodePort `protobuf:"bytes,20,rep,name=output_ports,json=outputPorts,proto3" json:"output_ports,omitempty" validate:"omitempty,dive,required"`
	// @gotags: validate:"required"
	Position *WorkflowNodePosition `protobuf:"bytes,21,opt,name=position,proto3" json:"position,omitempty" validate:"required"`
	// @gotags: validate:"required"
	Data          *structpb.Struct `protobuf:"bytes,22,opt,name=data,proto3" json:"data,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkflowNodeRequest) Reset() {
	*x = WorkflowNodeRequest{}
	mi := &file_v1_workflow_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowNodeRequest) ProtoMessage() {}

func (x *WorkflowNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_workflow_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowNodeRequest.ProtoReflect.Descriptor instead.
func (*WorkflowNodeRequest) Descriptor() ([]byte, []int) {
	return file_v1_workflow_service_proto_rawDescGZIP(), []int{5}
}

func (x *WorkflowNodeRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkflowNodeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WorkflowNodeRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WorkflowNodeRequest) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *WorkflowNodeRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *WorkflowNodeRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *WorkflowNodeRequest) GetPluginName() string {
	if x != nil && x.PluginName != nil {
		return *x.PluginName
	}
	return ""
}

func (x *WorkflowNodeRequest) GetPluginVersion() string {
	if x != nil && x.PluginVersion != nil {
		return *x.PluginVersion
	}
	return ""
}

func (x *WorkflowNodeRequest) GetInputParams() []*NodeParam {
	if x != nil {
		return x.InputParams
	}
	return nil
}

func (x *WorkflowNodeRequest) GetInputValues() *structpb.Struct {
	if x != nil {
		return x.InputValues
	}
	return nil
}

func (x *WorkflowNodeRequest) GetOutputParams() []*NodeParam {
	if x != nil {
		return x.OutputParams
	}
	return nil
}

func (x *WorkflowNodeRequest) GetOutputValues() *structpb.Struct {
	if x != nil {
		return x.OutputValues
	}
	return nil
}

func (x *WorkflowNodeRequest) GetInputPorts() []*NodePort {
	if x != nil {
		return x.InputPorts
	}
	return nil
}

func (x *WorkflowNodeRequest) GetOutputPorts() []*NodePort {
	if x != nil {
		return x.OutputPorts
	}
	return nil
}

func (x *WorkflowNodeRequest) GetPosition() *WorkflowNodePosition {
	if x != nil {
		return x.Position
	}
	return nil
}

func (x *WorkflowNodeRequest) GetData() *structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

type WorkflowLinkRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// @gotags: validate:"required,uuid4"
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" validate:"required,uuid4"`
	// @gotags: validate:"required,uuid4" msg_required:"来源节点不能为空" msg_uuid4:"来源节点id错误"
	FromNodeId string `protobuf:"bytes,3,opt,name=from_node_id,json=fromNodeId,proto3" json:"from_node_id,omitempty" validate:"required,uuid4" msg_required:"来源节点不能为空" msg_uuid4:"来源节点id错误"`
	// @gotags: validate:"required,uuid4" msg_required:"目标节点不能为空" msg_uuid4:"目标节点id错误"
	ToNodeId   string `protobuf:"bytes,4,opt,name=to_node_id,json=toNodeId,proto3" json:"to_node_id,omitempty" validate:"required,uuid4" msg_required:"目标节点不能为空" msg_uuid4:"目标节点id错误"`
	FromPortId string `protobuf:"bytes,5,opt,name=from_port_id,json=fromPortId,proto3" json:"from_port_id,omitempty"`
	ToPortId   string `protobuf:"bytes,6,opt,name=to_port_id,json=toPortId,proto3" json:"to_port_id,omitempty"`
	// @gotags: validate:"required" msg_required:"边类型错误"
	Type          string `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty" validate:"required" msg_required:"边类型错误"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkflowLinkRequest) Reset() {
	*x = WorkflowLinkRequest{}
	mi := &file_v1_workflow_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowLinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowLinkRequest) ProtoMessage() {}

func (x *WorkflowLinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_workflow_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowLinkRequest.ProtoReflect.Descriptor instead.
func (*WorkflowLinkRequest) Descriptor() ([]byte, []int) {
	return file_v1_workflow_service_proto_rawDescGZIP(), []int{6}
}

func (x *WorkflowLinkRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkflowLinkRequest) GetFromNodeId() string {
	if x != nil {
		return x.FromNodeId
	}
	return ""
}

func (x *WorkflowLinkRequest) GetToNodeId() string {
	if x != nil {
		return x.ToNodeId
	}
	return ""
}

func (x *WorkflowLinkRequest) GetFromPortId() string {
	if x != nil {
		return x.FromPortId
	}
	return ""
}

func (x *WorkflowLinkRequest) GetToPortId() string {
	if x != nil {
		return x.ToPortId
	}
	return ""
}

func (x *WorkflowLinkRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type CreateWorkflowRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// @gotags: validate:"required,max=50" label:"名称" msg_required:"请填写名称" msg_max:"名称不能超过50个字"
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty" validate:"required,max=50" label:"名称" msg_required:"请填写名称" msg_max:"名称不能超过50个字"`
	// @gotags: validate:"omitempty,max=200" msg_max:"描述不能超过200个字"
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty" validate:"omitempty,max=200" msg_max:"描述不能超过200个字"`
	// @gotags: validate:"omitempty"
	Icon string `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty" validate:"omitempty"`
	// @gotags: validate:"omitempty,dive,required"
	Nodes []*WorkflowNodeRequest `protobuf:"bytes,4,rep,name=nodes,proto3" json:"nodes,omitempty" validate:"omitempty,dive,required"`
	// @gotags: validate:"omitempty,dive,required"
	Links         []*WorkflowLinkRequest `protobuf:"bytes,5,rep,name=links,proto3" json:"links,omitempty" validate:"omitempty,dive,required"`
	Viewport      *WorkflowViewport      `protobuf:"bytes,6,opt,name=viewport,proto3" json:"viewport,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateWorkflowRequest) Reset() {
	*x = CreateWorkflowRequest{}
	mi := &file_v1_workflow_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWorkflowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkflowRequest) ProtoMessage() {}

func (x *CreateWorkflowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_workflow_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkflowRequest.ProtoReflect.Descriptor instead.
func (*CreateWorkflowRequest) Descriptor() ([]byte, []int) {
	return file_v1_workflow_service_proto_rawDescGZIP(), []int{7}
}

func (x *CreateWorkflowRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateWorkflowRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateWorkflowRequest) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *CreateWorkflowRequest) GetNodes() []*WorkflowNodeRequest {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *CreateWorkflowRequest) GetLinks() []*WorkflowLinkRequest {
	if x != nil {
		return x.Links
	}
	return nil
}

func (x *CreateWorkflowRequest) GetViewport() *WorkflowViewport {
	if x != nil {
		return x.Viewport
	}
	return nil
}

type WorkflowResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Workflow      *Workflow              `protobuf:"bytes,1,opt,name=workflow,proto3" json:"workflow,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkflowResponse) Reset() {
	*x = WorkflowResponse{}
	mi := &file_v1_workflow_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowResponse) ProtoMessage() {}

func (x *WorkflowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_workflow_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowResponse.ProtoReflect.Descriptor instead.
func (*WorkflowResponse) Descriptor() ([]byte, []int) {
	return file_v1_workflow_service_proto_rawDescGZIP(), []int{8}
}

func (x *WorkflowResponse) GetWorkflow() *Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

type UpdateWorkflowRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// @gotags: validate:"required,max=50" label:"名称" msg_required:"请填写名称" msg_max:"名称不能超过50个字"
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" validate:"required,max=50" label:"名称" msg_required:"请填写名称" msg_max:"名称不能超过50个字"`
	// @gotags: validate:"omitempty,dive,required"
	Nodes []*WorkflowNodeRequest `protobuf:"bytes,3,rep,name=nodes,proto3" json:"nodes,omitempty" validate:"omitempty,dive,required"`
	// @gotags: validate:"omitempty,dive,required"
	Links         []*WorkflowLinkRequest `protobuf:"bytes,4,rep,name=links,proto3" json:"links,omitempty" validate:"omitempty,dive,required"`
	Viewport      *WorkflowViewport      `protobuf:"bytes,5,opt,name=viewport,proto3" json:"viewport,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateWorkflowRequest) Reset() {
	*x = UpdateWorkflowRequest{}
	mi := &file_v1_workflow_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateWorkflowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkflowRequest) ProtoMessage() {}

func (x *UpdateWorkflowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_workflow_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkflowRequest.ProtoReflect.Descriptor instead.
func (*UpdateWorkflowRequest) Descriptor() ([]byte, []int) {
	return file_v1_workflow_service_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateWorkflowRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateWorkflowRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateWorkflowRequest) GetNodes() []*WorkflowNodeRequest {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *UpdateWorkflowRequest) GetLinks() []*WorkflowLinkRequest {
	if x != nil {
		return x.Links
	}
	return nil
}

func (x *UpdateWorkflowRequest) GetViewport() *WorkflowViewport {
	if x != nil {
		return x.Viewport
	}
	return nil
}

type ListWorkflowsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListWorkflowsRequest) Reset() {
	*x = ListWorkflowsRequest{}
	mi := &file_v1_workflow_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListWorkflowsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowsRequest) ProtoMessage() {}

func (x *ListWorkflowsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_workflow_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowsRequest.ProtoReflect.Descriptor instead.
func (*ListWorkflowsRequest) Descriptor() ([]byte, []int) {
	return file_v1_workflow_service_proto_rawDescGZIP(), []int{10}
}

type ListWorkflowsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Workflows     []*Workflow            `protobuf:"bytes,1,rep,name=workflows,proto3" json:"workflows,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListWorkflowsResponse) Reset() {
	*x = ListWorkflowsResponse{}
	mi := &file_v1_workflow_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListWorkflowsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowsResponse) ProtoMessage() {}

func (x *ListWorkflowsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_workflow_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowsResponse.ProtoReflect.Descriptor instead.
func (*ListWorkflowsResponse) Descriptor() ([]byte, []int) {
	return file_v1_workflow_service_proto_rawDescGZIP(), []int{11}
}

func (x *ListWorkflowsResponse) GetWorkflows() []*Workflow {
	if x != nil {
		return x.Workflows
	}
	return nil
}

var File_v1_workflow_service_proto protoreflect.FileDescriptor

const file_v1_workflow_service_proto_rawDesc = "" +
	"\n" +
	"\x19v1/workflow_service.proto\x12\x02v1\x1a\x1cgoogle/protobuf/struct.proto\x1a\x0fv1/common.proto\x1a\rv1/node.proto\x1a\x15v1/user_service.proto\"\xf4\x02\n" +
	"\bWorkflow\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12!\n" +
	"\x04user\x18\x02 \x01(\v2\b.v1.UserH\x00R\x04user\x88\x01\x01\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04icon\x18\x04 \x01(\tR\x04icon\x12 \n" +
	"\vdescription\x18\x05 \x01(\tR\vdescription\x12\"\n" +
	"\x06status\x18\x06 \x01(\x0e2\n" +
	".v1.StatusR\x06status\x120\n" +
	"\bviewport\x18\a \x01(\v2\x14.v1.WorkflowViewportR\bviewport\x12&\n" +
	"\x05nodes\x18\b \x03(\v2\x10.v1.WorkflowNodeR\x05nodes\x12&\n" +
	"\x05links\x18\t \x03(\v2\x10.v1.WorkflowLinkR\x05links\x12\x1d\n" +
	"\n" +
	"created_at\x18\x14 \x01(\tR\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\x15 \x01(\tR\tupdatedAtB\a\n" +
	"\x05_user\"B\n" +
	"\x10WorkflowViewport\x12\f\n" +
	"\x01x\x18\x01 \x01(\x02R\x01x\x12\f\n" +
	"\x01y\x18\x02 \x01(\x02R\x01y\x12\x12\n" +
	"\x04zoom\x18\x03 \x01(\x02R\x04zoom\"2\n" +
	"\x14WorkflowNodePosition\x12\f\n" +
	"\x01x\x18\x01 \x01(\x02R\x01x\x12\f\n" +
	"\x01y\x18\x02 \x01(\x02R\x01y\"\xfb\x05\n" +
	"\fWorkflowNode\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12-\n" +
	"\bworkflow\x18\x02 \x01(\v2\f.v1.WorkflowH\x00R\bworkflow\x88\x01\x01\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x12\n" +
	"\x04icon\x18\x05 \x01(\tR\x04icon\x12\x12\n" +
	"\x04type\x18\x06 \x01(\tR\x04type\x12\x18\n" +
	"\aversion\x18\a \x01(\tR\aversion\x12\x1f\n" +
	"\vplugin_name\x18\b \x01(\tR\n" +
	"pluginName\x12%\n" +
	"\x0eplugin_version\x18\t \x01(\tR\rpluginVersion\x120\n" +
	"\finput_params\x18\x0f \x03(\v2\r.v1.NodeParamR\vinputParams\x12:\n" +
	"\finput_values\x18\x10 \x01(\v2\x17.google.protobuf.StructR\vinputValues\x122\n" +
	"\routput_params\x18\x11 \x03(\v2\r.v1.NodeParamR\foutputParams\x12<\n" +
	"\routput_values\x18\x12 \x01(\v2\x17.google.protobuf.StructR\foutputValues\x12-\n" +
	"\vinput_ports\x18\x13 \x03(\v2\f.v1.NodePortR\n" +
	"inputPorts\x12/\n" +
	"\foutput_ports\x18\x14 \x03(\v2\f.v1.NodePortR\voutputPorts\x124\n" +
	"\bposition\x18\x15 \x01(\v2\x18.v1.WorkflowNodePositionR\bposition\x12+\n" +
	"\x04data\x18\x16 \x01(\v2\x17.google.protobuf.StructR\x04data\x12\x1d\n" +
	"\n" +
	"created_at\x18\x1e \x01(\tR\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\x1f \x01(\tR\tupdatedAtB\v\n" +
	"\t_workflow\"\xee\x01\n" +
	"\fWorkflowLink\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12-\n" +
	"\bworkflow\x18\x02 \x01(\v2\f.v1.WorkflowH\x00R\bworkflow\x88\x01\x01\x12 \n" +
	"\ffrom_node_id\x18\x03 \x01(\tR\n" +
	"fromNodeId\x12\x1c\n" +
	"\n" +
	"to_node_id\x18\x04 \x01(\tR\btoNodeId\x12 \n" +
	"\ffrom_port_id\x18\x05 \x01(\tR\n" +
	"fromPortId\x12\x1c\n" +
	"\n" +
	"to_port_id\x18\x06 \x01(\tR\btoPortId\x12\x12\n" +
	"\x04type\x18\a \x01(\tR\x04typeB\v\n" +
	"\t_workflow\"\xb5\x05\n" +
	"\x13WorkflowNodeRequest\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x12\n" +
	"\x04icon\x18\x05 \x01(\tR\x04icon\x12\x12\n" +
	"\x04type\x18\x06 \x01(\tR\x04type\x12\x18\n" +
	"\aversion\x18\a \x01(\tR\aversion\x12$\n" +
	"\vplugin_name\x18\b \x01(\tH\x00R\n" +
	"pluginName\x88\x01\x01\x12*\n" +
	"\x0eplugin_version\x18\t \x01(\tH\x01R\rpluginVersion\x88\x01\x01\x120\n" +
	"\finput_params\x18\x0f \x03(\v2\r.v1.NodeParamR\vinputParams\x12:\n" +
	"\finput_values\x18\x10 \x01(\v2\x17.google.protobuf.StructR\vinputValues\x122\n" +
	"\routput_params\x18\x11 \x03(\v2\r.v1.NodeParamR\foutputParams\x12<\n" +
	"\routput_values\x18\x12 \x01(\v2\x17.google.protobuf.StructR\foutputValues\x12-\n" +
	"\vinput_ports\x18\x13 \x03(\v2\f.v1.NodePortR\n" +
	"inputPorts\x12/\n" +
	"\foutput_ports\x18\x14 \x03(\v2\f.v1.NodePortR\voutputPorts\x124\n" +
	"\bposition\x18\x15 \x01(\v2\x18.v1.WorkflowNodePositionR\bposition\x12+\n" +
	"\x04data\x18\x16 \x01(\v2\x17.google.protobuf.StructR\x04dataB\x0e\n" +
	"\f_plugin_nameB\x11\n" +
	"\x0f_plugin_version\"\xb9\x01\n" +
	"\x13WorkflowLinkRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12 \n" +
	"\ffrom_node_id\x18\x03 \x01(\tR\n" +
	"fromNodeId\x12\x1c\n" +
	"\n" +
	"to_node_id\x18\x04 \x01(\tR\btoNodeId\x12 \n" +
	"\ffrom_port_id\x18\x05 \x01(\tR\n" +
	"fromPortId\x12\x1c\n" +
	"\n" +
	"to_port_id\x18\x06 \x01(\tR\btoPortId\x12\x12\n" +
	"\x04type\x18\a \x01(\tR\x04type\"\xf1\x01\n" +
	"\x15CreateWorkflowRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x12\n" +
	"\x04icon\x18\x03 \x01(\tR\x04icon\x12-\n" +
	"\x05nodes\x18\x04 \x03(\v2\x17.v1.WorkflowNodeRequestR\x05nodes\x12-\n" +
	"\x05links\x18\x05 \x03(\v2\x17.v1.WorkflowLinkRequestR\x05links\x120\n" +
	"\bviewport\x18\x06 \x01(\v2\x14.v1.WorkflowViewportR\bviewport\"<\n" +
	"\x10WorkflowResponse\x12(\n" +
	"\bworkflow\x18\x01 \x01(\v2\f.v1.WorkflowR\bworkflow\"\xcb\x01\n" +
	"\x15UpdateWorkflowRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12-\n" +
	"\x05nodes\x18\x03 \x03(\v2\x17.v1.WorkflowNodeRequestR\x05nodes\x12-\n" +
	"\x05links\x18\x04 \x03(\v2\x17.v1.WorkflowLinkRequestR\x05links\x120\n" +
	"\bviewport\x18\x05 \x01(\v2\x14.v1.WorkflowViewportR\bviewport\"\x16\n" +
	"\x14ListWorkflowsRequest\"C\n" +
	"\x15ListWorkflowsResponse\x12*\n" +
	"\tworkflows\x18\x01 \x03(\v2\f.v1.WorkflowR\tworkflows*$\n" +
	"\rWorkflowState\x12\t\n" +
	"\x05CLOSE\x10\x00\x12\b\n" +
	"\x04OPEN\x10\x012\xad\x02\n" +
	"\x0fWorkflowService\x122\n" +
	"\aGetById\x12\x11.v1.OnlyIdRequest\x1a\x14.v1.WorkflowResponse\x129\n" +
	"\x06Create\x12\x19.v1.CreateWorkflowRequest\x1a\x14.v1.WorkflowResponse\x129\n" +
	"\x06Update\x12\x19.v1.UpdateWorkflowRequest\x1a\x14.v1.WorkflowResponse\x123\n" +
	"\n" +
	"DeleteById\x12\x11.v1.OnlyIdRequest\x1a\x12.v1.CommonResponse\x12;\n" +
	"\x04List\x12\x18.v1.ListWorkflowsRequest\x1a\x19.v1.ListWorkflowsResponseB\x1fZ\x1dresflow/proto/generated_go/v1b\x06proto3"

var (
	file_v1_workflow_service_proto_rawDescOnce sync.Once
	file_v1_workflow_service_proto_rawDescData []byte
)

func file_v1_workflow_service_proto_rawDescGZIP() []byte {
	file_v1_workflow_service_proto_rawDescOnce.Do(func() {
		file_v1_workflow_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_v1_workflow_service_proto_rawDesc), len(file_v1_workflow_service_proto_rawDesc)))
	})
	return file_v1_workflow_service_proto_rawDescData
}

var file_v1_workflow_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_v1_workflow_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_v1_workflow_service_proto_goTypes = []any{
	(WorkflowState)(0),            // 0: v1.WorkflowState
	(*Workflow)(nil),              // 1: v1.Workflow
	(*WorkflowViewport)(nil),      // 2: v1.WorkflowViewport
	(*WorkflowNodePosition)(nil),  // 3: v1.WorkflowNodePosition
	(*WorkflowNode)(nil),          // 4: v1.WorkflowNode
	(*WorkflowLink)(nil),          // 5: v1.WorkflowLink
	(*WorkflowNodeRequest)(nil),   // 6: v1.WorkflowNodeRequest
	(*WorkflowLinkRequest)(nil),   // 7: v1.WorkflowLinkRequest
	(*CreateWorkflowRequest)(nil), // 8: v1.CreateWorkflowRequest
	(*WorkflowResponse)(nil),      // 9: v1.WorkflowResponse
	(*UpdateWorkflowRequest)(nil), // 10: v1.UpdateWorkflowRequest
	(*ListWorkflowsRequest)(nil),  // 11: v1.ListWorkflowsRequest
	(*ListWorkflowsResponse)(nil), // 12: v1.ListWorkflowsResponse
	(*User)(nil),                  // 13: v1.User
	(Status)(0),                   // 14: v1.Status
	(*NodeParam)(nil),             // 15: v1.NodeParam
	(*structpb.Struct)(nil),       // 16: google.protobuf.Struct
	(*NodePort)(nil),              // 17: v1.NodePort
	(*OnlyIdRequest)(nil),         // 18: v1.OnlyIdRequest
	(*CommonResponse)(nil),        // 19: v1.CommonResponse
}
var file_v1_workflow_service_proto_depIdxs = []int32{
	13, // 0: v1.Workflow.user:type_name -> v1.User
	14, // 1: v1.Workflow.status:type_name -> v1.Status
	2,  // 2: v1.Workflow.viewport:type_name -> v1.WorkflowViewport
	4,  // 3: v1.Workflow.nodes:type_name -> v1.WorkflowNode
	5,  // 4: v1.Workflow.links:type_name -> v1.WorkflowLink
	1,  // 5: v1.WorkflowNode.workflow:type_name -> v1.Workflow
	15, // 6: v1.WorkflowNode.input_params:type_name -> v1.NodeParam
	16, // 7: v1.WorkflowNode.input_values:type_name -> google.protobuf.Struct
	15, // 8: v1.WorkflowNode.output_params:type_name -> v1.NodeParam
	16, // 9: v1.WorkflowNode.output_values:type_name -> google.protobuf.Struct
	17, // 10: v1.WorkflowNode.input_ports:type_name -> v1.NodePort
	17, // 11: v1.WorkflowNode.output_ports:type_name -> v1.NodePort
	3,  // 12: v1.WorkflowNode.position:type_name -> v1.WorkflowNodePosition
	16, // 13: v1.WorkflowNode.data:type_name -> google.protobuf.Struct
	1,  // 14: v1.WorkflowLink.workflow:type_name -> v1.Workflow
	15, // 15: v1.WorkflowNodeRequest.input_params:type_name -> v1.NodeParam
	16, // 16: v1.WorkflowNodeRequest.input_values:type_name -> google.protobuf.Struct
	15, // 17: v1.WorkflowNodeRequest.output_params:type_name -> v1.NodeParam
	16, // 18: v1.WorkflowNodeRequest.output_values:type_name -> google.protobuf.Struct
	17, // 19: v1.WorkflowNodeRequest.input_ports:type_name -> v1.NodePort
	17, // 20: v1.WorkflowNodeRequest.output_ports:type_name -> v1.NodePort
	3,  // 21: v1.WorkflowNodeRequest.position:type_name -> v1.WorkflowNodePosition
	16, // 22: v1.WorkflowNodeRequest.data:type_name -> google.protobuf.Struct
	6,  // 23: v1.CreateWorkflowRequest.nodes:type_name -> v1.WorkflowNodeRequest
	7,  // 24: v1.CreateWorkflowRequest.links:type_name -> v1.WorkflowLinkRequest
	2,  // 25: v1.CreateWorkflowRequest.viewport:type_name -> v1.WorkflowViewport
	1,  // 26: v1.WorkflowResponse.workflow:type_name -> v1.Workflow
	6,  // 27: v1.UpdateWorkflowRequest.nodes:type_name -> v1.WorkflowNodeRequest
	7,  // 28: v1.UpdateWorkflowRequest.links:type_name -> v1.WorkflowLinkRequest
	2,  // 29: v1.UpdateWorkflowRequest.viewport:type_name -> v1.WorkflowViewport
	1,  // 30: v1.ListWorkflowsResponse.workflows:type_name -> v1.Workflow
	18, // 31: v1.WorkflowService.GetById:input_type -> v1.OnlyIdRequest
	8,  // 32: v1.WorkflowService.Create:input_type -> v1.CreateWorkflowRequest
	10, // 33: v1.WorkflowService.Update:input_type -> v1.UpdateWorkflowRequest
	18, // 34: v1.WorkflowService.DeleteById:input_type -> v1.OnlyIdRequest
	11, // 35: v1.WorkflowService.List:input_type -> v1.ListWorkflowsRequest
	9,  // 36: v1.WorkflowService.GetById:output_type -> v1.WorkflowResponse
	9,  // 37: v1.WorkflowService.Create:output_type -> v1.WorkflowResponse
	9,  // 38: v1.WorkflowService.Update:output_type -> v1.WorkflowResponse
	19, // 39: v1.WorkflowService.DeleteById:output_type -> v1.CommonResponse
	12, // 40: v1.WorkflowService.List:output_type -> v1.ListWorkflowsResponse
	36, // [36:41] is the sub-list for method output_type
	31, // [31:36] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_v1_workflow_service_proto_init() }
func file_v1_workflow_service_proto_init() {
	if File_v1_workflow_service_proto != nil {
		return
	}
	file_v1_common_proto_init()
	file_v1_node_proto_init()
	file_v1_user_service_proto_init()
	file_v1_workflow_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_v1_workflow_service_proto_msgTypes[3].OneofWrappers = []any{}
	file_v1_workflow_service_proto_msgTypes[4].OneofWrappers = []any{}
	file_v1_workflow_service_proto_msgTypes[5].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_v1_workflow_service_proto_rawDesc), len(file_v1_workflow_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_v1_workflow_service_proto_goTypes,
		DependencyIndexes: file_v1_workflow_service_proto_depIdxs,
		EnumInfos:         file_v1_workflow_service_proto_enumTypes,
		MessageInfos:      file_v1_workflow_service_proto_msgTypes,
	}.Build()
	File_v1_workflow_service_proto = out.File
	file_v1_workflow_service_proto_goTypes = nil
	file_v1_workflow_service_proto_depIdxs = nil
}
