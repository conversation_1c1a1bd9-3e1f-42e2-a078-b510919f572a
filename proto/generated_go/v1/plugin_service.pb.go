// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: v1/plugin_service.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Plugin struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Version       string                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	Author        string                 `protobuf:"bytes,4,opt,name=author,proto3" json:"author,omitempty"`
	DisplayName   string                 `protobuf:"bytes,5,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Description   string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	Icon          string                 `protobuf:"bytes,7,opt,name=icon,proto3" json:"icon,omitempty"`
	Path          string                 `protobuf:"bytes,8,opt,name=path,proto3" json:"path,omitempty"`
	Builtin       bool                   `protobuf:"varint,9,opt,name=builtin,proto3" json:"builtin,omitempty"`
	Enabled       bool                   `protobuf:"varint,10,opt,name=enabled,proto3" json:"enabled,omitempty"`
	CreatedAt     string                 `protobuf:"bytes,20,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     string                 `protobuf:"bytes,21,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Plugin) Reset() {
	*x = Plugin{}
	mi := &file_v1_plugin_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Plugin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Plugin) ProtoMessage() {}

func (x *Plugin) ProtoReflect() protoreflect.Message {
	mi := &file_v1_plugin_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Plugin.ProtoReflect.Descriptor instead.
func (*Plugin) Descriptor() ([]byte, []int) {
	return file_v1_plugin_service_proto_rawDescGZIP(), []int{0}
}

func (x *Plugin) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Plugin) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Plugin) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Plugin) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *Plugin) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Plugin) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Plugin) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *Plugin) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *Plugin) GetBuiltin() bool {
	if x != nil {
		return x.Builtin
	}
	return false
}

func (x *Plugin) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *Plugin) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *Plugin) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

type ListPluginsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPluginsRequest) Reset() {
	*x = ListPluginsRequest{}
	mi := &file_v1_plugin_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPluginsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPluginsRequest) ProtoMessage() {}

func (x *ListPluginsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_plugin_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPluginsRequest.ProtoReflect.Descriptor instead.
func (*ListPluginsRequest) Descriptor() ([]byte, []int) {
	return file_v1_plugin_service_proto_rawDescGZIP(), []int{1}
}

type ListPluginsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Plugins       []*Plugin              `protobuf:"bytes,1,rep,name=plugins,proto3" json:"plugins,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPluginsResponse) Reset() {
	*x = ListPluginsResponse{}
	mi := &file_v1_plugin_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPluginsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPluginsResponse) ProtoMessage() {}

func (x *ListPluginsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_v1_plugin_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPluginsResponse.ProtoReflect.Descriptor instead.
func (*ListPluginsResponse) Descriptor() ([]byte, []int) {
	return file_v1_plugin_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListPluginsResponse) GetPlugins() []*Plugin {
	if x != nil {
		return x.Plugins
	}
	return nil
}

var File_v1_plugin_service_proto protoreflect.FileDescriptor

const file_v1_plugin_service_proto_rawDesc = "" +
	"\n" +
	"\x17v1/plugin_service.proto\x12\x02v1\"\xbd\x02\n" +
	"\x06Plugin\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x03 \x01(\tR\aversion\x12\x16\n" +
	"\x06author\x18\x04 \x01(\tR\x06author\x12!\n" +
	"\fdisplay_name\x18\x05 \x01(\tR\vdisplayName\x12 \n" +
	"\vdescription\x18\x06 \x01(\tR\vdescription\x12\x12\n" +
	"\x04icon\x18\a \x01(\tR\x04icon\x12\x12\n" +
	"\x04path\x18\b \x01(\tR\x04path\x12\x18\n" +
	"\abuiltin\x18\t \x01(\bR\abuiltin\x12\x18\n" +
	"\aenabled\x18\n" +
	" \x01(\bR\aenabled\x12\x1d\n" +
	"\n" +
	"created_at\x18\x14 \x01(\tR\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\x15 \x01(\tR\tupdatedAt\"\x14\n" +
	"\x12ListPluginsRequest\";\n" +
	"\x13ListPluginsResponse\x12$\n" +
	"\aplugins\x18\x01 \x03(\v2\n" +
	".v1.PluginR\aplugins2H\n" +
	"\rPluginService\x127\n" +
	"\x04List\x12\x16.v1.ListPluginsRequest\x1a\x17.v1.ListPluginsResponseB\x1fZ\x1dresflow/proto/generated_go/v1b\x06proto3"

var (
	file_v1_plugin_service_proto_rawDescOnce sync.Once
	file_v1_plugin_service_proto_rawDescData []byte
)

func file_v1_plugin_service_proto_rawDescGZIP() []byte {
	file_v1_plugin_service_proto_rawDescOnce.Do(func() {
		file_v1_plugin_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_v1_plugin_service_proto_rawDesc), len(file_v1_plugin_service_proto_rawDesc)))
	})
	return file_v1_plugin_service_proto_rawDescData
}

var file_v1_plugin_service_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_v1_plugin_service_proto_goTypes = []any{
	(*Plugin)(nil),              // 0: v1.Plugin
	(*ListPluginsRequest)(nil),  // 1: v1.ListPluginsRequest
	(*ListPluginsResponse)(nil), // 2: v1.ListPluginsResponse
}
var file_v1_plugin_service_proto_depIdxs = []int32{
	0, // 0: v1.ListPluginsResponse.plugins:type_name -> v1.Plugin
	1, // 1: v1.PluginService.List:input_type -> v1.ListPluginsRequest
	2, // 2: v1.PluginService.List:output_type -> v1.ListPluginsResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_v1_plugin_service_proto_init() }
func file_v1_plugin_service_proto_init() {
	if File_v1_plugin_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_v1_plugin_service_proto_rawDesc), len(file_v1_plugin_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_v1_plugin_service_proto_goTypes,
		DependencyIndexes: file_v1_plugin_service_proto_depIdxs,
		MessageInfos:      file_v1_plugin_service_proto_msgTypes,
	}.Build()
	File_v1_plugin_service_proto = out.File
	file_v1_plugin_service_proto_goTypes = nil
	file_v1_plugin_service_proto_depIdxs = nil
}
