// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: v1/node_definition_service.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	NodeDefinitionService_List_FullMethodName                = "/v1.NodeDefinitionService/List"
	NodeDefinitionService_GetByTypeAndVersion_FullMethodName = "/v1.NodeDefinitionService/GetByTypeAndVersion"
)

// NodeDefinitionServiceClient is the client API for NodeDefinitionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NodeDefinitionServiceClient interface {
	List(ctx context.Context, in *ListNodeDefinitionsRequest, opts ...grpc.CallOption) (*ListNodeDefinitionsResponse, error)
	GetByTypeAndVersion(ctx context.Context, in *GetNodeDefinitionByTypeAndVersionRequest, opts ...grpc.CallOption) (*GetNodeDefinitionByTypeAndVersionResponse, error)
}

type nodeDefinitionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNodeDefinitionServiceClient(cc grpc.ClientConnInterface) NodeDefinitionServiceClient {
	return &nodeDefinitionServiceClient{cc}
}

func (c *nodeDefinitionServiceClient) List(ctx context.Context, in *ListNodeDefinitionsRequest, opts ...grpc.CallOption) (*ListNodeDefinitionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListNodeDefinitionsResponse)
	err := c.cc.Invoke(ctx, NodeDefinitionService_List_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeDefinitionServiceClient) GetByTypeAndVersion(ctx context.Context, in *GetNodeDefinitionByTypeAndVersionRequest, opts ...grpc.CallOption) (*GetNodeDefinitionByTypeAndVersionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetNodeDefinitionByTypeAndVersionResponse)
	err := c.cc.Invoke(ctx, NodeDefinitionService_GetByTypeAndVersion_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NodeDefinitionServiceServer is the server API for NodeDefinitionService service.
// All implementations must embed UnimplementedNodeDefinitionServiceServer
// for forward compatibility.
type NodeDefinitionServiceServer interface {
	List(context.Context, *ListNodeDefinitionsRequest) (*ListNodeDefinitionsResponse, error)
	GetByTypeAndVersion(context.Context, *GetNodeDefinitionByTypeAndVersionRequest) (*GetNodeDefinitionByTypeAndVersionResponse, error)
	mustEmbedUnimplementedNodeDefinitionServiceServer()
}

// UnimplementedNodeDefinitionServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedNodeDefinitionServiceServer struct{}

func (UnimplementedNodeDefinitionServiceServer) List(context.Context, *ListNodeDefinitionsRequest) (*ListNodeDefinitionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedNodeDefinitionServiceServer) GetByTypeAndVersion(context.Context, *GetNodeDefinitionByTypeAndVersionRequest) (*GetNodeDefinitionByTypeAndVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetByTypeAndVersion not implemented")
}
func (UnimplementedNodeDefinitionServiceServer) mustEmbedUnimplementedNodeDefinitionServiceServer() {}
func (UnimplementedNodeDefinitionServiceServer) testEmbeddedByValue()                               {}

// UnsafeNodeDefinitionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NodeDefinitionServiceServer will
// result in compilation errors.
type UnsafeNodeDefinitionServiceServer interface {
	mustEmbedUnimplementedNodeDefinitionServiceServer()
}

func RegisterNodeDefinitionServiceServer(s grpc.ServiceRegistrar, srv NodeDefinitionServiceServer) {
	// If the following call pancis, it indicates UnimplementedNodeDefinitionServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&NodeDefinitionService_ServiceDesc, srv)
}

func _NodeDefinitionService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNodeDefinitionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeDefinitionServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeDefinitionService_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeDefinitionServiceServer).List(ctx, req.(*ListNodeDefinitionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeDefinitionService_GetByTypeAndVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNodeDefinitionByTypeAndVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeDefinitionServiceServer).GetByTypeAndVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeDefinitionService_GetByTypeAndVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeDefinitionServiceServer).GetByTypeAndVersion(ctx, req.(*GetNodeDefinitionByTypeAndVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NodeDefinitionService_ServiceDesc is the grpc.ServiceDesc for NodeDefinitionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NodeDefinitionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "v1.NodeDefinitionService",
	HandlerType: (*NodeDefinitionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "List",
			Handler:    _NodeDefinitionService_List_Handler,
		},
		{
			MethodName: "GetByTypeAndVersion",
			Handler:    _NodeDefinitionService_GetByTypeAndVersion_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "v1/node_definition_service.proto",
}
