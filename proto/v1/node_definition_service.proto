syntax = "proto3";

package v1;

import "v1/node.proto";

option go_package = "resflow/proto/generated_go/v1";

message NodeDefinition {
    string id = 1;
    string name = 4;
    string author = 5;
    string description = 6;
    string icon = 7;
    string type = 8;
    string version = 9;
    string category = 10;
    repeated NodeParam input_params = 11;
    repeated NodeParam output_params = 12;
    repeated NodePort input_ports = 13;
    repeated NodePort output_ports = 14;
    bool exception = 15;
    bool builtin = 16;
    bool enabled = 17;
}

message ListNodeDefinitionsRequest {
}

message ListNodeDefinitionsResponse {
    repeated NodeDefinition node_definitions = 1;
}

message GetNodeDefinitionByTypeAndVersionRequest {
    // @gotags: validate:"required" msg_required:"节点类型不能为空"
    string type = 1;
    // @gotags: validate:"required" msg_required:"节点版本不能为空"
    string version = 2;
}

message GetNodeDefinitionByTypeAndVersionResponse {
    NodeDefinition node_definition = 1;
}

service NodeDefinitionService {
    rpc List (ListNodeDefinitionsRequest) returns (ListNodeDefinitionsResponse);
    rpc GetByTypeAndVersion (GetNodeDefinitionByTypeAndVersionRequest) returns (GetNodeDefinitionByTypeAndVersionResponse);
}
