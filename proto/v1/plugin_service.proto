syntax = "proto3";

package v1;

option go_package = "resflow/proto/generated_go/v1";

message Plugin {
    string id = 1;
    string name = 2;
    string version = 3;
    string author = 4;
    string display_name = 5;
    string description = 6;
    string icon = 7;
    string path = 8;
    bool builtin = 9;
    bool enabled = 10;
    string created_at = 20;
    string updated_at = 21;
}

message ListPluginsRequest {
}

message ListPluginsResponse {
    repeated Plugin plugins = 1;
}

service PluginService {
    rpc List (ListPluginsRequest) returns (ListPluginsResponse);
}
