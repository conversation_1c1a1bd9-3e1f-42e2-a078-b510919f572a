syntax = "proto3";

package v1;

import "google/protobuf/struct.proto";
import "v1/common.proto";
import "v1/node.proto";
import "v1/user_service.proto";

option go_package = "resflow/proto/generated_go/v1";

enum WorkflowState {
    CLOSE = 0;
    OPEN = 1;
}

message Workflow {
    string id = 1;
    optional User user = 2;
    string name = 3;
    string icon = 4;
    string description = 5;
    Status status = 6;
    WorkflowViewport viewport = 7;
    repeated WorkflowNode nodes = 8;
    repeated WorkflowLink links = 9;

    string created_at = 20;
    string updated_at = 21;
}

message WorkflowViewport {
    float x = 1;
    float y = 2;
    float zoom = 3;
}

message WorkflowNodePosition {
    float x = 1;
    float y = 2;
}

message WorkflowNode {
    string id = 1;
    optional Workflow workflow = 2;
    string name = 3;
    string description = 4;
    string icon = 5;
    string type = 6;
    string version = 7;
    string plugin_name = 8;
    string plugin_version = 9;
    repeated NodeParam input_params = 15;
    google.protobuf.Struct input_values = 16;
    repeated NodeParam output_params = 17;
    google.protobuf.Struct output_values = 18;
    repeated NodePort input_ports = 19;
    repeated NodePort output_ports = 20;
    WorkflowNodePosition position = 21;
    google.protobuf.Struct data = 22;

    string created_at = 30;
    string updated_at = 31;
}

message WorkflowLink {
    string id = 1;
    optional Workflow workflow = 2;
    string from_node_id = 3;
    string to_node_id = 4;
    string from_port_id = 5;
    string to_port_id = 6;
    string type = 7;
}

message WorkflowNodeRequest {
    // @gotags: validate:"required,uuid4"
    string id = 2;
    // @gotags: validate:"required,max=50" msg_max:"节点名称不能超过50个字"
    string name = 3;
    // @gotags: validate:"omitempty,max=200" msg_max:"节点描述不能超过200个字"
    string description = 4;
    // @gotags: validate:"omitempty"
    string icon = 5;
    // @gotags: validate:"required" msg_required:"节点类型不能为空"
    string type = 6;
    // @gotags: validate:"required" msg_required:"节点版本不能为空"
    string version = 7;
    // @gotags: validate:"omitempty,required"
    optional string plugin_name = 8;
    // @gotags: validate:"omitempty,required"
    optional string plugin_version = 9;
    // @gotags: validate:"omitempty,dive,required"
    repeated NodeParam input_params = 15;
    // @gotags: validate:"omitempty,required"
    google.protobuf.Struct input_values = 16;
    // @gotags: validate:"omitempty,dive,required"
    repeated NodeParam output_params = 17;
    // @gotags: validate:"omitempty,required"
    google.protobuf.Struct output_values = 18;
    // @gotags: validate:"omitempty,dive,required"
    repeated NodePort input_ports = 19;
    // @gotags: validate:"omitempty,dive,required"
    repeated NodePort output_ports = 20;
    // @gotags: validate:"required"
    WorkflowNodePosition position = 21;
    // @gotags: validate:"required"
    google.protobuf.Struct data = 22;
}

message WorkflowLinkRequest {
    // @gotags: validate:"required,uuid4"
    string id = 1;
    // @gotags: validate:"required,uuid4" msg_required:"来源节点不能为空" msg_uuid4:"来源节点id错误"
    string from_node_id = 3;
    // @gotags: validate:"required,uuid4" msg_required:"目标节点不能为空" msg_uuid4:"目标节点id错误"
    string to_node_id = 4;
    string from_port_id = 5;
    string to_port_id = 6;
    // @gotags: validate:"required" msg_required:"边类型错误"
    string type = 7;
}

message CreateWorkflowRequest {
    // @gotags: validate:"required,max=50" label:"名称" msg_required:"请填写名称" msg_max:"名称不能超过50个字"
    string name = 1;
    // @gotags: validate:"omitempty,max=200" msg_max:"描述不能超过200个字"
    string description = 2;
    // @gotags: validate:"omitempty"
    string icon = 3;
    // @gotags: validate:"omitempty,dive,required"
    repeated WorkflowNodeRequest nodes = 4;
    // @gotags: validate:"omitempty,dive,required"
    repeated WorkflowLinkRequest links = 5;
    WorkflowViewport viewport = 6;
}

message WorkflowResponse {
    Workflow workflow = 1;
}

message UpdateWorkflowRequest {
    string id = 1;
    // @gotags: validate:"required,max=50" label:"名称" msg_required:"请填写名称" msg_max:"名称不能超过50个字"
    string name = 2;
    // @gotags: validate:"omitempty,dive,required"
    repeated WorkflowNodeRequest nodes = 3;
    // @gotags: validate:"omitempty,dive,required"
    repeated WorkflowLinkRequest links = 4;
    WorkflowViewport viewport = 5;
}

message ListWorkflowsRequest {

}

message ListWorkflowsResponse {
    repeated Workflow workflows = 1;
}

service WorkflowService {
    rpc GetById (OnlyIdRequest) returns (WorkflowResponse);
    rpc Create (CreateWorkflowRequest) returns (WorkflowResponse);
    rpc Update (UpdateWorkflowRequest) returns (WorkflowResponse);
    rpc DeleteById (OnlyIdRequest) returns (CommonResponse);
    rpc List (ListWorkflowsRequest) returns (ListWorkflowsResponse);
}
