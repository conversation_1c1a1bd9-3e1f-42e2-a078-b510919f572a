syntax = "proto3";

package v1;

import "v1/user_service.proto";

option go_package = "resflow/proto/generated_go/v1";

enum AuthErrCode {
    OK = 0;
    PASSWORD_ERR = 1000;
}

message LoginRequest {
    string username = 1;
    string password = 2;
}

message LoginResponse {
    v1.User user = 1;
    string token = 2;
}

service AuthService {
    rpc Login (LoginRequest) returns (LoginResponse);
}
