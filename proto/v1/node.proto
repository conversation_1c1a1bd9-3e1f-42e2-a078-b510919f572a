syntax = "proto3";

package v1;

option go_package = "resflow/proto/generated_go/v1";

message NodeParam {
    // @gotags: validate:"required" msg_required:"参数ID不能为空"
    string id = 1;
    // @gotags: validate:"required" msg_required:"参数类型不能为空"
    string type = 2;
    // @gotags: validate:"required" msg_required:"参数名称不能为空"
    string label = 3;
    // @gotags: validate:"omitempty"
    string description = 4;
    // @gotags: validate:"omitempty"
    string value = 5;
    // @gotags: validate:"omitempty"
    string schema = 6;
    bool required = 10;
}

message NodePort {
    // @gotags: validate:"required" msg_required:"端口ID不能为空"
    string id = 1;
    string type = 2;
    string position = 3;
}
