syntax = "proto3";

package v1;

import "v1/common.proto";
import "google/protobuf/empty.proto";

option go_package = "resflow/proto/generated_go/v1";

message User {
    string id = 1;
    string username = 2;
    string nickname = 3;
    Status status = 4;
    string created_at = 20;
    string updated_at = 21;
}

message CreateUserRequest {
    string username = 1;
    string password = 2;
    string nickname = 3;
}

message CreateUserResponse {
    User user = 1;
}

service UserService {
    rpc Create (CreateUserRequest) returns (CreateUserResponse);
    rpc GetCurrentUser(google.protobuf.Empty) returns (User);
}
