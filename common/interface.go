package common

// JsonSerializable 实现该接口表示可以转换为JSON字符串
type JsonSerializable[T any] interface {
	ToJson() (string, error)
	FromJson(string) (T, error)
}

// Executor 实现该接口表示可以执行
type Executor interface {
	Execute() error
}

// Cancelable 实现该接口表示可以取消
type Cancelable interface {
	Cancel() error
}

// Pausable 实现该接口表示可以暂停和恢复
type Pausable interface {
	Pause() error
	Resume() error
}

type HasID interface {
	GetID() string
}
