package common

import (
	"github.com/go-playground/locales/en"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	en_translations "github.com/go-playground/validator/v10/translations/en"
	"reflect"
)

func GetValidate() (*validator.Validate, *ut.Translator) {
	enTranslator := en.New()
	uni := ut.New(enTranslator, enTranslator)
	trans, _ := uni.GetTranslator("en")

	validate := validator.New(validator.WithRequiredStructEnabled())
	en_translations.RegisterDefaultTranslations(validate, trans)

	return validate, &trans
}

func translateOverride(validate *validator.Validate, trans *ut.Translator) {
	validate.RegisterTranslation("required", *trans, func(ut ut.Translator) error {
		return ut.Add("required", "{0}为必填项", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("required", fe.Field())
		return t
	})

	validate.RegisterTranslation("max", *trans, func(ut ut.Translator) error {
		return ut.Add("max", "{0}的长度不能大于{1}", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("max", fe.Field(), fe.Param())
		return t
	})
}

func GetValidationErrMsg[T any](errs validator.ValidationErrors, trans *ut.Translator, t T) string {
	for _, e := range errs {
		fieldName := e.Field()
		rule := e.Tag()
		field, _ := reflect.TypeOf(t).FieldByName(fieldName)
		msgTag := field.Tag.Get("msg_" + rule)
		if msgTag != "" {
			return msgTag
		} else {
			return e.Translate(*trans)
		}
	}
	return "validation error"
}
