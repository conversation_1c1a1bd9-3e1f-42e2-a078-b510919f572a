package common

import "sync"

type Queue[T any] interface {
	Enqueue(T)
	Dequeue() T
	Size() int
	Front() T
	Rear() T
	IsEmpty() bool
}

type queue[T any] struct {
	elements []T
}

func NewQueue[T any]() Queue[T] {
	return &queue[T]{elements: make([]T, 0)}
}

func (q *queue[T]) Enqueue(value T) {
	q.elements = append(q.elements, value)
}

func (q *queue[T]) Dequeue() T {
	value := q.elements[0]
	q.elements = q.elements[1:]
	return value
}

func (q *queue[T]) Size() int {
	return len(q.elements)
}

func (q *queue[T]) Front() T {
	return q.elements[0]
}

func (q *queue[T]) Rear() T {
	return q.elements[len(q.elements)-1]
}

func (q *queue[T]) IsEmpty() bool {
	return q.Size() == 0
}

type concurrentQueue[T any] struct {
	elements []T
	mutex    sync.Mutex
}

func NewConcurrentQueue[T any]() Queue[T] {
	return &concurrentQueue[T]{elements: make([]T, 0)}
}

func (q *concurrentQueue[T]) Enqueue(value T) {
	q.mutex.Lock()
	q.elements = append(q.elements, value)
	q.mutex.Unlock()
}

func (q *concurrentQueue[T]) Dequeue() T {
	q.mutex.Lock()
	value := q.elements[0]
	q.elements = q.elements[1:]
	q.mutex.Unlock()
	return value
}

func (q *concurrentQueue[T]) Size() int {
	q.mutex.Lock()
	count := len(q.elements)
	q.mutex.Unlock()
	return count
}

func (q *concurrentQueue[T]) Front() T {
	q.mutex.Lock()
	value := q.elements[0]
	q.mutex.Unlock()
	return value
}

func (q *concurrentQueue[T]) Rear() T {
	q.mutex.Lock()
	value := q.elements[len(q.elements)-1]
	q.mutex.Unlock()
	return value
}

func (q *concurrentQueue[T]) IsEmpty() bool {
	return q.Size() == 0
}

type channelQueue[T any] struct {
	ch chan T
}

func NewChanQueue[T any](size int) Queue[T] {
	return &channelQueue[T]{
		ch: make(chan T, size),
	}
}

func (q *channelQueue[T]) Enqueue(element T) {
	q.ch <- element
}

func (q *channelQueue[T]) Dequeue() T {
	return <-q.ch
}

func (q *channelQueue[T]) Size() int {
	return len(q.ch)
}

func (q *channelQueue[T]) Front() T {
	panic("implement me")
}

func (q *channelQueue[T]) Rear() T {
	panic("implement me")
}

func (q *channelQueue[T]) IsEmpty() bool {
	return q.Size() == 0
}
