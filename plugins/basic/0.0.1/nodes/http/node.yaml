name: HTTP请求
author: resflow
description: 发起一个HTTP请求
version: 0.0.1
icon: icon.svg
category: 网络
type: http-request
exception: true
input_params:
  - name: method
    type: string
    view_type: select
    view_config:
      options:
        - label: GET
          value: GET
        - label: POST
          value: POST
        - label: PUT
          value: PUT
        - label: DELETE
          value: DELETE
        - label: PATCH
          value: PATCH
        - label: HEAD
          value: HEAD
    label: 请求方式
    description: 请求方式
    required: true
    default: "GET"
  - name: url
    type: string
    view_type: input
    label: 请求地址
    description: 请求地址
    required: true
    default: ""
  - name: headers
    type: object
    view_type: map
    label: 请求头
    required: false
    default: ""
    view_config:
      schema:
        key_title: 键
        key_type: string
        value_title: 值
        value_type: string
  - name: params
    type: object
    view_type: map
    label: 请求参数
    required: false
    default: ""
    view_config:
      schema:
        key_title: 键
        key_type: string
        value_title: 值
        value_type: string
  - name: body_type
    type: string
    view_type: select
    view_config:
      options:
        - label: none
          value: none
        - label: form-data
          value: form-data
        - label: credentialsSelect
          value: map
        - label: json
          value: json
        - label: raw
          value: textarea
        - label: binary
          value: file
    required: false
    default: "form-data"
    group: "请求体"
  - name: body
    type: string
    view_type: "{{ body_type }}"
    label: 请求体
    required: false
    default: ""
    group: 请求体
  - name: timeout
    type: number
    view_type: input
    label: 超时时间
    description: 单位毫秒
    required: false
    default: 0
  - name: retry
    type: number
    view_type: input
    label: 重试次数
    required: false
    default: 0
  - name: retry_interval
    type: number
    view_type: input
    label: 重试间隔
    description: 单位毫秒
    required: false
    default: 0
output_params:
  - name: status_code
    type: number
    label: 状态码
    description: 响应状态码
  - name: headers
    type: object
    label: 响应头
    description: 响应头
  - name: body
    type: string
    label: 响应内容
    description: 响应内容
input_ports:
  - name: input
    type: target
    position: left
output_ports:
  - name: output
    type: source
    position: right
runtime:
  entry: index.js
  type: js
  args: ""
