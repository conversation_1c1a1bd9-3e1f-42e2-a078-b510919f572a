// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"resflow/ent/user"
	"resflow/ent/workflow"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

// Workflow is the model entity for the Workflow schema.
type Workflow struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID uuid.UUID `json:"user_id,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// IconType holds the value of the "icon_type" field.
	IconType string `json:"icon_type,omitempty"`
	// IconBgColor holds the value of the "icon_bg_color" field.
	IconBgColor string `json:"icon_bg_color,omitempty"`
	// IconData holds the value of the "icon_data" field.
	IconData string `json:"icon_data,omitempty"`
	// Description holds the value of the "description" field.
	Description string `json:"description,omitempty"`
	// Status holds the value of the "status" field.
	Status int `json:"status,omitempty"`
	// Viewport holds the value of the "viewport" field.
	Viewport string `json:"viewport,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the WorkflowQuery when eager-loading is set.
	Edges        WorkflowEdges `json:"edges"`
	selectValues sql.SelectValues
}

// WorkflowEdges holds the relations/edges for other nodes in the graph.
type WorkflowEdges struct {
	// User holds the value of the user edge.
	User *User `json:"user,omitempty"`
	// Nodes holds the value of the nodes edge.
	Nodes []*WorkflowNode `json:"nodes,omitempty"`
	// WorkflowEdges holds the value of the workflow_edges edge.
	WorkflowEdges []*WorkflowsEdge `json:"workflow_edges,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [3]bool
}

// UserOrErr returns the User value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e WorkflowEdges) UserOrErr() (*User, error) {
	if e.User != nil {
		return e.User, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: user.Label}
	}
	return nil, &NotLoadedError{edge: "user"}
}

// NodesOrErr returns the Nodes value or an error if the edge
// was not loaded in eager-loading.
func (e WorkflowEdges) NodesOrErr() ([]*WorkflowNode, error) {
	if e.loadedTypes[1] {
		return e.Nodes, nil
	}
	return nil, &NotLoadedError{edge: "nodes"}
}

// WorkflowEdgesOrErr returns the WorkflowEdges value or an error if the edge
// was not loaded in eager-loading.
func (e WorkflowEdges) WorkflowEdgesOrErr() ([]*WorkflowsEdge, error) {
	if e.loadedTypes[2] {
		return e.WorkflowEdges, nil
	}
	return nil, &NotLoadedError{edge: "workflow_edges"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Workflow) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case workflow.FieldStatus:
			values[i] = new(sql.NullInt64)
		case workflow.FieldName, workflow.FieldIconType, workflow.FieldIconBgColor, workflow.FieldIconData, workflow.FieldDescription, workflow.FieldViewport:
			values[i] = new(sql.NullString)
		case workflow.FieldCreatedAt, workflow.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case workflow.FieldID, workflow.FieldUserID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Workflow fields.
func (w *Workflow) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case workflow.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				w.ID = *value
			}
		case workflow.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				w.UserID = *value
			}
		case workflow.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				w.Name = value.String
			}
		case workflow.FieldIconType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field icon_type", values[i])
			} else if value.Valid {
				w.IconType = value.String
			}
		case workflow.FieldIconBgColor:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field icon_bg_color", values[i])
			} else if value.Valid {
				w.IconBgColor = value.String
			}
		case workflow.FieldIconData:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field icon_data", values[i])
			} else if value.Valid {
				w.IconData = value.String
			}
		case workflow.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				w.Description = value.String
			}
		case workflow.FieldStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				w.Status = int(value.Int64)
			}
		case workflow.FieldViewport:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field viewport", values[i])
			} else if value.Valid {
				w.Viewport = value.String
			}
		case workflow.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				w.CreatedAt = value.Time
			}
		case workflow.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				w.UpdatedAt = value.Time
			}
		default:
			w.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Workflow.
// This includes values selected through modifiers, order, etc.
func (w *Workflow) Value(name string) (ent.Value, error) {
	return w.selectValues.Get(name)
}

// QueryUser queries the "user" edge of the Workflow entity.
func (w *Workflow) QueryUser() *UserQuery {
	return NewWorkflowClient(w.config).QueryUser(w)
}

// QueryNodes queries the "nodes" edge of the Workflow entity.
func (w *Workflow) QueryNodes() *WorkflowNodeQuery {
	return NewWorkflowClient(w.config).QueryNodes(w)
}

// QueryWorkflowEdges queries the "workflow_edges" edge of the Workflow entity.
func (w *Workflow) QueryWorkflowEdges() *WorkflowsEdgeQuery {
	return NewWorkflowClient(w.config).QueryWorkflowEdges(w)
}

// Update returns a builder for updating this Workflow.
// Note that you need to call Workflow.Unwrap() before calling this method if this Workflow
// was returned from a transaction, and the transaction was committed or rolled back.
func (w *Workflow) Update() *WorkflowUpdateOne {
	return NewWorkflowClient(w.config).UpdateOne(w)
}

// Unwrap unwraps the Workflow entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (w *Workflow) Unwrap() *Workflow {
	_tx, ok := w.config.driver.(*txDriver)
	if !ok {
		panic("ent: Workflow is not a transactional entity")
	}
	w.config.driver = _tx.drv
	return w
}

// String implements the fmt.Stringer.
func (w *Workflow) String() string {
	var builder strings.Builder
	builder.WriteString("Workflow(")
	builder.WriteString(fmt.Sprintf("id=%v, ", w.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", w.UserID))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(w.Name)
	builder.WriteString(", ")
	builder.WriteString("icon_type=")
	builder.WriteString(w.IconType)
	builder.WriteString(", ")
	builder.WriteString("icon_bg_color=")
	builder.WriteString(w.IconBgColor)
	builder.WriteString(", ")
	builder.WriteString("icon_data=")
	builder.WriteString(w.IconData)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(w.Description)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", w.Status))
	builder.WriteString(", ")
	builder.WriteString("viewport=")
	builder.WriteString(w.Viewport)
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(w.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(w.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Workflows is a parsable slice of Workflow.
type Workflows []*Workflow
