// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"resflow/ent/workflow"
	"resflow/ent/workflownode"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// WorkflowNodeCreate is the builder for creating a WorkflowNode entity.
type WorkflowNodeCreate struct {
	config
	mutation *WorkflowNodeMutation
	hooks    []Hook
}

// SetWorkflowID sets the "workflow_id" field.
func (wnc *WorkflowNodeCreate) SetWorkflowID(u uuid.UUID) *WorkflowNodeCreate {
	wnc.mutation.SetWorkflowID(u)
	return wnc
}

// SetName sets the "name" field.
func (wnc *WorkflowNodeCreate) SetName(s string) *WorkflowNodeCreate {
	wnc.mutation.SetName(s)
	return wnc
}

// SetDescription sets the "description" field.
func (wnc *WorkflowNodeCreate) SetDescription(s string) *WorkflowNodeCreate {
	wnc.mutation.SetDescription(s)
	return wnc
}

// SetIconType sets the "icon_type" field.
func (wnc *WorkflowNodeCreate) SetIconType(s string) *WorkflowNodeCreate {
	wnc.mutation.SetIconType(s)
	return wnc
}

// SetIconBgColor sets the "icon_bg_color" field.
func (wnc *WorkflowNodeCreate) SetIconBgColor(s string) *WorkflowNodeCreate {
	wnc.mutation.SetIconBgColor(s)
	return wnc
}

// SetIconData sets the "icon_data" field.
func (wnc *WorkflowNodeCreate) SetIconData(s string) *WorkflowNodeCreate {
	wnc.mutation.SetIconData(s)
	return wnc
}

// SetType sets the "type" field.
func (wnc *WorkflowNodeCreate) SetType(s string) *WorkflowNodeCreate {
	wnc.mutation.SetType(s)
	return wnc
}

// SetVersion sets the "version" field.
func (wnc *WorkflowNodeCreate) SetVersion(s string) *WorkflowNodeCreate {
	wnc.mutation.SetVersion(s)
	return wnc
}

// SetPluginName sets the "plugin_name" field.
func (wnc *WorkflowNodeCreate) SetPluginName(s string) *WorkflowNodeCreate {
	wnc.mutation.SetPluginName(s)
	return wnc
}

// SetPluginVersion sets the "plugin_version" field.
func (wnc *WorkflowNodeCreate) SetPluginVersion(s string) *WorkflowNodeCreate {
	wnc.mutation.SetPluginVersion(s)
	return wnc
}

// SetInputParams sets the "input_params" field.
func (wnc *WorkflowNodeCreate) SetInputParams(s string) *WorkflowNodeCreate {
	wnc.mutation.SetInputParams(s)
	return wnc
}

// SetOutputParams sets the "output_params" field.
func (wnc *WorkflowNodeCreate) SetOutputParams(s string) *WorkflowNodeCreate {
	wnc.mutation.SetOutputParams(s)
	return wnc
}

// SetInputPorts sets the "input_ports" field.
func (wnc *WorkflowNodeCreate) SetInputPorts(s string) *WorkflowNodeCreate {
	wnc.mutation.SetInputPorts(s)
	return wnc
}

// SetOutputPorts sets the "output_ports" field.
func (wnc *WorkflowNodeCreate) SetOutputPorts(s string) *WorkflowNodeCreate {
	wnc.mutation.SetOutputPorts(s)
	return wnc
}

// SetPosition sets the "position" field.
func (wnc *WorkflowNodeCreate) SetPosition(s string) *WorkflowNodeCreate {
	wnc.mutation.SetPosition(s)
	return wnc
}

// SetConfigData sets the "config_data" field.
func (wnc *WorkflowNodeCreate) SetConfigData(s string) *WorkflowNodeCreate {
	wnc.mutation.SetConfigData(s)
	return wnc
}

// SetNillableConfigData sets the "config_data" field if the given value is not nil.
func (wnc *WorkflowNodeCreate) SetNillableConfigData(s *string) *WorkflowNodeCreate {
	if s != nil {
		wnc.SetConfigData(*s)
	}
	return wnc
}

// SetCreatedAt sets the "created_at" field.
func (wnc *WorkflowNodeCreate) SetCreatedAt(t time.Time) *WorkflowNodeCreate {
	wnc.mutation.SetCreatedAt(t)
	return wnc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (wnc *WorkflowNodeCreate) SetNillableCreatedAt(t *time.Time) *WorkflowNodeCreate {
	if t != nil {
		wnc.SetCreatedAt(*t)
	}
	return wnc
}

// SetUpdatedAt sets the "updated_at" field.
func (wnc *WorkflowNodeCreate) SetUpdatedAt(t time.Time) *WorkflowNodeCreate {
	wnc.mutation.SetUpdatedAt(t)
	return wnc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wnc *WorkflowNodeCreate) SetNillableUpdatedAt(t *time.Time) *WorkflowNodeCreate {
	if t != nil {
		wnc.SetUpdatedAt(*t)
	}
	return wnc
}

// SetID sets the "id" field.
func (wnc *WorkflowNodeCreate) SetID(u uuid.UUID) *WorkflowNodeCreate {
	wnc.mutation.SetID(u)
	return wnc
}

// SetWorkflow sets the "workflow" edge to the Workflow entity.
func (wnc *WorkflowNodeCreate) SetWorkflow(w *Workflow) *WorkflowNodeCreate {
	return wnc.SetWorkflowID(w.ID)
}

// Mutation returns the WorkflowNodeMutation object of the builder.
func (wnc *WorkflowNodeCreate) Mutation() *WorkflowNodeMutation {
	return wnc.mutation
}

// Save creates the WorkflowNode in the database.
func (wnc *WorkflowNodeCreate) Save(ctx context.Context) (*WorkflowNode, error) {
	wnc.defaults()
	return withHooks(ctx, wnc.sqlSave, wnc.mutation, wnc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (wnc *WorkflowNodeCreate) SaveX(ctx context.Context) *WorkflowNode {
	v, err := wnc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wnc *WorkflowNodeCreate) Exec(ctx context.Context) error {
	_, err := wnc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wnc *WorkflowNodeCreate) ExecX(ctx context.Context) {
	if err := wnc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wnc *WorkflowNodeCreate) defaults() {
	if _, ok := wnc.mutation.CreatedAt(); !ok {
		v := workflownode.DefaultCreatedAt()
		wnc.mutation.SetCreatedAt(v)
	}
	if _, ok := wnc.mutation.UpdatedAt(); !ok {
		v := workflownode.DefaultUpdatedAt()
		wnc.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wnc *WorkflowNodeCreate) check() error {
	if _, ok := wnc.mutation.WorkflowID(); !ok {
		return &ValidationError{Name: "workflow_id", err: errors.New(`ent: missing required field "WorkflowNode.workflow_id"`)}
	}
	if _, ok := wnc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "WorkflowNode.name"`)}
	}
	if _, ok := wnc.mutation.Description(); !ok {
		return &ValidationError{Name: "description", err: errors.New(`ent: missing required field "WorkflowNode.description"`)}
	}
	if _, ok := wnc.mutation.IconType(); !ok {
		return &ValidationError{Name: "icon_type", err: errors.New(`ent: missing required field "WorkflowNode.icon_type"`)}
	}
	if _, ok := wnc.mutation.IconBgColor(); !ok {
		return &ValidationError{Name: "icon_bg_color", err: errors.New(`ent: missing required field "WorkflowNode.icon_bg_color"`)}
	}
	if _, ok := wnc.mutation.IconData(); !ok {
		return &ValidationError{Name: "icon_data", err: errors.New(`ent: missing required field "WorkflowNode.icon_data"`)}
	}
	if _, ok := wnc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "WorkflowNode.type"`)}
	}
	if _, ok := wnc.mutation.Version(); !ok {
		return &ValidationError{Name: "version", err: errors.New(`ent: missing required field "WorkflowNode.version"`)}
	}
	if _, ok := wnc.mutation.PluginName(); !ok {
		return &ValidationError{Name: "plugin_name", err: errors.New(`ent: missing required field "WorkflowNode.plugin_name"`)}
	}
	if _, ok := wnc.mutation.PluginVersion(); !ok {
		return &ValidationError{Name: "plugin_version", err: errors.New(`ent: missing required field "WorkflowNode.plugin_version"`)}
	}
	if _, ok := wnc.mutation.InputParams(); !ok {
		return &ValidationError{Name: "input_params", err: errors.New(`ent: missing required field "WorkflowNode.input_params"`)}
	}
	if _, ok := wnc.mutation.OutputParams(); !ok {
		return &ValidationError{Name: "output_params", err: errors.New(`ent: missing required field "WorkflowNode.output_params"`)}
	}
	if _, ok := wnc.mutation.InputPorts(); !ok {
		return &ValidationError{Name: "input_ports", err: errors.New(`ent: missing required field "WorkflowNode.input_ports"`)}
	}
	if _, ok := wnc.mutation.OutputPorts(); !ok {
		return &ValidationError{Name: "output_ports", err: errors.New(`ent: missing required field "WorkflowNode.output_ports"`)}
	}
	if _, ok := wnc.mutation.Position(); !ok {
		return &ValidationError{Name: "position", err: errors.New(`ent: missing required field "WorkflowNode.position"`)}
	}
	if _, ok := wnc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "WorkflowNode.created_at"`)}
	}
	if _, ok := wnc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "WorkflowNode.updated_at"`)}
	}
	if len(wnc.mutation.WorkflowIDs()) == 0 {
		return &ValidationError{Name: "workflow", err: errors.New(`ent: missing required edge "WorkflowNode.workflow"`)}
	}
	return nil
}

func (wnc *WorkflowNodeCreate) sqlSave(ctx context.Context) (*WorkflowNode, error) {
	if err := wnc.check(); err != nil {
		return nil, err
	}
	_node, _spec := wnc.createSpec()
	if err := sqlgraph.CreateNode(ctx, wnc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	wnc.mutation.id = &_node.ID
	wnc.mutation.done = true
	return _node, nil
}

func (wnc *WorkflowNodeCreate) createSpec() (*WorkflowNode, *sqlgraph.CreateSpec) {
	var (
		_node = &WorkflowNode{config: wnc.config}
		_spec = sqlgraph.NewCreateSpec(workflownode.Table, sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID))
	)
	if id, ok := wnc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := wnc.mutation.Name(); ok {
		_spec.SetField(workflownode.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := wnc.mutation.Description(); ok {
		_spec.SetField(workflownode.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := wnc.mutation.IconType(); ok {
		_spec.SetField(workflownode.FieldIconType, field.TypeString, value)
		_node.IconType = value
	}
	if value, ok := wnc.mutation.IconBgColor(); ok {
		_spec.SetField(workflownode.FieldIconBgColor, field.TypeString, value)
		_node.IconBgColor = value
	}
	if value, ok := wnc.mutation.IconData(); ok {
		_spec.SetField(workflownode.FieldIconData, field.TypeString, value)
		_node.IconData = value
	}
	if value, ok := wnc.mutation.GetType(); ok {
		_spec.SetField(workflownode.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := wnc.mutation.Version(); ok {
		_spec.SetField(workflownode.FieldVersion, field.TypeString, value)
		_node.Version = value
	}
	if value, ok := wnc.mutation.PluginName(); ok {
		_spec.SetField(workflownode.FieldPluginName, field.TypeString, value)
		_node.PluginName = value
	}
	if value, ok := wnc.mutation.PluginVersion(); ok {
		_spec.SetField(workflownode.FieldPluginVersion, field.TypeString, value)
		_node.PluginVersion = value
	}
	if value, ok := wnc.mutation.InputParams(); ok {
		_spec.SetField(workflownode.FieldInputParams, field.TypeString, value)
		_node.InputParams = value
	}
	if value, ok := wnc.mutation.OutputParams(); ok {
		_spec.SetField(workflownode.FieldOutputParams, field.TypeString, value)
		_node.OutputParams = value
	}
	if value, ok := wnc.mutation.InputPorts(); ok {
		_spec.SetField(workflownode.FieldInputPorts, field.TypeString, value)
		_node.InputPorts = value
	}
	if value, ok := wnc.mutation.OutputPorts(); ok {
		_spec.SetField(workflownode.FieldOutputPorts, field.TypeString, value)
		_node.OutputPorts = value
	}
	if value, ok := wnc.mutation.Position(); ok {
		_spec.SetField(workflownode.FieldPosition, field.TypeString, value)
		_node.Position = value
	}
	if value, ok := wnc.mutation.ConfigData(); ok {
		_spec.SetField(workflownode.FieldConfigData, field.TypeString, value)
		_node.ConfigData = value
	}
	if value, ok := wnc.mutation.CreatedAt(); ok {
		_spec.SetField(workflownode.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := wnc.mutation.UpdatedAt(); ok {
		_spec.SetField(workflownode.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if nodes := wnc.mutation.WorkflowIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflownode.WorkflowTable,
			Columns: []string{workflownode.WorkflowColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.WorkflowID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// WorkflowNodeCreateBulk is the builder for creating many WorkflowNode entities in bulk.
type WorkflowNodeCreateBulk struct {
	config
	err      error
	builders []*WorkflowNodeCreate
}

// Save creates the WorkflowNode entities in the database.
func (wncb *WorkflowNodeCreateBulk) Save(ctx context.Context) ([]*WorkflowNode, error) {
	if wncb.err != nil {
		return nil, wncb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(wncb.builders))
	nodes := make([]*WorkflowNode, len(wncb.builders))
	mutators := make([]Mutator, len(wncb.builders))
	for i := range wncb.builders {
		func(i int, root context.Context) {
			builder := wncb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*WorkflowNodeMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, wncb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, wncb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, wncb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (wncb *WorkflowNodeCreateBulk) SaveX(ctx context.Context) []*WorkflowNode {
	v, err := wncb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wncb *WorkflowNodeCreateBulk) Exec(ctx context.Context) error {
	_, err := wncb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wncb *WorkflowNodeCreateBulk) ExecX(ctx context.Context) {
	if err := wncb.Exec(ctx); err != nil {
		panic(err)
	}
}
