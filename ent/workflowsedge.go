// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"resflow/ent/workflow"
	"resflow/ent/workflowsedge"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

// WorkflowsEdge is the model entity for the WorkflowsEdge schema.
type WorkflowsEdge struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// WorkflowID holds the value of the "workflow_id" field.
	WorkflowID uuid.UUID `json:"workflow_id,omitempty"`
	// FromNodeID holds the value of the "from_node_id" field.
	FromNodeID uuid.UUID `json:"from_node_id,omitempty"`
	// ToNodeID holds the value of the "to_node_id" field.
	ToNodeID uuid.UUID `json:"to_node_id,omitempty"`
	// FromPortID holds the value of the "from_port_id" field.
	FromPortID uuid.UUID `json:"from_port_id,omitempty"`
	// ToPortID holds the value of the "to_port_id" field.
	ToPortID uuid.UUID `json:"to_port_id,omitempty"`
	// Type holds the value of the "type" field.
	Type string `json:"type,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the WorkflowsEdgeQuery when eager-loading is set.
	Edges        WorkflowsEdgeEdges `json:"edges"`
	selectValues sql.SelectValues
}

// WorkflowsEdgeEdges holds the relations/edges for other nodes in the graph.
type WorkflowsEdgeEdges struct {
	// Workflow holds the value of the workflow edge.
	Workflow *Workflow `json:"workflow,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// WorkflowOrErr returns the Workflow value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e WorkflowsEdgeEdges) WorkflowOrErr() (*Workflow, error) {
	if e.Workflow != nil {
		return e.Workflow, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: workflow.Label}
	}
	return nil, &NotLoadedError{edge: "workflow"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*WorkflowsEdge) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case workflowsedge.FieldType:
			values[i] = new(sql.NullString)
		case workflowsedge.FieldCreatedAt, workflowsedge.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case workflowsedge.FieldID, workflowsedge.FieldWorkflowID, workflowsedge.FieldFromNodeID, workflowsedge.FieldToNodeID, workflowsedge.FieldFromPortID, workflowsedge.FieldToPortID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the WorkflowsEdge fields.
func (we *WorkflowsEdge) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case workflowsedge.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				we.ID = *value
			}
		case workflowsedge.FieldWorkflowID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field workflow_id", values[i])
			} else if value != nil {
				we.WorkflowID = *value
			}
		case workflowsedge.FieldFromNodeID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field from_node_id", values[i])
			} else if value != nil {
				we.FromNodeID = *value
			}
		case workflowsedge.FieldToNodeID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field to_node_id", values[i])
			} else if value != nil {
				we.ToNodeID = *value
			}
		case workflowsedge.FieldFromPortID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field from_port_id", values[i])
			} else if value != nil {
				we.FromPortID = *value
			}
		case workflowsedge.FieldToPortID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field to_port_id", values[i])
			} else if value != nil {
				we.ToPortID = *value
			}
		case workflowsedge.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				we.Type = value.String
			}
		case workflowsedge.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				we.CreatedAt = value.Time
			}
		case workflowsedge.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				we.UpdatedAt = value.Time
			}
		default:
			we.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the WorkflowsEdge.
// This includes values selected through modifiers, order, etc.
func (we *WorkflowsEdge) Value(name string) (ent.Value, error) {
	return we.selectValues.Get(name)
}

// QueryWorkflow queries the "workflow" edge of the WorkflowsEdge entity.
func (we *WorkflowsEdge) QueryWorkflow() *WorkflowQuery {
	return NewWorkflowsEdgeClient(we.config).QueryWorkflow(we)
}

// Update returns a builder for updating this WorkflowsEdge.
// Note that you need to call WorkflowsEdge.Unwrap() before calling this method if this WorkflowsEdge
// was returned from a transaction, and the transaction was committed or rolled back.
func (we *WorkflowsEdge) Update() *WorkflowsEdgeUpdateOne {
	return NewWorkflowsEdgeClient(we.config).UpdateOne(we)
}

// Unwrap unwraps the WorkflowsEdge entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (we *WorkflowsEdge) Unwrap() *WorkflowsEdge {
	_tx, ok := we.config.driver.(*txDriver)
	if !ok {
		panic("ent: WorkflowsEdge is not a transactional entity")
	}
	we.config.driver = _tx.drv
	return we
}

// String implements the fmt.Stringer.
func (we *WorkflowsEdge) String() string {
	var builder strings.Builder
	builder.WriteString("WorkflowsEdge(")
	builder.WriteString(fmt.Sprintf("id=%v, ", we.ID))
	builder.WriteString("workflow_id=")
	builder.WriteString(fmt.Sprintf("%v", we.WorkflowID))
	builder.WriteString(", ")
	builder.WriteString("from_node_id=")
	builder.WriteString(fmt.Sprintf("%v", we.FromNodeID))
	builder.WriteString(", ")
	builder.WriteString("to_node_id=")
	builder.WriteString(fmt.Sprintf("%v", we.ToNodeID))
	builder.WriteString(", ")
	builder.WriteString("from_port_id=")
	builder.WriteString(fmt.Sprintf("%v", we.FromPortID))
	builder.WriteString(", ")
	builder.WriteString("to_port_id=")
	builder.WriteString(fmt.Sprintf("%v", we.ToPortID))
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(we.Type)
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(we.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(we.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// WorkflowsEdges is a parsable slice of WorkflowsEdge.
type WorkflowsEdges []*WorkflowsEdge
