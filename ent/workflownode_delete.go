// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"resflow/ent/predicate"
	"resflow/ent/workflownode"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WorkflowNodeDelete is the builder for deleting a WorkflowNode entity.
type WorkflowNodeDelete struct {
	config
	hooks    []Hook
	mutation *WorkflowNodeMutation
}

// Where appends a list predicates to the WorkflowNodeDelete builder.
func (wnd *WorkflowNodeDelete) Where(ps ...predicate.WorkflowNode) *WorkflowNodeDelete {
	wnd.mutation.Where(ps...)
	return wnd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (wnd *WorkflowNodeDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, wnd.sqlExec, wnd.mutation, wnd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (wnd *WorkflowNodeDelete) ExecX(ctx context.Context) int {
	n, err := wnd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (wnd *WorkflowNodeDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(workflownode.Table, sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID))
	if ps := wnd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, wnd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	wnd.mutation.done = true
	return affected, err
}

// WorkflowNodeDeleteOne is the builder for deleting a single WorkflowNode entity.
type WorkflowNodeDeleteOne struct {
	wnd *WorkflowNodeDelete
}

// Where appends a list predicates to the WorkflowNodeDelete builder.
func (wndo *WorkflowNodeDeleteOne) Where(ps ...predicate.WorkflowNode) *WorkflowNodeDeleteOne {
	wndo.wnd.mutation.Where(ps...)
	return wndo
}

// Exec executes the deletion query.
func (wndo *WorkflowNodeDeleteOne) Exec(ctx context.Context) error {
	n, err := wndo.wnd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{workflownode.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (wndo *WorkflowNodeDeleteOne) ExecX(ctx context.Context) {
	if err := wndo.Exec(ctx); err != nil {
		panic(err)
	}
}
