// Code generated by ent, DO NOT EDIT.

package workflow

import (
	"resflow/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Workflow {
	return predicate.Workflow(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Workflow {
	return predicate.Workflow(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Workflow {
	return predicate.Workflow(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Workflow {
	return predicate.Workflow(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Workflow {
	return predicate.Workflow(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Workflow {
	return predicate.Workflow(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Workflow {
	return predicate.Workflow(sql.FieldLTE(FieldID, id))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldUserID, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldName, v))
}

// IconType applies equality check predicate on the "icon_type" field. It's identical to IconTypeEQ.
func IconType(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldIconType, v))
}

// IconBgColor applies equality check predicate on the "icon_bg_color" field. It's identical to IconBgColorEQ.
func IconBgColor(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldIconBgColor, v))
}

// IconData applies equality check predicate on the "icon_data" field. It's identical to IconDataEQ.
func IconData(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldIconData, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldDescription, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v int) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldStatus, v))
}

// Viewport applies equality check predicate on the "viewport" field. It's identical to ViewportEQ.
func Viewport(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldViewport, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldUpdatedAt, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.Workflow {
	return predicate.Workflow(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.Workflow {
	return predicate.Workflow(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.Workflow {
	return predicate.Workflow(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDIsNil applies the IsNil predicate on the "user_id" field.
func UserIDIsNil() predicate.Workflow {
	return predicate.Workflow(sql.FieldIsNull(FieldUserID))
}

// UserIDNotNil applies the NotNil predicate on the "user_id" field.
func UserIDNotNil() predicate.Workflow {
	return predicate.Workflow(sql.FieldNotNull(FieldUserID))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Workflow {
	return predicate.Workflow(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Workflow {
	return predicate.Workflow(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldContainsFold(FieldName, v))
}

// IconTypeEQ applies the EQ predicate on the "icon_type" field.
func IconTypeEQ(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldIconType, v))
}

// IconTypeNEQ applies the NEQ predicate on the "icon_type" field.
func IconTypeNEQ(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldNEQ(FieldIconType, v))
}

// IconTypeIn applies the In predicate on the "icon_type" field.
func IconTypeIn(vs ...string) predicate.Workflow {
	return predicate.Workflow(sql.FieldIn(FieldIconType, vs...))
}

// IconTypeNotIn applies the NotIn predicate on the "icon_type" field.
func IconTypeNotIn(vs ...string) predicate.Workflow {
	return predicate.Workflow(sql.FieldNotIn(FieldIconType, vs...))
}

// IconTypeGT applies the GT predicate on the "icon_type" field.
func IconTypeGT(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldGT(FieldIconType, v))
}

// IconTypeGTE applies the GTE predicate on the "icon_type" field.
func IconTypeGTE(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldGTE(FieldIconType, v))
}

// IconTypeLT applies the LT predicate on the "icon_type" field.
func IconTypeLT(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldLT(FieldIconType, v))
}

// IconTypeLTE applies the LTE predicate on the "icon_type" field.
func IconTypeLTE(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldLTE(FieldIconType, v))
}

// IconTypeContains applies the Contains predicate on the "icon_type" field.
func IconTypeContains(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldContains(FieldIconType, v))
}

// IconTypeHasPrefix applies the HasPrefix predicate on the "icon_type" field.
func IconTypeHasPrefix(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldHasPrefix(FieldIconType, v))
}

// IconTypeHasSuffix applies the HasSuffix predicate on the "icon_type" field.
func IconTypeHasSuffix(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldHasSuffix(FieldIconType, v))
}

// IconTypeEqualFold applies the EqualFold predicate on the "icon_type" field.
func IconTypeEqualFold(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEqualFold(FieldIconType, v))
}

// IconTypeContainsFold applies the ContainsFold predicate on the "icon_type" field.
func IconTypeContainsFold(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldContainsFold(FieldIconType, v))
}

// IconBgColorEQ applies the EQ predicate on the "icon_bg_color" field.
func IconBgColorEQ(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldIconBgColor, v))
}

// IconBgColorNEQ applies the NEQ predicate on the "icon_bg_color" field.
func IconBgColorNEQ(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldNEQ(FieldIconBgColor, v))
}

// IconBgColorIn applies the In predicate on the "icon_bg_color" field.
func IconBgColorIn(vs ...string) predicate.Workflow {
	return predicate.Workflow(sql.FieldIn(FieldIconBgColor, vs...))
}

// IconBgColorNotIn applies the NotIn predicate on the "icon_bg_color" field.
func IconBgColorNotIn(vs ...string) predicate.Workflow {
	return predicate.Workflow(sql.FieldNotIn(FieldIconBgColor, vs...))
}

// IconBgColorGT applies the GT predicate on the "icon_bg_color" field.
func IconBgColorGT(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldGT(FieldIconBgColor, v))
}

// IconBgColorGTE applies the GTE predicate on the "icon_bg_color" field.
func IconBgColorGTE(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldGTE(FieldIconBgColor, v))
}

// IconBgColorLT applies the LT predicate on the "icon_bg_color" field.
func IconBgColorLT(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldLT(FieldIconBgColor, v))
}

// IconBgColorLTE applies the LTE predicate on the "icon_bg_color" field.
func IconBgColorLTE(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldLTE(FieldIconBgColor, v))
}

// IconBgColorContains applies the Contains predicate on the "icon_bg_color" field.
func IconBgColorContains(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldContains(FieldIconBgColor, v))
}

// IconBgColorHasPrefix applies the HasPrefix predicate on the "icon_bg_color" field.
func IconBgColorHasPrefix(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldHasPrefix(FieldIconBgColor, v))
}

// IconBgColorHasSuffix applies the HasSuffix predicate on the "icon_bg_color" field.
func IconBgColorHasSuffix(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldHasSuffix(FieldIconBgColor, v))
}

// IconBgColorEqualFold applies the EqualFold predicate on the "icon_bg_color" field.
func IconBgColorEqualFold(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEqualFold(FieldIconBgColor, v))
}

// IconBgColorContainsFold applies the ContainsFold predicate on the "icon_bg_color" field.
func IconBgColorContainsFold(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldContainsFold(FieldIconBgColor, v))
}

// IconDataEQ applies the EQ predicate on the "icon_data" field.
func IconDataEQ(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldIconData, v))
}

// IconDataNEQ applies the NEQ predicate on the "icon_data" field.
func IconDataNEQ(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldNEQ(FieldIconData, v))
}

// IconDataIn applies the In predicate on the "icon_data" field.
func IconDataIn(vs ...string) predicate.Workflow {
	return predicate.Workflow(sql.FieldIn(FieldIconData, vs...))
}

// IconDataNotIn applies the NotIn predicate on the "icon_data" field.
func IconDataNotIn(vs ...string) predicate.Workflow {
	return predicate.Workflow(sql.FieldNotIn(FieldIconData, vs...))
}

// IconDataGT applies the GT predicate on the "icon_data" field.
func IconDataGT(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldGT(FieldIconData, v))
}

// IconDataGTE applies the GTE predicate on the "icon_data" field.
func IconDataGTE(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldGTE(FieldIconData, v))
}

// IconDataLT applies the LT predicate on the "icon_data" field.
func IconDataLT(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldLT(FieldIconData, v))
}

// IconDataLTE applies the LTE predicate on the "icon_data" field.
func IconDataLTE(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldLTE(FieldIconData, v))
}

// IconDataContains applies the Contains predicate on the "icon_data" field.
func IconDataContains(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldContains(FieldIconData, v))
}

// IconDataHasPrefix applies the HasPrefix predicate on the "icon_data" field.
func IconDataHasPrefix(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldHasPrefix(FieldIconData, v))
}

// IconDataHasSuffix applies the HasSuffix predicate on the "icon_data" field.
func IconDataHasSuffix(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldHasSuffix(FieldIconData, v))
}

// IconDataEqualFold applies the EqualFold predicate on the "icon_data" field.
func IconDataEqualFold(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEqualFold(FieldIconData, v))
}

// IconDataContainsFold applies the ContainsFold predicate on the "icon_data" field.
func IconDataContainsFold(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldContainsFold(FieldIconData, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.Workflow {
	return predicate.Workflow(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.Workflow {
	return predicate.Workflow(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldContainsFold(FieldDescription, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v int) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v int) predicate.Workflow {
	return predicate.Workflow(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...int) predicate.Workflow {
	return predicate.Workflow(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...int) predicate.Workflow {
	return predicate.Workflow(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v int) predicate.Workflow {
	return predicate.Workflow(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v int) predicate.Workflow {
	return predicate.Workflow(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v int) predicate.Workflow {
	return predicate.Workflow(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v int) predicate.Workflow {
	return predicate.Workflow(sql.FieldLTE(FieldStatus, v))
}

// ViewportEQ applies the EQ predicate on the "viewport" field.
func ViewportEQ(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldViewport, v))
}

// ViewportNEQ applies the NEQ predicate on the "viewport" field.
func ViewportNEQ(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldNEQ(FieldViewport, v))
}

// ViewportIn applies the In predicate on the "viewport" field.
func ViewportIn(vs ...string) predicate.Workflow {
	return predicate.Workflow(sql.FieldIn(FieldViewport, vs...))
}

// ViewportNotIn applies the NotIn predicate on the "viewport" field.
func ViewportNotIn(vs ...string) predicate.Workflow {
	return predicate.Workflow(sql.FieldNotIn(FieldViewport, vs...))
}

// ViewportGT applies the GT predicate on the "viewport" field.
func ViewportGT(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldGT(FieldViewport, v))
}

// ViewportGTE applies the GTE predicate on the "viewport" field.
func ViewportGTE(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldGTE(FieldViewport, v))
}

// ViewportLT applies the LT predicate on the "viewport" field.
func ViewportLT(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldLT(FieldViewport, v))
}

// ViewportLTE applies the LTE predicate on the "viewport" field.
func ViewportLTE(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldLTE(FieldViewport, v))
}

// ViewportContains applies the Contains predicate on the "viewport" field.
func ViewportContains(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldContains(FieldViewport, v))
}

// ViewportHasPrefix applies the HasPrefix predicate on the "viewport" field.
func ViewportHasPrefix(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldHasPrefix(FieldViewport, v))
}

// ViewportHasSuffix applies the HasSuffix predicate on the "viewport" field.
func ViewportHasSuffix(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldHasSuffix(FieldViewport, v))
}

// ViewportEqualFold applies the EqualFold predicate on the "viewport" field.
func ViewportEqualFold(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldEqualFold(FieldViewport, v))
}

// ViewportContainsFold applies the ContainsFold predicate on the "viewport" field.
func ViewportContainsFold(v string) predicate.Workflow {
	return predicate.Workflow(sql.FieldContainsFold(FieldViewport, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Workflow {
	return predicate.Workflow(sql.FieldLTE(FieldUpdatedAt, v))
}

// HasUser applies the HasEdge predicate on the "user" edge.
func HasUser() predicate.Workflow {
	return predicate.Workflow(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, UserTable, UserColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasUserWith applies the HasEdge predicate on the "user" edge with a given conditions (other predicates).
func HasUserWith(preds ...predicate.User) predicate.Workflow {
	return predicate.Workflow(func(s *sql.Selector) {
		step := newUserStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasNodes applies the HasEdge predicate on the "nodes" edge.
func HasNodes() predicate.Workflow {
	return predicate.Workflow(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, NodesTable, NodesColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasNodesWith applies the HasEdge predicate on the "nodes" edge with a given conditions (other predicates).
func HasNodesWith(preds ...predicate.WorkflowNode) predicate.Workflow {
	return predicate.Workflow(func(s *sql.Selector) {
		step := newNodesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasWorkflowEdges applies the HasEdge predicate on the "workflow_edges" edge.
func HasWorkflowEdges() predicate.Workflow {
	return predicate.Workflow(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, WorkflowEdgesTable, WorkflowEdgesColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasWorkflowEdgesWith applies the HasEdge predicate on the "workflow_edges" edge with a given conditions (other predicates).
func HasWorkflowEdgesWith(preds ...predicate.WorkflowsEdge) predicate.Workflow {
	return predicate.Workflow(func(s *sql.Selector) {
		step := newWorkflowEdgesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Workflow) predicate.Workflow {
	return predicate.Workflow(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Workflow) predicate.Workflow {
	return predicate.Workflow(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Workflow) predicate.Workflow {
	return predicate.Workflow(sql.NotPredicates(p))
}
