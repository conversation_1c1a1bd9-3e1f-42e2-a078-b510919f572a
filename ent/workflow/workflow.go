// Code generated by ent, DO NOT EDIT.

package workflow

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the workflow type in the database.
	Label = "workflow"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldIconType holds the string denoting the icon_type field in the database.
	FieldIconType = "icon_type"
	// FieldIconBgColor holds the string denoting the icon_bg_color field in the database.
	FieldIconBgColor = "icon_bg_color"
	// FieldIconData holds the string denoting the icon_data field in the database.
	FieldIconData = "icon_data"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldViewport holds the string denoting the viewport field in the database.
	FieldViewport = "viewport"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// EdgeUser holds the string denoting the user edge name in mutations.
	EdgeUser = "user"
	// EdgeNodes holds the string denoting the nodes edge name in mutations.
	EdgeNodes = "nodes"
	// EdgeWorkflowEdges holds the string denoting the workflow_edges edge name in mutations.
	EdgeWorkflowEdges = "workflow_edges"
	// Table holds the table name of the workflow in the database.
	Table = "workflows"
	// UserTable is the table that holds the user relation/edge.
	UserTable = "workflows"
	// UserInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	UserInverseTable = "users"
	// UserColumn is the table column denoting the user relation/edge.
	UserColumn = "user_id"
	// NodesTable is the table that holds the nodes relation/edge.
	NodesTable = "workflow_nodes"
	// NodesInverseTable is the table name for the WorkflowNode entity.
	// It exists in this package in order to avoid circular dependency with the "workflownode" package.
	NodesInverseTable = "workflow_nodes"
	// NodesColumn is the table column denoting the nodes relation/edge.
	NodesColumn = "workflow_id"
	// WorkflowEdgesTable is the table that holds the workflow_edges relation/edge.
	WorkflowEdgesTable = "workflows_edges"
	// WorkflowEdgesInverseTable is the table name for the WorkflowsEdge entity.
	// It exists in this package in order to avoid circular dependency with the "workflowsedge" package.
	WorkflowEdgesInverseTable = "workflows_edges"
	// WorkflowEdgesColumn is the table column denoting the workflow_edges relation/edge.
	WorkflowEdgesColumn = "workflow_id"
)

// Columns holds all SQL columns for workflow fields.
var Columns = []string{
	FieldID,
	FieldUserID,
	FieldName,
	FieldIconType,
	FieldIconBgColor,
	FieldIconData,
	FieldDescription,
	FieldStatus,
	FieldViewport,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
)

// OrderOption defines the ordering options for the Workflow queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByIconType orders the results by the icon_type field.
func ByIconType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIconType, opts...).ToFunc()
}

// ByIconBgColor orders the results by the icon_bg_color field.
func ByIconBgColor(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIconBgColor, opts...).ToFunc()
}

// ByIconData orders the results by the icon_data field.
func ByIconData(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIconData, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByViewport orders the results by the viewport field.
func ByViewport(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldViewport, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByUserField orders the results by user field.
func ByUserField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newUserStep(), sql.OrderByField(field, opts...))
	}
}

// ByNodesCount orders the results by nodes count.
func ByNodesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newNodesStep(), opts...)
	}
}

// ByNodes orders the results by nodes terms.
func ByNodes(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newNodesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByWorkflowEdgesCount orders the results by workflow_edges count.
func ByWorkflowEdgesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newWorkflowEdgesStep(), opts...)
	}
}

// ByWorkflowEdges orders the results by workflow_edges terms.
func ByWorkflowEdges(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newWorkflowEdgesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newUserStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(UserInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, UserTable, UserColumn),
	)
}
func newNodesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(NodesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, NodesTable, NodesColumn),
	)
}
func newWorkflowEdgesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(WorkflowEdgesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, WorkflowEdgesTable, WorkflowEdgesColumn),
	)
}
