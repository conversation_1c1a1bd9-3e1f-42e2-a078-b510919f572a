// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"resflow/ent/plugin"
	"resflow/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PluginUpdate is the builder for updating Plugin entities.
type PluginUpdate struct {
	config
	hooks    []Hook
	mutation *PluginMutation
}

// Where appends a list predicates to the PluginUpdate builder.
func (pu *PluginUpdate) Where(ps ...predicate.Plugin) *PluginUpdate {
	pu.mutation.Where(ps...)
	return pu
}

// SetName sets the "name" field.
func (pu *PluginUpdate) SetName(s string) *PluginUpdate {
	pu.mutation.SetName(s)
	return pu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (pu *PluginUpdate) SetNillableName(s *string) *PluginUpdate {
	if s != nil {
		pu.SetName(*s)
	}
	return pu
}

// SetVersion sets the "version" field.
func (pu *PluginUpdate) SetVersion(s string) *PluginUpdate {
	pu.mutation.SetVersion(s)
	return pu
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (pu *PluginUpdate) SetNillableVersion(s *string) *PluginUpdate {
	if s != nil {
		pu.SetVersion(*s)
	}
	return pu
}

// SetAuthor sets the "author" field.
func (pu *PluginUpdate) SetAuthor(s string) *PluginUpdate {
	pu.mutation.SetAuthor(s)
	return pu
}

// SetNillableAuthor sets the "author" field if the given value is not nil.
func (pu *PluginUpdate) SetNillableAuthor(s *string) *PluginUpdate {
	if s != nil {
		pu.SetAuthor(*s)
	}
	return pu
}

// SetDisplayName sets the "display_name" field.
func (pu *PluginUpdate) SetDisplayName(s string) *PluginUpdate {
	pu.mutation.SetDisplayName(s)
	return pu
}

// SetNillableDisplayName sets the "display_name" field if the given value is not nil.
func (pu *PluginUpdate) SetNillableDisplayName(s *string) *PluginUpdate {
	if s != nil {
		pu.SetDisplayName(*s)
	}
	return pu
}

// SetDescription sets the "description" field.
func (pu *PluginUpdate) SetDescription(s string) *PluginUpdate {
	pu.mutation.SetDescription(s)
	return pu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (pu *PluginUpdate) SetNillableDescription(s *string) *PluginUpdate {
	if s != nil {
		pu.SetDescription(*s)
	}
	return pu
}

// SetIcon sets the "icon" field.
func (pu *PluginUpdate) SetIcon(s string) *PluginUpdate {
	pu.mutation.SetIcon(s)
	return pu
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (pu *PluginUpdate) SetNillableIcon(s *string) *PluginUpdate {
	if s != nil {
		pu.SetIcon(*s)
	}
	return pu
}

// SetPath sets the "path" field.
func (pu *PluginUpdate) SetPath(s string) *PluginUpdate {
	pu.mutation.SetPath(s)
	return pu
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (pu *PluginUpdate) SetNillablePath(s *string) *PluginUpdate {
	if s != nil {
		pu.SetPath(*s)
	}
	return pu
}

// SetBuiltin sets the "builtin" field.
func (pu *PluginUpdate) SetBuiltin(b bool) *PluginUpdate {
	pu.mutation.SetBuiltin(b)
	return pu
}

// SetNillableBuiltin sets the "builtin" field if the given value is not nil.
func (pu *PluginUpdate) SetNillableBuiltin(b *bool) *PluginUpdate {
	if b != nil {
		pu.SetBuiltin(*b)
	}
	return pu
}

// SetEnabled sets the "enabled" field.
func (pu *PluginUpdate) SetEnabled(b bool) *PluginUpdate {
	pu.mutation.SetEnabled(b)
	return pu
}

// SetNillableEnabled sets the "enabled" field if the given value is not nil.
func (pu *PluginUpdate) SetNillableEnabled(b *bool) *PluginUpdate {
	if b != nil {
		pu.SetEnabled(*b)
	}
	return pu
}

// SetUpdatedAt sets the "updated_at" field.
func (pu *PluginUpdate) SetUpdatedAt(t time.Time) *PluginUpdate {
	pu.mutation.SetUpdatedAt(t)
	return pu
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (pu *PluginUpdate) SetNillableUpdatedAt(t *time.Time) *PluginUpdate {
	if t != nil {
		pu.SetUpdatedAt(*t)
	}
	return pu
}

// Mutation returns the PluginMutation object of the builder.
func (pu *PluginUpdate) Mutation() *PluginMutation {
	return pu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (pu *PluginUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, pu.sqlSave, pu.mutation, pu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (pu *PluginUpdate) SaveX(ctx context.Context) int {
	affected, err := pu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (pu *PluginUpdate) Exec(ctx context.Context) error {
	_, err := pu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pu *PluginUpdate) ExecX(ctx context.Context) {
	if err := pu.Exec(ctx); err != nil {
		panic(err)
	}
}

func (pu *PluginUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(plugin.Table, plugin.Columns, sqlgraph.NewFieldSpec(plugin.FieldID, field.TypeUUID))
	if ps := pu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := pu.mutation.Name(); ok {
		_spec.SetField(plugin.FieldName, field.TypeString, value)
	}
	if value, ok := pu.mutation.Version(); ok {
		_spec.SetField(plugin.FieldVersion, field.TypeString, value)
	}
	if value, ok := pu.mutation.Author(); ok {
		_spec.SetField(plugin.FieldAuthor, field.TypeString, value)
	}
	if value, ok := pu.mutation.DisplayName(); ok {
		_spec.SetField(plugin.FieldDisplayName, field.TypeString, value)
	}
	if value, ok := pu.mutation.Description(); ok {
		_spec.SetField(plugin.FieldDescription, field.TypeString, value)
	}
	if value, ok := pu.mutation.Icon(); ok {
		_spec.SetField(plugin.FieldIcon, field.TypeString, value)
	}
	if value, ok := pu.mutation.Path(); ok {
		_spec.SetField(plugin.FieldPath, field.TypeString, value)
	}
	if value, ok := pu.mutation.Builtin(); ok {
		_spec.SetField(plugin.FieldBuiltin, field.TypeBool, value)
	}
	if value, ok := pu.mutation.Enabled(); ok {
		_spec.SetField(plugin.FieldEnabled, field.TypeBool, value)
	}
	if value, ok := pu.mutation.UpdatedAt(); ok {
		_spec.SetField(plugin.FieldUpdatedAt, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, pu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{plugin.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	pu.mutation.done = true
	return n, nil
}

// PluginUpdateOne is the builder for updating a single Plugin entity.
type PluginUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *PluginMutation
}

// SetName sets the "name" field.
func (puo *PluginUpdateOne) SetName(s string) *PluginUpdateOne {
	puo.mutation.SetName(s)
	return puo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (puo *PluginUpdateOne) SetNillableName(s *string) *PluginUpdateOne {
	if s != nil {
		puo.SetName(*s)
	}
	return puo
}

// SetVersion sets the "version" field.
func (puo *PluginUpdateOne) SetVersion(s string) *PluginUpdateOne {
	puo.mutation.SetVersion(s)
	return puo
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (puo *PluginUpdateOne) SetNillableVersion(s *string) *PluginUpdateOne {
	if s != nil {
		puo.SetVersion(*s)
	}
	return puo
}

// SetAuthor sets the "author" field.
func (puo *PluginUpdateOne) SetAuthor(s string) *PluginUpdateOne {
	puo.mutation.SetAuthor(s)
	return puo
}

// SetNillableAuthor sets the "author" field if the given value is not nil.
func (puo *PluginUpdateOne) SetNillableAuthor(s *string) *PluginUpdateOne {
	if s != nil {
		puo.SetAuthor(*s)
	}
	return puo
}

// SetDisplayName sets the "display_name" field.
func (puo *PluginUpdateOne) SetDisplayName(s string) *PluginUpdateOne {
	puo.mutation.SetDisplayName(s)
	return puo
}

// SetNillableDisplayName sets the "display_name" field if the given value is not nil.
func (puo *PluginUpdateOne) SetNillableDisplayName(s *string) *PluginUpdateOne {
	if s != nil {
		puo.SetDisplayName(*s)
	}
	return puo
}

// SetDescription sets the "description" field.
func (puo *PluginUpdateOne) SetDescription(s string) *PluginUpdateOne {
	puo.mutation.SetDescription(s)
	return puo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (puo *PluginUpdateOne) SetNillableDescription(s *string) *PluginUpdateOne {
	if s != nil {
		puo.SetDescription(*s)
	}
	return puo
}

// SetIcon sets the "icon" field.
func (puo *PluginUpdateOne) SetIcon(s string) *PluginUpdateOne {
	puo.mutation.SetIcon(s)
	return puo
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (puo *PluginUpdateOne) SetNillableIcon(s *string) *PluginUpdateOne {
	if s != nil {
		puo.SetIcon(*s)
	}
	return puo
}

// SetPath sets the "path" field.
func (puo *PluginUpdateOne) SetPath(s string) *PluginUpdateOne {
	puo.mutation.SetPath(s)
	return puo
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (puo *PluginUpdateOne) SetNillablePath(s *string) *PluginUpdateOne {
	if s != nil {
		puo.SetPath(*s)
	}
	return puo
}

// SetBuiltin sets the "builtin" field.
func (puo *PluginUpdateOne) SetBuiltin(b bool) *PluginUpdateOne {
	puo.mutation.SetBuiltin(b)
	return puo
}

// SetNillableBuiltin sets the "builtin" field if the given value is not nil.
func (puo *PluginUpdateOne) SetNillableBuiltin(b *bool) *PluginUpdateOne {
	if b != nil {
		puo.SetBuiltin(*b)
	}
	return puo
}

// SetEnabled sets the "enabled" field.
func (puo *PluginUpdateOne) SetEnabled(b bool) *PluginUpdateOne {
	puo.mutation.SetEnabled(b)
	return puo
}

// SetNillableEnabled sets the "enabled" field if the given value is not nil.
func (puo *PluginUpdateOne) SetNillableEnabled(b *bool) *PluginUpdateOne {
	if b != nil {
		puo.SetEnabled(*b)
	}
	return puo
}

// SetUpdatedAt sets the "updated_at" field.
func (puo *PluginUpdateOne) SetUpdatedAt(t time.Time) *PluginUpdateOne {
	puo.mutation.SetUpdatedAt(t)
	return puo
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (puo *PluginUpdateOne) SetNillableUpdatedAt(t *time.Time) *PluginUpdateOne {
	if t != nil {
		puo.SetUpdatedAt(*t)
	}
	return puo
}

// Mutation returns the PluginMutation object of the builder.
func (puo *PluginUpdateOne) Mutation() *PluginMutation {
	return puo.mutation
}

// Where appends a list predicates to the PluginUpdate builder.
func (puo *PluginUpdateOne) Where(ps ...predicate.Plugin) *PluginUpdateOne {
	puo.mutation.Where(ps...)
	return puo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (puo *PluginUpdateOne) Select(field string, fields ...string) *PluginUpdateOne {
	puo.fields = append([]string{field}, fields...)
	return puo
}

// Save executes the query and returns the updated Plugin entity.
func (puo *PluginUpdateOne) Save(ctx context.Context) (*Plugin, error) {
	return withHooks(ctx, puo.sqlSave, puo.mutation, puo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (puo *PluginUpdateOne) SaveX(ctx context.Context) *Plugin {
	node, err := puo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (puo *PluginUpdateOne) Exec(ctx context.Context) error {
	_, err := puo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (puo *PluginUpdateOne) ExecX(ctx context.Context) {
	if err := puo.Exec(ctx); err != nil {
		panic(err)
	}
}

func (puo *PluginUpdateOne) sqlSave(ctx context.Context) (_node *Plugin, err error) {
	_spec := sqlgraph.NewUpdateSpec(plugin.Table, plugin.Columns, sqlgraph.NewFieldSpec(plugin.FieldID, field.TypeUUID))
	id, ok := puo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Plugin.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := puo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, plugin.FieldID)
		for _, f := range fields {
			if !plugin.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != plugin.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := puo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := puo.mutation.Name(); ok {
		_spec.SetField(plugin.FieldName, field.TypeString, value)
	}
	if value, ok := puo.mutation.Version(); ok {
		_spec.SetField(plugin.FieldVersion, field.TypeString, value)
	}
	if value, ok := puo.mutation.Author(); ok {
		_spec.SetField(plugin.FieldAuthor, field.TypeString, value)
	}
	if value, ok := puo.mutation.DisplayName(); ok {
		_spec.SetField(plugin.FieldDisplayName, field.TypeString, value)
	}
	if value, ok := puo.mutation.Description(); ok {
		_spec.SetField(plugin.FieldDescription, field.TypeString, value)
	}
	if value, ok := puo.mutation.Icon(); ok {
		_spec.SetField(plugin.FieldIcon, field.TypeString, value)
	}
	if value, ok := puo.mutation.Path(); ok {
		_spec.SetField(plugin.FieldPath, field.TypeString, value)
	}
	if value, ok := puo.mutation.Builtin(); ok {
		_spec.SetField(plugin.FieldBuiltin, field.TypeBool, value)
	}
	if value, ok := puo.mutation.Enabled(); ok {
		_spec.SetField(plugin.FieldEnabled, field.TypeBool, value)
	}
	if value, ok := puo.mutation.UpdatedAt(); ok {
		_spec.SetField(plugin.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &Plugin{config: puo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, puo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{plugin.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	puo.mutation.done = true
	return _node, nil
}
