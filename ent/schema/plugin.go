package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// Plugin holds the schema definition for the Plugin entity.
type Plugin struct {
	ent.Schema
}

// Fields of the Plugin.
func (Plugin) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}),
		field.String("name"),
		field.String("version"),
		field.String("author"),
		field.String("display_name"),
		field.String("description"),
		field.String("icon"),
		field.String("path"),
		field.Bool("builtin"),
		field.Bool("enabled"),
		field.Time("created_at").Default(time.Now).Immutable(),
		field.Time("updated_at").Default(time.Now),
	}
}

// Edges of the Plugin.
func (Plugin) Edges() []ent.Edge {
	return nil
}

func (Plugin) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("name", "version").Unique(),
	}
}
