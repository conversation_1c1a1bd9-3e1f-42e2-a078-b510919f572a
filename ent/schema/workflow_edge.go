package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// WorkflowsEdge holds the schema definition for the WorkflowsEdge entity.
type WorkflowsEdge struct {
	ent.Schema
}

// Fields of the WorkflowsEdge.
func (WorkflowsEdge) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).Default(uuid.New),
		field.UUID("workflow_id", uuid.UUID{}),
		field.UUID("from_node_id", uuid.UUID{}),
		field.UUID("to_node_id", uuid.UUID{}),
		field.UUID("from_port_id", uuid.UUID{}),
		field.UUID("to_port_id", uuid.UUID{}),
		field.String("type"),
		field.Time("created_at").Default(time.Now).Immutable(),
		field.Time("updated_at").Default(time.Now),
	}
}

// Edges of the WorkflowsEdge.
func (WorkflowsEdge) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("workflow", Workflow.Type).Ref("workflow_edges").Unique().Field("workflow_id").Required(),
	}
}
