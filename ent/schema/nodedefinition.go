package schema

import (
	"resflow/internal/plugin/domain"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/google/uuid"
)

// NodeDefinition holds the schema definition for the NodeDefinition entity.
type NodeDefinition struct {
	ent.Schema
}

// Fields of the NodeDefinition.
func (NodeDefinition) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}),
		field.String("plugin_name"),
		field.String("name"),
		field.String("author"),
		field.String("description"),
		field.String("icon"),
		field.String("type"),
		field.String("version"),
		field.String("category"),
		field.JSON("input_params", []*domain.NodeParam{}),
		field.JSON("output_params", []*domain.NodeParam{}),
		field.JSON("input_ports", []*domain.NodePort{}),
		field.JSON("output_ports", []*domain.NodePort{}),
		field.Bool("exception"),
		field.String("path"),
		field.Bool("builtin"),
		field.Bool("enabled"),
		field.Time("created_at").Default(time.Now).Immutable(),
		field.Time("updated_at").Default(time.Now),
	}
}

// Edges of the NodeDefinition.
func (NodeDefinition) Edges() []ent.Edge {
	return nil
}

func (NodeDefinition) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("type", "version", "plugin_name").Unique(),
	}
}
