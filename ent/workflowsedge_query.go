// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"
	"resflow/ent/predicate"
	"resflow/ent/workflow"
	"resflow/ent/workflowsedge"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// WorkflowsEdgeQuery is the builder for querying WorkflowsEdge entities.
type WorkflowsEdgeQuery struct {
	config
	ctx          *QueryContext
	order        []workflowsedge.OrderOption
	inters       []Interceptor
	predicates   []predicate.WorkflowsEdge
	withWorkflow *WorkflowQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the WorkflowsEdgeQuery builder.
func (weq *WorkflowsEdgeQuery) Where(ps ...predicate.WorkflowsEdge) *WorkflowsEdgeQuery {
	weq.predicates = append(weq.predicates, ps...)
	return weq
}

// Limit the number of records to be returned by this query.
func (weq *WorkflowsEdgeQuery) Limit(limit int) *WorkflowsEdgeQuery {
	weq.ctx.Limit = &limit
	return weq
}

// Offset to start from.
func (weq *WorkflowsEdgeQuery) Offset(offset int) *WorkflowsEdgeQuery {
	weq.ctx.Offset = &offset
	return weq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (weq *WorkflowsEdgeQuery) Unique(unique bool) *WorkflowsEdgeQuery {
	weq.ctx.Unique = &unique
	return weq
}

// Order specifies how the records should be ordered.
func (weq *WorkflowsEdgeQuery) Order(o ...workflowsedge.OrderOption) *WorkflowsEdgeQuery {
	weq.order = append(weq.order, o...)
	return weq
}

// QueryWorkflow chains the current query on the "workflow" edge.
func (weq *WorkflowsEdgeQuery) QueryWorkflow() *WorkflowQuery {
	query := (&WorkflowClient{config: weq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := weq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := weq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(workflowsedge.Table, workflowsedge.FieldID, selector),
			sqlgraph.To(workflow.Table, workflow.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, workflowsedge.WorkflowTable, workflowsedge.WorkflowColumn),
		)
		fromU = sqlgraph.SetNeighbors(weq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first WorkflowsEdge entity from the query.
// Returns a *NotFoundError when no WorkflowsEdge was found.
func (weq *WorkflowsEdgeQuery) First(ctx context.Context) (*WorkflowsEdge, error) {
	nodes, err := weq.Limit(1).All(setContextOp(ctx, weq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{workflowsedge.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (weq *WorkflowsEdgeQuery) FirstX(ctx context.Context) *WorkflowsEdge {
	node, err := weq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first WorkflowsEdge ID from the query.
// Returns a *NotFoundError when no WorkflowsEdge ID was found.
func (weq *WorkflowsEdgeQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = weq.Limit(1).IDs(setContextOp(ctx, weq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{workflowsedge.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (weq *WorkflowsEdgeQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := weq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single WorkflowsEdge entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one WorkflowsEdge entity is found.
// Returns a *NotFoundError when no WorkflowsEdge entities are found.
func (weq *WorkflowsEdgeQuery) Only(ctx context.Context) (*WorkflowsEdge, error) {
	nodes, err := weq.Limit(2).All(setContextOp(ctx, weq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{workflowsedge.Label}
	default:
		return nil, &NotSingularError{workflowsedge.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (weq *WorkflowsEdgeQuery) OnlyX(ctx context.Context) *WorkflowsEdge {
	node, err := weq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only WorkflowsEdge ID in the query.
// Returns a *NotSingularError when more than one WorkflowsEdge ID is found.
// Returns a *NotFoundError when no entities are found.
func (weq *WorkflowsEdgeQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = weq.Limit(2).IDs(setContextOp(ctx, weq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{workflowsedge.Label}
	default:
		err = &NotSingularError{workflowsedge.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (weq *WorkflowsEdgeQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := weq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of WorkflowsEdges.
func (weq *WorkflowsEdgeQuery) All(ctx context.Context) ([]*WorkflowsEdge, error) {
	ctx = setContextOp(ctx, weq.ctx, ent.OpQueryAll)
	if err := weq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*WorkflowsEdge, *WorkflowsEdgeQuery]()
	return withInterceptors[[]*WorkflowsEdge](ctx, weq, qr, weq.inters)
}

// AllX is like All, but panics if an error occurs.
func (weq *WorkflowsEdgeQuery) AllX(ctx context.Context) []*WorkflowsEdge {
	nodes, err := weq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of WorkflowsEdge IDs.
func (weq *WorkflowsEdgeQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if weq.ctx.Unique == nil && weq.path != nil {
		weq.Unique(true)
	}
	ctx = setContextOp(ctx, weq.ctx, ent.OpQueryIDs)
	if err = weq.Select(workflowsedge.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (weq *WorkflowsEdgeQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := weq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (weq *WorkflowsEdgeQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, weq.ctx, ent.OpQueryCount)
	if err := weq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, weq, querierCount[*WorkflowsEdgeQuery](), weq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (weq *WorkflowsEdgeQuery) CountX(ctx context.Context) int {
	count, err := weq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (weq *WorkflowsEdgeQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, weq.ctx, ent.OpQueryExist)
	switch _, err := weq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (weq *WorkflowsEdgeQuery) ExistX(ctx context.Context) bool {
	exist, err := weq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the WorkflowsEdgeQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (weq *WorkflowsEdgeQuery) Clone() *WorkflowsEdgeQuery {
	if weq == nil {
		return nil
	}
	return &WorkflowsEdgeQuery{
		config:       weq.config,
		ctx:          weq.ctx.Clone(),
		order:        append([]workflowsedge.OrderOption{}, weq.order...),
		inters:       append([]Interceptor{}, weq.inters...),
		predicates:   append([]predicate.WorkflowsEdge{}, weq.predicates...),
		withWorkflow: weq.withWorkflow.Clone(),
		// clone intermediate query.
		sql:  weq.sql.Clone(),
		path: weq.path,
	}
}

// WithWorkflow tells the query-builder to eager-load the nodes that are connected to
// the "workflow" edge. The optional arguments are used to configure the query builder of the edge.
func (weq *WorkflowsEdgeQuery) WithWorkflow(opts ...func(*WorkflowQuery)) *WorkflowsEdgeQuery {
	query := (&WorkflowClient{config: weq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	weq.withWorkflow = query
	return weq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		WorkflowID uuid.UUID `json:"workflow_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.WorkflowsEdge.Query().
//		GroupBy(workflowsedge.FieldWorkflowID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (weq *WorkflowsEdgeQuery) GroupBy(field string, fields ...string) *WorkflowsEdgeGroupBy {
	weq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &WorkflowsEdgeGroupBy{build: weq}
	grbuild.flds = &weq.ctx.Fields
	grbuild.label = workflowsedge.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		WorkflowID uuid.UUID `json:"workflow_id,omitempty"`
//	}
//
//	client.WorkflowsEdge.Query().
//		Select(workflowsedge.FieldWorkflowID).
//		Scan(ctx, &v)
func (weq *WorkflowsEdgeQuery) Select(fields ...string) *WorkflowsEdgeSelect {
	weq.ctx.Fields = append(weq.ctx.Fields, fields...)
	sbuild := &WorkflowsEdgeSelect{WorkflowsEdgeQuery: weq}
	sbuild.label = workflowsedge.Label
	sbuild.flds, sbuild.scan = &weq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a WorkflowsEdgeSelect configured with the given aggregations.
func (weq *WorkflowsEdgeQuery) Aggregate(fns ...AggregateFunc) *WorkflowsEdgeSelect {
	return weq.Select().Aggregate(fns...)
}

func (weq *WorkflowsEdgeQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range weq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, weq); err != nil {
				return err
			}
		}
	}
	for _, f := range weq.ctx.Fields {
		if !workflowsedge.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if weq.path != nil {
		prev, err := weq.path(ctx)
		if err != nil {
			return err
		}
		weq.sql = prev
	}
	return nil
}

func (weq *WorkflowsEdgeQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*WorkflowsEdge, error) {
	var (
		nodes       = []*WorkflowsEdge{}
		_spec       = weq.querySpec()
		loadedTypes = [1]bool{
			weq.withWorkflow != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*WorkflowsEdge).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &WorkflowsEdge{config: weq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, weq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := weq.withWorkflow; query != nil {
		if err := weq.loadWorkflow(ctx, query, nodes, nil,
			func(n *WorkflowsEdge, e *Workflow) { n.Edges.Workflow = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (weq *WorkflowsEdgeQuery) loadWorkflow(ctx context.Context, query *WorkflowQuery, nodes []*WorkflowsEdge, init func(*WorkflowsEdge), assign func(*WorkflowsEdge, *Workflow)) error {
	ids := make([]uuid.UUID, 0, len(nodes))
	nodeids := make(map[uuid.UUID][]*WorkflowsEdge)
	for i := range nodes {
		fk := nodes[i].WorkflowID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(workflow.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "workflow_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (weq *WorkflowsEdgeQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := weq.querySpec()
	_spec.Node.Columns = weq.ctx.Fields
	if len(weq.ctx.Fields) > 0 {
		_spec.Unique = weq.ctx.Unique != nil && *weq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, weq.driver, _spec)
}

func (weq *WorkflowsEdgeQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(workflowsedge.Table, workflowsedge.Columns, sqlgraph.NewFieldSpec(workflowsedge.FieldID, field.TypeUUID))
	_spec.From = weq.sql
	if unique := weq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if weq.path != nil {
		_spec.Unique = true
	}
	if fields := weq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, workflowsedge.FieldID)
		for i := range fields {
			if fields[i] != workflowsedge.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if weq.withWorkflow != nil {
			_spec.Node.AddColumnOnce(workflowsedge.FieldWorkflowID)
		}
	}
	if ps := weq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := weq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := weq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := weq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (weq *WorkflowsEdgeQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(weq.driver.Dialect())
	t1 := builder.Table(workflowsedge.Table)
	columns := weq.ctx.Fields
	if len(columns) == 0 {
		columns = workflowsedge.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if weq.sql != nil {
		selector = weq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if weq.ctx.Unique != nil && *weq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range weq.predicates {
		p(selector)
	}
	for _, p := range weq.order {
		p(selector)
	}
	if offset := weq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := weq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// WorkflowsEdgeGroupBy is the group-by builder for WorkflowsEdge entities.
type WorkflowsEdgeGroupBy struct {
	selector
	build *WorkflowsEdgeQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (wegb *WorkflowsEdgeGroupBy) Aggregate(fns ...AggregateFunc) *WorkflowsEdgeGroupBy {
	wegb.fns = append(wegb.fns, fns...)
	return wegb
}

// Scan applies the selector query and scans the result into the given value.
func (wegb *WorkflowsEdgeGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, wegb.build.ctx, ent.OpQueryGroupBy)
	if err := wegb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WorkflowsEdgeQuery, *WorkflowsEdgeGroupBy](ctx, wegb.build, wegb, wegb.build.inters, v)
}

func (wegb *WorkflowsEdgeGroupBy) sqlScan(ctx context.Context, root *WorkflowsEdgeQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(wegb.fns))
	for _, fn := range wegb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*wegb.flds)+len(wegb.fns))
		for _, f := range *wegb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*wegb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := wegb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// WorkflowsEdgeSelect is the builder for selecting fields of WorkflowsEdge entities.
type WorkflowsEdgeSelect struct {
	*WorkflowsEdgeQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (wes *WorkflowsEdgeSelect) Aggregate(fns ...AggregateFunc) *WorkflowsEdgeSelect {
	wes.fns = append(wes.fns, fns...)
	return wes
}

// Scan applies the selector query and scans the result into the given value.
func (wes *WorkflowsEdgeSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, wes.ctx, ent.OpQuerySelect)
	if err := wes.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WorkflowsEdgeQuery, *WorkflowsEdgeSelect](ctx, wes.WorkflowsEdgeQuery, wes, wes.inters, v)
}

func (wes *WorkflowsEdgeSelect) sqlScan(ctx context.Context, root *WorkflowsEdgeQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(wes.fns))
	for _, fn := range wes.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*wes.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := wes.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
