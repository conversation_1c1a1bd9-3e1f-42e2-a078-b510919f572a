// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"resflow/ent/nodedefinition"
	"resflow/internal/plugin/domain"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// NodeDefinitionCreate is the builder for creating a NodeDefinition entity.
type NodeDefinitionCreate struct {
	config
	mutation *NodeDefinitionMutation
	hooks    []Hook
}

// SetPluginName sets the "plugin_name" field.
func (ndc *NodeDefinitionCreate) SetPluginName(s string) *NodeDefinitionCreate {
	ndc.mutation.SetPluginName(s)
	return ndc
}

// SetName sets the "name" field.
func (ndc *NodeDefinitionCreate) SetName(s string) *NodeDefinitionCreate {
	ndc.mutation.SetName(s)
	return ndc
}

// SetAuthor sets the "author" field.
func (ndc *NodeDefinitionCreate) SetAuthor(s string) *NodeDefinitionCreate {
	ndc.mutation.SetAuthor(s)
	return ndc
}

// SetDescription sets the "description" field.
func (ndc *NodeDefinitionCreate) SetDescription(s string) *NodeDefinitionCreate {
	ndc.mutation.SetDescription(s)
	return ndc
}

// SetIcon sets the "icon" field.
func (ndc *NodeDefinitionCreate) SetIcon(s string) *NodeDefinitionCreate {
	ndc.mutation.SetIcon(s)
	return ndc
}

// SetType sets the "type" field.
func (ndc *NodeDefinitionCreate) SetType(s string) *NodeDefinitionCreate {
	ndc.mutation.SetType(s)
	return ndc
}

// SetVersion sets the "version" field.
func (ndc *NodeDefinitionCreate) SetVersion(s string) *NodeDefinitionCreate {
	ndc.mutation.SetVersion(s)
	return ndc
}

// SetCategory sets the "category" field.
func (ndc *NodeDefinitionCreate) SetCategory(s string) *NodeDefinitionCreate {
	ndc.mutation.SetCategory(s)
	return ndc
}

// SetInputParams sets the "input_params" field.
func (ndc *NodeDefinitionCreate) SetInputParams(dp []*domain.NodeParam) *NodeDefinitionCreate {
	ndc.mutation.SetInputParams(dp)
	return ndc
}

// SetOutputParams sets the "output_params" field.
func (ndc *NodeDefinitionCreate) SetOutputParams(dp []*domain.NodeParam) *NodeDefinitionCreate {
	ndc.mutation.SetOutputParams(dp)
	return ndc
}

// SetInputPorts sets the "input_ports" field.
func (ndc *NodeDefinitionCreate) SetInputPorts(dp []*domain.NodePort) *NodeDefinitionCreate {
	ndc.mutation.SetInputPorts(dp)
	return ndc
}

// SetOutputPorts sets the "output_ports" field.
func (ndc *NodeDefinitionCreate) SetOutputPorts(dp []*domain.NodePort) *NodeDefinitionCreate {
	ndc.mutation.SetOutputPorts(dp)
	return ndc
}

// SetException sets the "exception" field.
func (ndc *NodeDefinitionCreate) SetException(b bool) *NodeDefinitionCreate {
	ndc.mutation.SetException(b)
	return ndc
}

// SetPath sets the "path" field.
func (ndc *NodeDefinitionCreate) SetPath(s string) *NodeDefinitionCreate {
	ndc.mutation.SetPath(s)
	return ndc
}

// SetBuiltin sets the "builtin" field.
func (ndc *NodeDefinitionCreate) SetBuiltin(b bool) *NodeDefinitionCreate {
	ndc.mutation.SetBuiltin(b)
	return ndc
}

// SetEnabled sets the "enabled" field.
func (ndc *NodeDefinitionCreate) SetEnabled(b bool) *NodeDefinitionCreate {
	ndc.mutation.SetEnabled(b)
	return ndc
}

// SetCreatedAt sets the "created_at" field.
func (ndc *NodeDefinitionCreate) SetCreatedAt(t time.Time) *NodeDefinitionCreate {
	ndc.mutation.SetCreatedAt(t)
	return ndc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ndc *NodeDefinitionCreate) SetNillableCreatedAt(t *time.Time) *NodeDefinitionCreate {
	if t != nil {
		ndc.SetCreatedAt(*t)
	}
	return ndc
}

// SetUpdatedAt sets the "updated_at" field.
func (ndc *NodeDefinitionCreate) SetUpdatedAt(t time.Time) *NodeDefinitionCreate {
	ndc.mutation.SetUpdatedAt(t)
	return ndc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ndc *NodeDefinitionCreate) SetNillableUpdatedAt(t *time.Time) *NodeDefinitionCreate {
	if t != nil {
		ndc.SetUpdatedAt(*t)
	}
	return ndc
}

// SetID sets the "id" field.
func (ndc *NodeDefinitionCreate) SetID(u uuid.UUID) *NodeDefinitionCreate {
	ndc.mutation.SetID(u)
	return ndc
}

// Mutation returns the NodeDefinitionMutation object of the builder.
func (ndc *NodeDefinitionCreate) Mutation() *NodeDefinitionMutation {
	return ndc.mutation
}

// Save creates the NodeDefinition in the database.
func (ndc *NodeDefinitionCreate) Save(ctx context.Context) (*NodeDefinition, error) {
	ndc.defaults()
	return withHooks(ctx, ndc.sqlSave, ndc.mutation, ndc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ndc *NodeDefinitionCreate) SaveX(ctx context.Context) *NodeDefinition {
	v, err := ndc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ndc *NodeDefinitionCreate) Exec(ctx context.Context) error {
	_, err := ndc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ndc *NodeDefinitionCreate) ExecX(ctx context.Context) {
	if err := ndc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ndc *NodeDefinitionCreate) defaults() {
	if _, ok := ndc.mutation.CreatedAt(); !ok {
		v := nodedefinition.DefaultCreatedAt()
		ndc.mutation.SetCreatedAt(v)
	}
	if _, ok := ndc.mutation.UpdatedAt(); !ok {
		v := nodedefinition.DefaultUpdatedAt()
		ndc.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ndc *NodeDefinitionCreate) check() error {
	if _, ok := ndc.mutation.PluginName(); !ok {
		return &ValidationError{Name: "plugin_name", err: errors.New(`ent: missing required field "NodeDefinition.plugin_name"`)}
	}
	if _, ok := ndc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "NodeDefinition.name"`)}
	}
	if _, ok := ndc.mutation.Author(); !ok {
		return &ValidationError{Name: "author", err: errors.New(`ent: missing required field "NodeDefinition.author"`)}
	}
	if _, ok := ndc.mutation.Description(); !ok {
		return &ValidationError{Name: "description", err: errors.New(`ent: missing required field "NodeDefinition.description"`)}
	}
	if _, ok := ndc.mutation.Icon(); !ok {
		return &ValidationError{Name: "icon", err: errors.New(`ent: missing required field "NodeDefinition.icon"`)}
	}
	if _, ok := ndc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "NodeDefinition.type"`)}
	}
	if _, ok := ndc.mutation.Version(); !ok {
		return &ValidationError{Name: "version", err: errors.New(`ent: missing required field "NodeDefinition.version"`)}
	}
	if _, ok := ndc.mutation.Category(); !ok {
		return &ValidationError{Name: "category", err: errors.New(`ent: missing required field "NodeDefinition.category"`)}
	}
	if _, ok := ndc.mutation.InputParams(); !ok {
		return &ValidationError{Name: "input_params", err: errors.New(`ent: missing required field "NodeDefinition.input_params"`)}
	}
	if _, ok := ndc.mutation.OutputParams(); !ok {
		return &ValidationError{Name: "output_params", err: errors.New(`ent: missing required field "NodeDefinition.output_params"`)}
	}
	if _, ok := ndc.mutation.InputPorts(); !ok {
		return &ValidationError{Name: "input_ports", err: errors.New(`ent: missing required field "NodeDefinition.input_ports"`)}
	}
	if _, ok := ndc.mutation.OutputPorts(); !ok {
		return &ValidationError{Name: "output_ports", err: errors.New(`ent: missing required field "NodeDefinition.output_ports"`)}
	}
	if _, ok := ndc.mutation.Exception(); !ok {
		return &ValidationError{Name: "exception", err: errors.New(`ent: missing required field "NodeDefinition.exception"`)}
	}
	if _, ok := ndc.mutation.Path(); !ok {
		return &ValidationError{Name: "path", err: errors.New(`ent: missing required field "NodeDefinition.path"`)}
	}
	if _, ok := ndc.mutation.Builtin(); !ok {
		return &ValidationError{Name: "builtin", err: errors.New(`ent: missing required field "NodeDefinition.builtin"`)}
	}
	if _, ok := ndc.mutation.Enabled(); !ok {
		return &ValidationError{Name: "enabled", err: errors.New(`ent: missing required field "NodeDefinition.enabled"`)}
	}
	if _, ok := ndc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "NodeDefinition.created_at"`)}
	}
	if _, ok := ndc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "NodeDefinition.updated_at"`)}
	}
	return nil
}

func (ndc *NodeDefinitionCreate) sqlSave(ctx context.Context) (*NodeDefinition, error) {
	if err := ndc.check(); err != nil {
		return nil, err
	}
	_node, _spec := ndc.createSpec()
	if err := sqlgraph.CreateNode(ctx, ndc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	ndc.mutation.id = &_node.ID
	ndc.mutation.done = true
	return _node, nil
}

func (ndc *NodeDefinitionCreate) createSpec() (*NodeDefinition, *sqlgraph.CreateSpec) {
	var (
		_node = &NodeDefinition{config: ndc.config}
		_spec = sqlgraph.NewCreateSpec(nodedefinition.Table, sqlgraph.NewFieldSpec(nodedefinition.FieldID, field.TypeUUID))
	)
	if id, ok := ndc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := ndc.mutation.PluginName(); ok {
		_spec.SetField(nodedefinition.FieldPluginName, field.TypeString, value)
		_node.PluginName = value
	}
	if value, ok := ndc.mutation.Name(); ok {
		_spec.SetField(nodedefinition.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := ndc.mutation.Author(); ok {
		_spec.SetField(nodedefinition.FieldAuthor, field.TypeString, value)
		_node.Author = value
	}
	if value, ok := ndc.mutation.Description(); ok {
		_spec.SetField(nodedefinition.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := ndc.mutation.Icon(); ok {
		_spec.SetField(nodedefinition.FieldIcon, field.TypeString, value)
		_node.Icon = value
	}
	if value, ok := ndc.mutation.GetType(); ok {
		_spec.SetField(nodedefinition.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := ndc.mutation.Version(); ok {
		_spec.SetField(nodedefinition.FieldVersion, field.TypeString, value)
		_node.Version = value
	}
	if value, ok := ndc.mutation.Category(); ok {
		_spec.SetField(nodedefinition.FieldCategory, field.TypeString, value)
		_node.Category = value
	}
	if value, ok := ndc.mutation.InputParams(); ok {
		_spec.SetField(nodedefinition.FieldInputParams, field.TypeJSON, value)
		_node.InputParams = value
	}
	if value, ok := ndc.mutation.OutputParams(); ok {
		_spec.SetField(nodedefinition.FieldOutputParams, field.TypeJSON, value)
		_node.OutputParams = value
	}
	if value, ok := ndc.mutation.InputPorts(); ok {
		_spec.SetField(nodedefinition.FieldInputPorts, field.TypeJSON, value)
		_node.InputPorts = value
	}
	if value, ok := ndc.mutation.OutputPorts(); ok {
		_spec.SetField(nodedefinition.FieldOutputPorts, field.TypeJSON, value)
		_node.OutputPorts = value
	}
	if value, ok := ndc.mutation.Exception(); ok {
		_spec.SetField(nodedefinition.FieldException, field.TypeBool, value)
		_node.Exception = value
	}
	if value, ok := ndc.mutation.Path(); ok {
		_spec.SetField(nodedefinition.FieldPath, field.TypeString, value)
		_node.Path = value
	}
	if value, ok := ndc.mutation.Builtin(); ok {
		_spec.SetField(nodedefinition.FieldBuiltin, field.TypeBool, value)
		_node.Builtin = value
	}
	if value, ok := ndc.mutation.Enabled(); ok {
		_spec.SetField(nodedefinition.FieldEnabled, field.TypeBool, value)
		_node.Enabled = value
	}
	if value, ok := ndc.mutation.CreatedAt(); ok {
		_spec.SetField(nodedefinition.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ndc.mutation.UpdatedAt(); ok {
		_spec.SetField(nodedefinition.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	return _node, _spec
}

// NodeDefinitionCreateBulk is the builder for creating many NodeDefinition entities in bulk.
type NodeDefinitionCreateBulk struct {
	config
	err      error
	builders []*NodeDefinitionCreate
}

// Save creates the NodeDefinition entities in the database.
func (ndcb *NodeDefinitionCreateBulk) Save(ctx context.Context) ([]*NodeDefinition, error) {
	if ndcb.err != nil {
		return nil, ndcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ndcb.builders))
	nodes := make([]*NodeDefinition, len(ndcb.builders))
	mutators := make([]Mutator, len(ndcb.builders))
	for i := range ndcb.builders {
		func(i int, root context.Context) {
			builder := ndcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*NodeDefinitionMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ndcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ndcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ndcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ndcb *NodeDefinitionCreateBulk) SaveX(ctx context.Context) []*NodeDefinition {
	v, err := ndcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ndcb *NodeDefinitionCreateBulk) Exec(ctx context.Context) error {
	_, err := ndcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ndcb *NodeDefinitionCreateBulk) ExecX(ctx context.Context) {
	if err := ndcb.Exec(ctx); err != nil {
		panic(err)
	}
}
