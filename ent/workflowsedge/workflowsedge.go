// Code generated by ent, DO NOT EDIT.

package workflowsedge

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the workflowsedge type in the database.
	Label = "workflows_edge"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldWorkflowID holds the string denoting the workflow_id field in the database.
	FieldWorkflowID = "workflow_id"
	// FieldFromNodeID holds the string denoting the from_node_id field in the database.
	FieldFromNodeID = "from_node_id"
	// FieldToNodeID holds the string denoting the to_node_id field in the database.
	FieldToNodeID = "to_node_id"
	// FieldFromPortID holds the string denoting the from_port_id field in the database.
	FieldFromPortID = "from_port_id"
	// FieldToPortID holds the string denoting the to_port_id field in the database.
	FieldToPortID = "to_port_id"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// EdgeWorkflow holds the string denoting the workflow edge name in mutations.
	EdgeWorkflow = "workflow"
	// Table holds the table name of the workflowsedge in the database.
	Table = "workflows_edges"
	// WorkflowTable is the table that holds the workflow relation/edge.
	WorkflowTable = "workflows_edges"
	// WorkflowInverseTable is the table name for the Workflow entity.
	// It exists in this package in order to avoid circular dependency with the "workflow" package.
	WorkflowInverseTable = "workflows"
	// WorkflowColumn is the table column denoting the workflow relation/edge.
	WorkflowColumn = "workflow_id"
)

// Columns holds all SQL columns for workflowsedge fields.
var Columns = []string{
	FieldID,
	FieldWorkflowID,
	FieldFromNodeID,
	FieldToNodeID,
	FieldFromPortID,
	FieldToPortID,
	FieldType,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// OrderOption defines the ordering options for the WorkflowsEdge queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByWorkflowID orders the results by the workflow_id field.
func ByWorkflowID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWorkflowID, opts...).ToFunc()
}

// ByFromNodeID orders the results by the from_node_id field.
func ByFromNodeID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFromNodeID, opts...).ToFunc()
}

// ByToNodeID orders the results by the to_node_id field.
func ByToNodeID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldToNodeID, opts...).ToFunc()
}

// ByFromPortID orders the results by the from_port_id field.
func ByFromPortID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFromPortID, opts...).ToFunc()
}

// ByToPortID orders the results by the to_port_id field.
func ByToPortID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldToPortID, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByWorkflowField orders the results by workflow field.
func ByWorkflowField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newWorkflowStep(), sql.OrderByField(field, opts...))
	}
}
func newWorkflowStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(WorkflowInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, WorkflowTable, WorkflowColumn),
	)
}
