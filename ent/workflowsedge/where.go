// Code generated by ent, DO NOT EDIT.

package workflowsedge

import (
	"resflow/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLTE(FieldID, id))
}

// WorkflowID applies equality check predicate on the "workflow_id" field. It's identical to WorkflowIDEQ.
func WorkflowID(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldWorkflowID, v))
}

// FromNodeID applies equality check predicate on the "from_node_id" field. It's identical to FromNodeIDEQ.
func FromNodeID(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldFromNodeID, v))
}

// ToNodeID applies equality check predicate on the "to_node_id" field. It's identical to ToNodeIDEQ.
func ToNodeID(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldToNodeID, v))
}

// FromPortID applies equality check predicate on the "from_port_id" field. It's identical to FromPortIDEQ.
func FromPortID(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldFromPortID, v))
}

// ToPortID applies equality check predicate on the "to_port_id" field. It's identical to ToPortIDEQ.
func ToPortID(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldToPortID, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldType, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldUpdatedAt, v))
}

// WorkflowIDEQ applies the EQ predicate on the "workflow_id" field.
func WorkflowIDEQ(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldWorkflowID, v))
}

// WorkflowIDNEQ applies the NEQ predicate on the "workflow_id" field.
func WorkflowIDNEQ(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNEQ(FieldWorkflowID, v))
}

// WorkflowIDIn applies the In predicate on the "workflow_id" field.
func WorkflowIDIn(vs ...uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldIn(FieldWorkflowID, vs...))
}

// WorkflowIDNotIn applies the NotIn predicate on the "workflow_id" field.
func WorkflowIDNotIn(vs ...uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNotIn(FieldWorkflowID, vs...))
}

// FromNodeIDEQ applies the EQ predicate on the "from_node_id" field.
func FromNodeIDEQ(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldFromNodeID, v))
}

// FromNodeIDNEQ applies the NEQ predicate on the "from_node_id" field.
func FromNodeIDNEQ(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNEQ(FieldFromNodeID, v))
}

// FromNodeIDIn applies the In predicate on the "from_node_id" field.
func FromNodeIDIn(vs ...uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldIn(FieldFromNodeID, vs...))
}

// FromNodeIDNotIn applies the NotIn predicate on the "from_node_id" field.
func FromNodeIDNotIn(vs ...uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNotIn(FieldFromNodeID, vs...))
}

// FromNodeIDGT applies the GT predicate on the "from_node_id" field.
func FromNodeIDGT(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGT(FieldFromNodeID, v))
}

// FromNodeIDGTE applies the GTE predicate on the "from_node_id" field.
func FromNodeIDGTE(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGTE(FieldFromNodeID, v))
}

// FromNodeIDLT applies the LT predicate on the "from_node_id" field.
func FromNodeIDLT(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLT(FieldFromNodeID, v))
}

// FromNodeIDLTE applies the LTE predicate on the "from_node_id" field.
func FromNodeIDLTE(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLTE(FieldFromNodeID, v))
}

// ToNodeIDEQ applies the EQ predicate on the "to_node_id" field.
func ToNodeIDEQ(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldToNodeID, v))
}

// ToNodeIDNEQ applies the NEQ predicate on the "to_node_id" field.
func ToNodeIDNEQ(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNEQ(FieldToNodeID, v))
}

// ToNodeIDIn applies the In predicate on the "to_node_id" field.
func ToNodeIDIn(vs ...uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldIn(FieldToNodeID, vs...))
}

// ToNodeIDNotIn applies the NotIn predicate on the "to_node_id" field.
func ToNodeIDNotIn(vs ...uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNotIn(FieldToNodeID, vs...))
}

// ToNodeIDGT applies the GT predicate on the "to_node_id" field.
func ToNodeIDGT(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGT(FieldToNodeID, v))
}

// ToNodeIDGTE applies the GTE predicate on the "to_node_id" field.
func ToNodeIDGTE(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGTE(FieldToNodeID, v))
}

// ToNodeIDLT applies the LT predicate on the "to_node_id" field.
func ToNodeIDLT(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLT(FieldToNodeID, v))
}

// ToNodeIDLTE applies the LTE predicate on the "to_node_id" field.
func ToNodeIDLTE(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLTE(FieldToNodeID, v))
}

// FromPortIDEQ applies the EQ predicate on the "from_port_id" field.
func FromPortIDEQ(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldFromPortID, v))
}

// FromPortIDNEQ applies the NEQ predicate on the "from_port_id" field.
func FromPortIDNEQ(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNEQ(FieldFromPortID, v))
}

// FromPortIDIn applies the In predicate on the "from_port_id" field.
func FromPortIDIn(vs ...uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldIn(FieldFromPortID, vs...))
}

// FromPortIDNotIn applies the NotIn predicate on the "from_port_id" field.
func FromPortIDNotIn(vs ...uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNotIn(FieldFromPortID, vs...))
}

// FromPortIDGT applies the GT predicate on the "from_port_id" field.
func FromPortIDGT(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGT(FieldFromPortID, v))
}

// FromPortIDGTE applies the GTE predicate on the "from_port_id" field.
func FromPortIDGTE(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGTE(FieldFromPortID, v))
}

// FromPortIDLT applies the LT predicate on the "from_port_id" field.
func FromPortIDLT(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLT(FieldFromPortID, v))
}

// FromPortIDLTE applies the LTE predicate on the "from_port_id" field.
func FromPortIDLTE(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLTE(FieldFromPortID, v))
}

// ToPortIDEQ applies the EQ predicate on the "to_port_id" field.
func ToPortIDEQ(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldToPortID, v))
}

// ToPortIDNEQ applies the NEQ predicate on the "to_port_id" field.
func ToPortIDNEQ(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNEQ(FieldToPortID, v))
}

// ToPortIDIn applies the In predicate on the "to_port_id" field.
func ToPortIDIn(vs ...uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldIn(FieldToPortID, vs...))
}

// ToPortIDNotIn applies the NotIn predicate on the "to_port_id" field.
func ToPortIDNotIn(vs ...uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNotIn(FieldToPortID, vs...))
}

// ToPortIDGT applies the GT predicate on the "to_port_id" field.
func ToPortIDGT(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGT(FieldToPortID, v))
}

// ToPortIDGTE applies the GTE predicate on the "to_port_id" field.
func ToPortIDGTE(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGTE(FieldToPortID, v))
}

// ToPortIDLT applies the LT predicate on the "to_port_id" field.
func ToPortIDLT(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLT(FieldToPortID, v))
}

// ToPortIDLTE applies the LTE predicate on the "to_port_id" field.
func ToPortIDLTE(v uuid.UUID) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLTE(FieldToPortID, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldContainsFold(FieldType, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.FieldLTE(FieldUpdatedAt, v))
}

// HasWorkflow applies the HasEdge predicate on the "workflow" edge.
func HasWorkflow() predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, WorkflowTable, WorkflowColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasWorkflowWith applies the HasEdge predicate on the "workflow" edge with a given conditions (other predicates).
func HasWorkflowWith(preds ...predicate.Workflow) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(func(s *sql.Selector) {
		step := newWorkflowStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WorkflowsEdge) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WorkflowsEdge) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WorkflowsEdge) predicate.WorkflowsEdge {
	return predicate.WorkflowsEdge(sql.NotPredicates(p))
}
