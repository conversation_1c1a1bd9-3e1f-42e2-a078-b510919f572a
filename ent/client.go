// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"resflow/ent/migrate"

	"resflow/ent/nodedefinition"
	"resflow/ent/plugin"
	"resflow/ent/user"
	"resflow/ent/workflow"
	"resflow/ent/workflownode"
	"resflow/ent/workflowsedge"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// NodeDefinition is the client for interacting with the NodeDefinition builders.
	NodeDefinition *NodeDefinitionClient
	// Plugin is the client for interacting with the Plugin builders.
	Plugin *PluginClient
	// User is the client for interacting with the User builders.
	User *UserClient
	// Workflow is the client for interacting with the Workflow builders.
	Workflow *WorkflowClient
	// WorkflowNode is the client for interacting with the WorkflowNode builders.
	WorkflowNode *WorkflowNodeClient
	// WorkflowsEdge is the client for interacting with the WorkflowsEdge builders.
	WorkflowsEdge *WorkflowsEdgeClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.NodeDefinition = NewNodeDefinitionClient(c.config)
	c.Plugin = NewPluginClient(c.config)
	c.User = NewUserClient(c.config)
	c.Workflow = NewWorkflowClient(c.config)
	c.WorkflowNode = NewWorkflowNodeClient(c.config)
	c.WorkflowsEdge = NewWorkflowsEdgeClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:            ctx,
		config:         cfg,
		NodeDefinition: NewNodeDefinitionClient(cfg),
		Plugin:         NewPluginClient(cfg),
		User:           NewUserClient(cfg),
		Workflow:       NewWorkflowClient(cfg),
		WorkflowNode:   NewWorkflowNodeClient(cfg),
		WorkflowsEdge:  NewWorkflowsEdgeClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:            ctx,
		config:         cfg,
		NodeDefinition: NewNodeDefinitionClient(cfg),
		Plugin:         NewPluginClient(cfg),
		User:           NewUserClient(cfg),
		Workflow:       NewWorkflowClient(cfg),
		WorkflowNode:   NewWorkflowNodeClient(cfg),
		WorkflowsEdge:  NewWorkflowsEdgeClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		NodeDefinition.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.NodeDefinition, c.Plugin, c.User, c.Workflow, c.WorkflowNode, c.WorkflowsEdge,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.NodeDefinition, c.Plugin, c.User, c.Workflow, c.WorkflowNode, c.WorkflowsEdge,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *NodeDefinitionMutation:
		return c.NodeDefinition.mutate(ctx, m)
	case *PluginMutation:
		return c.Plugin.mutate(ctx, m)
	case *UserMutation:
		return c.User.mutate(ctx, m)
	case *WorkflowMutation:
		return c.Workflow.mutate(ctx, m)
	case *WorkflowNodeMutation:
		return c.WorkflowNode.mutate(ctx, m)
	case *WorkflowsEdgeMutation:
		return c.WorkflowsEdge.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// NodeDefinitionClient is a client for the NodeDefinition schema.
type NodeDefinitionClient struct {
	config
}

// NewNodeDefinitionClient returns a client for the NodeDefinition from the given config.
func NewNodeDefinitionClient(c config) *NodeDefinitionClient {
	return &NodeDefinitionClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `nodedefinition.Hooks(f(g(h())))`.
func (c *NodeDefinitionClient) Use(hooks ...Hook) {
	c.hooks.NodeDefinition = append(c.hooks.NodeDefinition, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `nodedefinition.Intercept(f(g(h())))`.
func (c *NodeDefinitionClient) Intercept(interceptors ...Interceptor) {
	c.inters.NodeDefinition = append(c.inters.NodeDefinition, interceptors...)
}

// Create returns a builder for creating a NodeDefinition entity.
func (c *NodeDefinitionClient) Create() *NodeDefinitionCreate {
	mutation := newNodeDefinitionMutation(c.config, OpCreate)
	return &NodeDefinitionCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of NodeDefinition entities.
func (c *NodeDefinitionClient) CreateBulk(builders ...*NodeDefinitionCreate) *NodeDefinitionCreateBulk {
	return &NodeDefinitionCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *NodeDefinitionClient) MapCreateBulk(slice any, setFunc func(*NodeDefinitionCreate, int)) *NodeDefinitionCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &NodeDefinitionCreateBulk{err: fmt.Errorf("calling to NodeDefinitionClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*NodeDefinitionCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &NodeDefinitionCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for NodeDefinition.
func (c *NodeDefinitionClient) Update() *NodeDefinitionUpdate {
	mutation := newNodeDefinitionMutation(c.config, OpUpdate)
	return &NodeDefinitionUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *NodeDefinitionClient) UpdateOne(nd *NodeDefinition) *NodeDefinitionUpdateOne {
	mutation := newNodeDefinitionMutation(c.config, OpUpdateOne, withNodeDefinition(nd))
	return &NodeDefinitionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *NodeDefinitionClient) UpdateOneID(id uuid.UUID) *NodeDefinitionUpdateOne {
	mutation := newNodeDefinitionMutation(c.config, OpUpdateOne, withNodeDefinitionID(id))
	return &NodeDefinitionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for NodeDefinition.
func (c *NodeDefinitionClient) Delete() *NodeDefinitionDelete {
	mutation := newNodeDefinitionMutation(c.config, OpDelete)
	return &NodeDefinitionDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *NodeDefinitionClient) DeleteOne(nd *NodeDefinition) *NodeDefinitionDeleteOne {
	return c.DeleteOneID(nd.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *NodeDefinitionClient) DeleteOneID(id uuid.UUID) *NodeDefinitionDeleteOne {
	builder := c.Delete().Where(nodedefinition.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &NodeDefinitionDeleteOne{builder}
}

// Query returns a query builder for NodeDefinition.
func (c *NodeDefinitionClient) Query() *NodeDefinitionQuery {
	return &NodeDefinitionQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeNodeDefinition},
		inters: c.Interceptors(),
	}
}

// Get returns a NodeDefinition entity by its id.
func (c *NodeDefinitionClient) Get(ctx context.Context, id uuid.UUID) (*NodeDefinition, error) {
	return c.Query().Where(nodedefinition.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *NodeDefinitionClient) GetX(ctx context.Context, id uuid.UUID) *NodeDefinition {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *NodeDefinitionClient) Hooks() []Hook {
	return c.hooks.NodeDefinition
}

// Interceptors returns the client interceptors.
func (c *NodeDefinitionClient) Interceptors() []Interceptor {
	return c.inters.NodeDefinition
}

func (c *NodeDefinitionClient) mutate(ctx context.Context, m *NodeDefinitionMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&NodeDefinitionCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&NodeDefinitionUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&NodeDefinitionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&NodeDefinitionDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown NodeDefinition mutation op: %q", m.Op())
	}
}

// PluginClient is a client for the Plugin schema.
type PluginClient struct {
	config
}

// NewPluginClient returns a client for the Plugin from the given config.
func NewPluginClient(c config) *PluginClient {
	return &PluginClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `plugin.Hooks(f(g(h())))`.
func (c *PluginClient) Use(hooks ...Hook) {
	c.hooks.Plugin = append(c.hooks.Plugin, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `plugin.Intercept(f(g(h())))`.
func (c *PluginClient) Intercept(interceptors ...Interceptor) {
	c.inters.Plugin = append(c.inters.Plugin, interceptors...)
}

// Create returns a builder for creating a Plugin entity.
func (c *PluginClient) Create() *PluginCreate {
	mutation := newPluginMutation(c.config, OpCreate)
	return &PluginCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Plugin entities.
func (c *PluginClient) CreateBulk(builders ...*PluginCreate) *PluginCreateBulk {
	return &PluginCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PluginClient) MapCreateBulk(slice any, setFunc func(*PluginCreate, int)) *PluginCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PluginCreateBulk{err: fmt.Errorf("calling to PluginClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PluginCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PluginCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Plugin.
func (c *PluginClient) Update() *PluginUpdate {
	mutation := newPluginMutation(c.config, OpUpdate)
	return &PluginUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PluginClient) UpdateOne(pl *Plugin) *PluginUpdateOne {
	mutation := newPluginMutation(c.config, OpUpdateOne, withPlugin(pl))
	return &PluginUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PluginClient) UpdateOneID(id uuid.UUID) *PluginUpdateOne {
	mutation := newPluginMutation(c.config, OpUpdateOne, withPluginID(id))
	return &PluginUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Plugin.
func (c *PluginClient) Delete() *PluginDelete {
	mutation := newPluginMutation(c.config, OpDelete)
	return &PluginDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PluginClient) DeleteOne(pl *Plugin) *PluginDeleteOne {
	return c.DeleteOneID(pl.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PluginClient) DeleteOneID(id uuid.UUID) *PluginDeleteOne {
	builder := c.Delete().Where(plugin.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PluginDeleteOne{builder}
}

// Query returns a query builder for Plugin.
func (c *PluginClient) Query() *PluginQuery {
	return &PluginQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePlugin},
		inters: c.Interceptors(),
	}
}

// Get returns a Plugin entity by its id.
func (c *PluginClient) Get(ctx context.Context, id uuid.UUID) (*Plugin, error) {
	return c.Query().Where(plugin.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PluginClient) GetX(ctx context.Context, id uuid.UUID) *Plugin {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *PluginClient) Hooks() []Hook {
	return c.hooks.Plugin
}

// Interceptors returns the client interceptors.
func (c *PluginClient) Interceptors() []Interceptor {
	return c.inters.Plugin
}

func (c *PluginClient) mutate(ctx context.Context, m *PluginMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PluginCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PluginUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PluginUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PluginDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Plugin mutation op: %q", m.Op())
	}
}

// UserClient is a client for the User schema.
type UserClient struct {
	config
}

// NewUserClient returns a client for the User from the given config.
func NewUserClient(c config) *UserClient {
	return &UserClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `user.Hooks(f(g(h())))`.
func (c *UserClient) Use(hooks ...Hook) {
	c.hooks.User = append(c.hooks.User, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `user.Intercept(f(g(h())))`.
func (c *UserClient) Intercept(interceptors ...Interceptor) {
	c.inters.User = append(c.inters.User, interceptors...)
}

// Create returns a builder for creating a User entity.
func (c *UserClient) Create() *UserCreate {
	mutation := newUserMutation(c.config, OpCreate)
	return &UserCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of User entities.
func (c *UserClient) CreateBulk(builders ...*UserCreate) *UserCreateBulk {
	return &UserCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UserClient) MapCreateBulk(slice any, setFunc func(*UserCreate, int)) *UserCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UserCreateBulk{err: fmt.Errorf("calling to UserClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UserCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UserCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for User.
func (c *UserClient) Update() *UserUpdate {
	mutation := newUserMutation(c.config, OpUpdate)
	return &UserUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UserClient) UpdateOne(u *User) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUser(u))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UserClient) UpdateOneID(id uuid.UUID) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUserID(id))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for User.
func (c *UserClient) Delete() *UserDelete {
	mutation := newUserMutation(c.config, OpDelete)
	return &UserDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UserClient) DeleteOne(u *User) *UserDeleteOne {
	return c.DeleteOneID(u.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UserClient) DeleteOneID(id uuid.UUID) *UserDeleteOne {
	builder := c.Delete().Where(user.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UserDeleteOne{builder}
}

// Query returns a query builder for User.
func (c *UserClient) Query() *UserQuery {
	return &UserQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUser},
		inters: c.Interceptors(),
	}
}

// Get returns a User entity by its id.
func (c *UserClient) Get(ctx context.Context, id uuid.UUID) (*User, error) {
	return c.Query().Where(user.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UserClient) GetX(ctx context.Context, id uuid.UUID) *User {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryWorkflows queries the workflows edge of a User.
func (c *UserClient) QueryWorkflows(u *User) *WorkflowQuery {
	query := (&WorkflowClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(workflow.Table, workflow.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.WorkflowsTable, user.WorkflowsColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *UserClient) Hooks() []Hook {
	return c.hooks.User
}

// Interceptors returns the client interceptors.
func (c *UserClient) Interceptors() []Interceptor {
	return c.inters.User
}

func (c *UserClient) mutate(ctx context.Context, m *UserMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UserCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UserUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UserDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown User mutation op: %q", m.Op())
	}
}

// WorkflowClient is a client for the Workflow schema.
type WorkflowClient struct {
	config
}

// NewWorkflowClient returns a client for the Workflow from the given config.
func NewWorkflowClient(c config) *WorkflowClient {
	return &WorkflowClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `workflow.Hooks(f(g(h())))`.
func (c *WorkflowClient) Use(hooks ...Hook) {
	c.hooks.Workflow = append(c.hooks.Workflow, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `workflow.Intercept(f(g(h())))`.
func (c *WorkflowClient) Intercept(interceptors ...Interceptor) {
	c.inters.Workflow = append(c.inters.Workflow, interceptors...)
}

// Create returns a builder for creating a Workflow entity.
func (c *WorkflowClient) Create() *WorkflowCreate {
	mutation := newWorkflowMutation(c.config, OpCreate)
	return &WorkflowCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Workflow entities.
func (c *WorkflowClient) CreateBulk(builders ...*WorkflowCreate) *WorkflowCreateBulk {
	return &WorkflowCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *WorkflowClient) MapCreateBulk(slice any, setFunc func(*WorkflowCreate, int)) *WorkflowCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &WorkflowCreateBulk{err: fmt.Errorf("calling to WorkflowClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*WorkflowCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &WorkflowCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Workflow.
func (c *WorkflowClient) Update() *WorkflowUpdate {
	mutation := newWorkflowMutation(c.config, OpUpdate)
	return &WorkflowUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *WorkflowClient) UpdateOne(w *Workflow) *WorkflowUpdateOne {
	mutation := newWorkflowMutation(c.config, OpUpdateOne, withWorkflow(w))
	return &WorkflowUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *WorkflowClient) UpdateOneID(id uuid.UUID) *WorkflowUpdateOne {
	mutation := newWorkflowMutation(c.config, OpUpdateOne, withWorkflowID(id))
	return &WorkflowUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Workflow.
func (c *WorkflowClient) Delete() *WorkflowDelete {
	mutation := newWorkflowMutation(c.config, OpDelete)
	return &WorkflowDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *WorkflowClient) DeleteOne(w *Workflow) *WorkflowDeleteOne {
	return c.DeleteOneID(w.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *WorkflowClient) DeleteOneID(id uuid.UUID) *WorkflowDeleteOne {
	builder := c.Delete().Where(workflow.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &WorkflowDeleteOne{builder}
}

// Query returns a query builder for Workflow.
func (c *WorkflowClient) Query() *WorkflowQuery {
	return &WorkflowQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeWorkflow},
		inters: c.Interceptors(),
	}
}

// Get returns a Workflow entity by its id.
func (c *WorkflowClient) Get(ctx context.Context, id uuid.UUID) (*Workflow, error) {
	return c.Query().Where(workflow.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *WorkflowClient) GetX(ctx context.Context, id uuid.UUID) *Workflow {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUser queries the user edge of a Workflow.
func (c *WorkflowClient) QueryUser(w *Workflow) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := w.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(workflow.Table, workflow.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, workflow.UserTable, workflow.UserColumn),
		)
		fromV = sqlgraph.Neighbors(w.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryNodes queries the nodes edge of a Workflow.
func (c *WorkflowClient) QueryNodes(w *Workflow) *WorkflowNodeQuery {
	query := (&WorkflowNodeClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := w.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(workflow.Table, workflow.FieldID, id),
			sqlgraph.To(workflownode.Table, workflownode.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, workflow.NodesTable, workflow.NodesColumn),
		)
		fromV = sqlgraph.Neighbors(w.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryWorkflowEdges queries the workflow_edges edge of a Workflow.
func (c *WorkflowClient) QueryWorkflowEdges(w *Workflow) *WorkflowsEdgeQuery {
	query := (&WorkflowsEdgeClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := w.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(workflow.Table, workflow.FieldID, id),
			sqlgraph.To(workflowsedge.Table, workflowsedge.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, workflow.WorkflowEdgesTable, workflow.WorkflowEdgesColumn),
		)
		fromV = sqlgraph.Neighbors(w.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *WorkflowClient) Hooks() []Hook {
	return c.hooks.Workflow
}

// Interceptors returns the client interceptors.
func (c *WorkflowClient) Interceptors() []Interceptor {
	return c.inters.Workflow
}

func (c *WorkflowClient) mutate(ctx context.Context, m *WorkflowMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&WorkflowCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&WorkflowUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&WorkflowUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&WorkflowDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Workflow mutation op: %q", m.Op())
	}
}

// WorkflowNodeClient is a client for the WorkflowNode schema.
type WorkflowNodeClient struct {
	config
}

// NewWorkflowNodeClient returns a client for the WorkflowNode from the given config.
func NewWorkflowNodeClient(c config) *WorkflowNodeClient {
	return &WorkflowNodeClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `workflownode.Hooks(f(g(h())))`.
func (c *WorkflowNodeClient) Use(hooks ...Hook) {
	c.hooks.WorkflowNode = append(c.hooks.WorkflowNode, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `workflownode.Intercept(f(g(h())))`.
func (c *WorkflowNodeClient) Intercept(interceptors ...Interceptor) {
	c.inters.WorkflowNode = append(c.inters.WorkflowNode, interceptors...)
}

// Create returns a builder for creating a WorkflowNode entity.
func (c *WorkflowNodeClient) Create() *WorkflowNodeCreate {
	mutation := newWorkflowNodeMutation(c.config, OpCreate)
	return &WorkflowNodeCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of WorkflowNode entities.
func (c *WorkflowNodeClient) CreateBulk(builders ...*WorkflowNodeCreate) *WorkflowNodeCreateBulk {
	return &WorkflowNodeCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *WorkflowNodeClient) MapCreateBulk(slice any, setFunc func(*WorkflowNodeCreate, int)) *WorkflowNodeCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &WorkflowNodeCreateBulk{err: fmt.Errorf("calling to WorkflowNodeClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*WorkflowNodeCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &WorkflowNodeCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for WorkflowNode.
func (c *WorkflowNodeClient) Update() *WorkflowNodeUpdate {
	mutation := newWorkflowNodeMutation(c.config, OpUpdate)
	return &WorkflowNodeUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *WorkflowNodeClient) UpdateOne(wn *WorkflowNode) *WorkflowNodeUpdateOne {
	mutation := newWorkflowNodeMutation(c.config, OpUpdateOne, withWorkflowNode(wn))
	return &WorkflowNodeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *WorkflowNodeClient) UpdateOneID(id uuid.UUID) *WorkflowNodeUpdateOne {
	mutation := newWorkflowNodeMutation(c.config, OpUpdateOne, withWorkflowNodeID(id))
	return &WorkflowNodeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for WorkflowNode.
func (c *WorkflowNodeClient) Delete() *WorkflowNodeDelete {
	mutation := newWorkflowNodeMutation(c.config, OpDelete)
	return &WorkflowNodeDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *WorkflowNodeClient) DeleteOne(wn *WorkflowNode) *WorkflowNodeDeleteOne {
	return c.DeleteOneID(wn.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *WorkflowNodeClient) DeleteOneID(id uuid.UUID) *WorkflowNodeDeleteOne {
	builder := c.Delete().Where(workflownode.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &WorkflowNodeDeleteOne{builder}
}

// Query returns a query builder for WorkflowNode.
func (c *WorkflowNodeClient) Query() *WorkflowNodeQuery {
	return &WorkflowNodeQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeWorkflowNode},
		inters: c.Interceptors(),
	}
}

// Get returns a WorkflowNode entity by its id.
func (c *WorkflowNodeClient) Get(ctx context.Context, id uuid.UUID) (*WorkflowNode, error) {
	return c.Query().Where(workflownode.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *WorkflowNodeClient) GetX(ctx context.Context, id uuid.UUID) *WorkflowNode {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryWorkflow queries the workflow edge of a WorkflowNode.
func (c *WorkflowNodeClient) QueryWorkflow(wn *WorkflowNode) *WorkflowQuery {
	query := (&WorkflowClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := wn.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(workflownode.Table, workflownode.FieldID, id),
			sqlgraph.To(workflow.Table, workflow.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, workflownode.WorkflowTable, workflownode.WorkflowColumn),
		)
		fromV = sqlgraph.Neighbors(wn.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *WorkflowNodeClient) Hooks() []Hook {
	return c.hooks.WorkflowNode
}

// Interceptors returns the client interceptors.
func (c *WorkflowNodeClient) Interceptors() []Interceptor {
	return c.inters.WorkflowNode
}

func (c *WorkflowNodeClient) mutate(ctx context.Context, m *WorkflowNodeMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&WorkflowNodeCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&WorkflowNodeUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&WorkflowNodeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&WorkflowNodeDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown WorkflowNode mutation op: %q", m.Op())
	}
}

// WorkflowsEdgeClient is a client for the WorkflowsEdge schema.
type WorkflowsEdgeClient struct {
	config
}

// NewWorkflowsEdgeClient returns a client for the WorkflowsEdge from the given config.
func NewWorkflowsEdgeClient(c config) *WorkflowsEdgeClient {
	return &WorkflowsEdgeClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `workflowsedge.Hooks(f(g(h())))`.
func (c *WorkflowsEdgeClient) Use(hooks ...Hook) {
	c.hooks.WorkflowsEdge = append(c.hooks.WorkflowsEdge, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `workflowsedge.Intercept(f(g(h())))`.
func (c *WorkflowsEdgeClient) Intercept(interceptors ...Interceptor) {
	c.inters.WorkflowsEdge = append(c.inters.WorkflowsEdge, interceptors...)
}

// Create returns a builder for creating a WorkflowsEdge entity.
func (c *WorkflowsEdgeClient) Create() *WorkflowsEdgeCreate {
	mutation := newWorkflowsEdgeMutation(c.config, OpCreate)
	return &WorkflowsEdgeCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of WorkflowsEdge entities.
func (c *WorkflowsEdgeClient) CreateBulk(builders ...*WorkflowsEdgeCreate) *WorkflowsEdgeCreateBulk {
	return &WorkflowsEdgeCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *WorkflowsEdgeClient) MapCreateBulk(slice any, setFunc func(*WorkflowsEdgeCreate, int)) *WorkflowsEdgeCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &WorkflowsEdgeCreateBulk{err: fmt.Errorf("calling to WorkflowsEdgeClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*WorkflowsEdgeCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &WorkflowsEdgeCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for WorkflowsEdge.
func (c *WorkflowsEdgeClient) Update() *WorkflowsEdgeUpdate {
	mutation := newWorkflowsEdgeMutation(c.config, OpUpdate)
	return &WorkflowsEdgeUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *WorkflowsEdgeClient) UpdateOne(we *WorkflowsEdge) *WorkflowsEdgeUpdateOne {
	mutation := newWorkflowsEdgeMutation(c.config, OpUpdateOne, withWorkflowsEdge(we))
	return &WorkflowsEdgeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *WorkflowsEdgeClient) UpdateOneID(id uuid.UUID) *WorkflowsEdgeUpdateOne {
	mutation := newWorkflowsEdgeMutation(c.config, OpUpdateOne, withWorkflowsEdgeID(id))
	return &WorkflowsEdgeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for WorkflowsEdge.
func (c *WorkflowsEdgeClient) Delete() *WorkflowsEdgeDelete {
	mutation := newWorkflowsEdgeMutation(c.config, OpDelete)
	return &WorkflowsEdgeDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *WorkflowsEdgeClient) DeleteOne(we *WorkflowsEdge) *WorkflowsEdgeDeleteOne {
	return c.DeleteOneID(we.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *WorkflowsEdgeClient) DeleteOneID(id uuid.UUID) *WorkflowsEdgeDeleteOne {
	builder := c.Delete().Where(workflowsedge.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &WorkflowsEdgeDeleteOne{builder}
}

// Query returns a query builder for WorkflowsEdge.
func (c *WorkflowsEdgeClient) Query() *WorkflowsEdgeQuery {
	return &WorkflowsEdgeQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeWorkflowsEdge},
		inters: c.Interceptors(),
	}
}

// Get returns a WorkflowsEdge entity by its id.
func (c *WorkflowsEdgeClient) Get(ctx context.Context, id uuid.UUID) (*WorkflowsEdge, error) {
	return c.Query().Where(workflowsedge.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *WorkflowsEdgeClient) GetX(ctx context.Context, id uuid.UUID) *WorkflowsEdge {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryWorkflow queries the workflow edge of a WorkflowsEdge.
func (c *WorkflowsEdgeClient) QueryWorkflow(we *WorkflowsEdge) *WorkflowQuery {
	query := (&WorkflowClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := we.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(workflowsedge.Table, workflowsedge.FieldID, id),
			sqlgraph.To(workflow.Table, workflow.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, workflowsedge.WorkflowTable, workflowsedge.WorkflowColumn),
		)
		fromV = sqlgraph.Neighbors(we.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *WorkflowsEdgeClient) Hooks() []Hook {
	return c.hooks.WorkflowsEdge
}

// Interceptors returns the client interceptors.
func (c *WorkflowsEdgeClient) Interceptors() []Interceptor {
	return c.inters.WorkflowsEdge
}

func (c *WorkflowsEdgeClient) mutate(ctx context.Context, m *WorkflowsEdgeMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&WorkflowsEdgeCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&WorkflowsEdgeUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&WorkflowsEdgeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&WorkflowsEdgeDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown WorkflowsEdge mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		NodeDefinition, Plugin, User, Workflow, WorkflowNode, WorkflowsEdge []ent.Hook
	}
	inters struct {
		NodeDefinition, Plugin, User, Workflow, WorkflowNode,
		WorkflowsEdge []ent.Interceptor
	}
)
