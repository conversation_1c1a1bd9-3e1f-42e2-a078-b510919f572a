// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"resflow/ent/nodedefinition"
	"resflow/ent/predicate"
	"resflow/internal/plugin/domain"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
)

// NodeDefinitionUpdate is the builder for updating NodeDefinition entities.
type NodeDefinitionUpdate struct {
	config
	hooks    []Hook
	mutation *NodeDefinitionMutation
}

// Where appends a list predicates to the NodeDefinitionUpdate builder.
func (ndu *NodeDefinitionUpdate) Where(ps ...predicate.NodeDefinition) *NodeDefinitionUpdate {
	ndu.mutation.Where(ps...)
	return ndu
}

// SetPluginName sets the "plugin_name" field.
func (ndu *NodeDefinitionUpdate) SetPluginName(s string) *NodeDefinitionUpdate {
	ndu.mutation.SetPluginName(s)
	return ndu
}

// SetNillablePluginName sets the "plugin_name" field if the given value is not nil.
func (ndu *NodeDefinitionUpdate) SetNillablePluginName(s *string) *NodeDefinitionUpdate {
	if s != nil {
		ndu.SetPluginName(*s)
	}
	return ndu
}

// SetName sets the "name" field.
func (ndu *NodeDefinitionUpdate) SetName(s string) *NodeDefinitionUpdate {
	ndu.mutation.SetName(s)
	return ndu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (ndu *NodeDefinitionUpdate) SetNillableName(s *string) *NodeDefinitionUpdate {
	if s != nil {
		ndu.SetName(*s)
	}
	return ndu
}

// SetAuthor sets the "author" field.
func (ndu *NodeDefinitionUpdate) SetAuthor(s string) *NodeDefinitionUpdate {
	ndu.mutation.SetAuthor(s)
	return ndu
}

// SetNillableAuthor sets the "author" field if the given value is not nil.
func (ndu *NodeDefinitionUpdate) SetNillableAuthor(s *string) *NodeDefinitionUpdate {
	if s != nil {
		ndu.SetAuthor(*s)
	}
	return ndu
}

// SetDescription sets the "description" field.
func (ndu *NodeDefinitionUpdate) SetDescription(s string) *NodeDefinitionUpdate {
	ndu.mutation.SetDescription(s)
	return ndu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (ndu *NodeDefinitionUpdate) SetNillableDescription(s *string) *NodeDefinitionUpdate {
	if s != nil {
		ndu.SetDescription(*s)
	}
	return ndu
}

// SetIcon sets the "icon" field.
func (ndu *NodeDefinitionUpdate) SetIcon(s string) *NodeDefinitionUpdate {
	ndu.mutation.SetIcon(s)
	return ndu
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (ndu *NodeDefinitionUpdate) SetNillableIcon(s *string) *NodeDefinitionUpdate {
	if s != nil {
		ndu.SetIcon(*s)
	}
	return ndu
}

// SetType sets the "type" field.
func (ndu *NodeDefinitionUpdate) SetType(s string) *NodeDefinitionUpdate {
	ndu.mutation.SetType(s)
	return ndu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (ndu *NodeDefinitionUpdate) SetNillableType(s *string) *NodeDefinitionUpdate {
	if s != nil {
		ndu.SetType(*s)
	}
	return ndu
}

// SetVersion sets the "version" field.
func (ndu *NodeDefinitionUpdate) SetVersion(s string) *NodeDefinitionUpdate {
	ndu.mutation.SetVersion(s)
	return ndu
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (ndu *NodeDefinitionUpdate) SetNillableVersion(s *string) *NodeDefinitionUpdate {
	if s != nil {
		ndu.SetVersion(*s)
	}
	return ndu
}

// SetCategory sets the "category" field.
func (ndu *NodeDefinitionUpdate) SetCategory(s string) *NodeDefinitionUpdate {
	ndu.mutation.SetCategory(s)
	return ndu
}

// SetNillableCategory sets the "category" field if the given value is not nil.
func (ndu *NodeDefinitionUpdate) SetNillableCategory(s *string) *NodeDefinitionUpdate {
	if s != nil {
		ndu.SetCategory(*s)
	}
	return ndu
}

// SetInputParams sets the "input_params" field.
func (ndu *NodeDefinitionUpdate) SetInputParams(dp []*domain.NodeParam) *NodeDefinitionUpdate {
	ndu.mutation.SetInputParams(dp)
	return ndu
}

// AppendInputParams appends dp to the "input_params" field.
func (ndu *NodeDefinitionUpdate) AppendInputParams(dp []*domain.NodeParam) *NodeDefinitionUpdate {
	ndu.mutation.AppendInputParams(dp)
	return ndu
}

// SetOutputParams sets the "output_params" field.
func (ndu *NodeDefinitionUpdate) SetOutputParams(dp []*domain.NodeParam) *NodeDefinitionUpdate {
	ndu.mutation.SetOutputParams(dp)
	return ndu
}

// AppendOutputParams appends dp to the "output_params" field.
func (ndu *NodeDefinitionUpdate) AppendOutputParams(dp []*domain.NodeParam) *NodeDefinitionUpdate {
	ndu.mutation.AppendOutputParams(dp)
	return ndu
}

// SetInputPorts sets the "input_ports" field.
func (ndu *NodeDefinitionUpdate) SetInputPorts(dp []*domain.NodePort) *NodeDefinitionUpdate {
	ndu.mutation.SetInputPorts(dp)
	return ndu
}

// AppendInputPorts appends dp to the "input_ports" field.
func (ndu *NodeDefinitionUpdate) AppendInputPorts(dp []*domain.NodePort) *NodeDefinitionUpdate {
	ndu.mutation.AppendInputPorts(dp)
	return ndu
}

// SetOutputPorts sets the "output_ports" field.
func (ndu *NodeDefinitionUpdate) SetOutputPorts(dp []*domain.NodePort) *NodeDefinitionUpdate {
	ndu.mutation.SetOutputPorts(dp)
	return ndu
}

// AppendOutputPorts appends dp to the "output_ports" field.
func (ndu *NodeDefinitionUpdate) AppendOutputPorts(dp []*domain.NodePort) *NodeDefinitionUpdate {
	ndu.mutation.AppendOutputPorts(dp)
	return ndu
}

// SetException sets the "exception" field.
func (ndu *NodeDefinitionUpdate) SetException(b bool) *NodeDefinitionUpdate {
	ndu.mutation.SetException(b)
	return ndu
}

// SetNillableException sets the "exception" field if the given value is not nil.
func (ndu *NodeDefinitionUpdate) SetNillableException(b *bool) *NodeDefinitionUpdate {
	if b != nil {
		ndu.SetException(*b)
	}
	return ndu
}

// SetPath sets the "path" field.
func (ndu *NodeDefinitionUpdate) SetPath(s string) *NodeDefinitionUpdate {
	ndu.mutation.SetPath(s)
	return ndu
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (ndu *NodeDefinitionUpdate) SetNillablePath(s *string) *NodeDefinitionUpdate {
	if s != nil {
		ndu.SetPath(*s)
	}
	return ndu
}

// SetBuiltin sets the "builtin" field.
func (ndu *NodeDefinitionUpdate) SetBuiltin(b bool) *NodeDefinitionUpdate {
	ndu.mutation.SetBuiltin(b)
	return ndu
}

// SetNillableBuiltin sets the "builtin" field if the given value is not nil.
func (ndu *NodeDefinitionUpdate) SetNillableBuiltin(b *bool) *NodeDefinitionUpdate {
	if b != nil {
		ndu.SetBuiltin(*b)
	}
	return ndu
}

// SetEnabled sets the "enabled" field.
func (ndu *NodeDefinitionUpdate) SetEnabled(b bool) *NodeDefinitionUpdate {
	ndu.mutation.SetEnabled(b)
	return ndu
}

// SetNillableEnabled sets the "enabled" field if the given value is not nil.
func (ndu *NodeDefinitionUpdate) SetNillableEnabled(b *bool) *NodeDefinitionUpdate {
	if b != nil {
		ndu.SetEnabled(*b)
	}
	return ndu
}

// SetUpdatedAt sets the "updated_at" field.
func (ndu *NodeDefinitionUpdate) SetUpdatedAt(t time.Time) *NodeDefinitionUpdate {
	ndu.mutation.SetUpdatedAt(t)
	return ndu
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ndu *NodeDefinitionUpdate) SetNillableUpdatedAt(t *time.Time) *NodeDefinitionUpdate {
	if t != nil {
		ndu.SetUpdatedAt(*t)
	}
	return ndu
}

// Mutation returns the NodeDefinitionMutation object of the builder.
func (ndu *NodeDefinitionUpdate) Mutation() *NodeDefinitionMutation {
	return ndu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ndu *NodeDefinitionUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, ndu.sqlSave, ndu.mutation, ndu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ndu *NodeDefinitionUpdate) SaveX(ctx context.Context) int {
	affected, err := ndu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ndu *NodeDefinitionUpdate) Exec(ctx context.Context) error {
	_, err := ndu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ndu *NodeDefinitionUpdate) ExecX(ctx context.Context) {
	if err := ndu.Exec(ctx); err != nil {
		panic(err)
	}
}

func (ndu *NodeDefinitionUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(nodedefinition.Table, nodedefinition.Columns, sqlgraph.NewFieldSpec(nodedefinition.FieldID, field.TypeUUID))
	if ps := ndu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ndu.mutation.PluginName(); ok {
		_spec.SetField(nodedefinition.FieldPluginName, field.TypeString, value)
	}
	if value, ok := ndu.mutation.Name(); ok {
		_spec.SetField(nodedefinition.FieldName, field.TypeString, value)
	}
	if value, ok := ndu.mutation.Author(); ok {
		_spec.SetField(nodedefinition.FieldAuthor, field.TypeString, value)
	}
	if value, ok := ndu.mutation.Description(); ok {
		_spec.SetField(nodedefinition.FieldDescription, field.TypeString, value)
	}
	if value, ok := ndu.mutation.Icon(); ok {
		_spec.SetField(nodedefinition.FieldIcon, field.TypeString, value)
	}
	if value, ok := ndu.mutation.GetType(); ok {
		_spec.SetField(nodedefinition.FieldType, field.TypeString, value)
	}
	if value, ok := ndu.mutation.Version(); ok {
		_spec.SetField(nodedefinition.FieldVersion, field.TypeString, value)
	}
	if value, ok := ndu.mutation.Category(); ok {
		_spec.SetField(nodedefinition.FieldCategory, field.TypeString, value)
	}
	if value, ok := ndu.mutation.InputParams(); ok {
		_spec.SetField(nodedefinition.FieldInputParams, field.TypeJSON, value)
	}
	if value, ok := ndu.mutation.AppendedInputParams(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, nodedefinition.FieldInputParams, value)
		})
	}
	if value, ok := ndu.mutation.OutputParams(); ok {
		_spec.SetField(nodedefinition.FieldOutputParams, field.TypeJSON, value)
	}
	if value, ok := ndu.mutation.AppendedOutputParams(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, nodedefinition.FieldOutputParams, value)
		})
	}
	if value, ok := ndu.mutation.InputPorts(); ok {
		_spec.SetField(nodedefinition.FieldInputPorts, field.TypeJSON, value)
	}
	if value, ok := ndu.mutation.AppendedInputPorts(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, nodedefinition.FieldInputPorts, value)
		})
	}
	if value, ok := ndu.mutation.OutputPorts(); ok {
		_spec.SetField(nodedefinition.FieldOutputPorts, field.TypeJSON, value)
	}
	if value, ok := ndu.mutation.AppendedOutputPorts(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, nodedefinition.FieldOutputPorts, value)
		})
	}
	if value, ok := ndu.mutation.Exception(); ok {
		_spec.SetField(nodedefinition.FieldException, field.TypeBool, value)
	}
	if value, ok := ndu.mutation.Path(); ok {
		_spec.SetField(nodedefinition.FieldPath, field.TypeString, value)
	}
	if value, ok := ndu.mutation.Builtin(); ok {
		_spec.SetField(nodedefinition.FieldBuiltin, field.TypeBool, value)
	}
	if value, ok := ndu.mutation.Enabled(); ok {
		_spec.SetField(nodedefinition.FieldEnabled, field.TypeBool, value)
	}
	if value, ok := ndu.mutation.UpdatedAt(); ok {
		_spec.SetField(nodedefinition.FieldUpdatedAt, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ndu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{nodedefinition.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ndu.mutation.done = true
	return n, nil
}

// NodeDefinitionUpdateOne is the builder for updating a single NodeDefinition entity.
type NodeDefinitionUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *NodeDefinitionMutation
}

// SetPluginName sets the "plugin_name" field.
func (nduo *NodeDefinitionUpdateOne) SetPluginName(s string) *NodeDefinitionUpdateOne {
	nduo.mutation.SetPluginName(s)
	return nduo
}

// SetNillablePluginName sets the "plugin_name" field if the given value is not nil.
func (nduo *NodeDefinitionUpdateOne) SetNillablePluginName(s *string) *NodeDefinitionUpdateOne {
	if s != nil {
		nduo.SetPluginName(*s)
	}
	return nduo
}

// SetName sets the "name" field.
func (nduo *NodeDefinitionUpdateOne) SetName(s string) *NodeDefinitionUpdateOne {
	nduo.mutation.SetName(s)
	return nduo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (nduo *NodeDefinitionUpdateOne) SetNillableName(s *string) *NodeDefinitionUpdateOne {
	if s != nil {
		nduo.SetName(*s)
	}
	return nduo
}

// SetAuthor sets the "author" field.
func (nduo *NodeDefinitionUpdateOne) SetAuthor(s string) *NodeDefinitionUpdateOne {
	nduo.mutation.SetAuthor(s)
	return nduo
}

// SetNillableAuthor sets the "author" field if the given value is not nil.
func (nduo *NodeDefinitionUpdateOne) SetNillableAuthor(s *string) *NodeDefinitionUpdateOne {
	if s != nil {
		nduo.SetAuthor(*s)
	}
	return nduo
}

// SetDescription sets the "description" field.
func (nduo *NodeDefinitionUpdateOne) SetDescription(s string) *NodeDefinitionUpdateOne {
	nduo.mutation.SetDescription(s)
	return nduo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (nduo *NodeDefinitionUpdateOne) SetNillableDescription(s *string) *NodeDefinitionUpdateOne {
	if s != nil {
		nduo.SetDescription(*s)
	}
	return nduo
}

// SetIcon sets the "icon" field.
func (nduo *NodeDefinitionUpdateOne) SetIcon(s string) *NodeDefinitionUpdateOne {
	nduo.mutation.SetIcon(s)
	return nduo
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (nduo *NodeDefinitionUpdateOne) SetNillableIcon(s *string) *NodeDefinitionUpdateOne {
	if s != nil {
		nduo.SetIcon(*s)
	}
	return nduo
}

// SetType sets the "type" field.
func (nduo *NodeDefinitionUpdateOne) SetType(s string) *NodeDefinitionUpdateOne {
	nduo.mutation.SetType(s)
	return nduo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (nduo *NodeDefinitionUpdateOne) SetNillableType(s *string) *NodeDefinitionUpdateOne {
	if s != nil {
		nduo.SetType(*s)
	}
	return nduo
}

// SetVersion sets the "version" field.
func (nduo *NodeDefinitionUpdateOne) SetVersion(s string) *NodeDefinitionUpdateOne {
	nduo.mutation.SetVersion(s)
	return nduo
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (nduo *NodeDefinitionUpdateOne) SetNillableVersion(s *string) *NodeDefinitionUpdateOne {
	if s != nil {
		nduo.SetVersion(*s)
	}
	return nduo
}

// SetCategory sets the "category" field.
func (nduo *NodeDefinitionUpdateOne) SetCategory(s string) *NodeDefinitionUpdateOne {
	nduo.mutation.SetCategory(s)
	return nduo
}

// SetNillableCategory sets the "category" field if the given value is not nil.
func (nduo *NodeDefinitionUpdateOne) SetNillableCategory(s *string) *NodeDefinitionUpdateOne {
	if s != nil {
		nduo.SetCategory(*s)
	}
	return nduo
}

// SetInputParams sets the "input_params" field.
func (nduo *NodeDefinitionUpdateOne) SetInputParams(dp []*domain.NodeParam) *NodeDefinitionUpdateOne {
	nduo.mutation.SetInputParams(dp)
	return nduo
}

// AppendInputParams appends dp to the "input_params" field.
func (nduo *NodeDefinitionUpdateOne) AppendInputParams(dp []*domain.NodeParam) *NodeDefinitionUpdateOne {
	nduo.mutation.AppendInputParams(dp)
	return nduo
}

// SetOutputParams sets the "output_params" field.
func (nduo *NodeDefinitionUpdateOne) SetOutputParams(dp []*domain.NodeParam) *NodeDefinitionUpdateOne {
	nduo.mutation.SetOutputParams(dp)
	return nduo
}

// AppendOutputParams appends dp to the "output_params" field.
func (nduo *NodeDefinitionUpdateOne) AppendOutputParams(dp []*domain.NodeParam) *NodeDefinitionUpdateOne {
	nduo.mutation.AppendOutputParams(dp)
	return nduo
}

// SetInputPorts sets the "input_ports" field.
func (nduo *NodeDefinitionUpdateOne) SetInputPorts(dp []*domain.NodePort) *NodeDefinitionUpdateOne {
	nduo.mutation.SetInputPorts(dp)
	return nduo
}

// AppendInputPorts appends dp to the "input_ports" field.
func (nduo *NodeDefinitionUpdateOne) AppendInputPorts(dp []*domain.NodePort) *NodeDefinitionUpdateOne {
	nduo.mutation.AppendInputPorts(dp)
	return nduo
}

// SetOutputPorts sets the "output_ports" field.
func (nduo *NodeDefinitionUpdateOne) SetOutputPorts(dp []*domain.NodePort) *NodeDefinitionUpdateOne {
	nduo.mutation.SetOutputPorts(dp)
	return nduo
}

// AppendOutputPorts appends dp to the "output_ports" field.
func (nduo *NodeDefinitionUpdateOne) AppendOutputPorts(dp []*domain.NodePort) *NodeDefinitionUpdateOne {
	nduo.mutation.AppendOutputPorts(dp)
	return nduo
}

// SetException sets the "exception" field.
func (nduo *NodeDefinitionUpdateOne) SetException(b bool) *NodeDefinitionUpdateOne {
	nduo.mutation.SetException(b)
	return nduo
}

// SetNillableException sets the "exception" field if the given value is not nil.
func (nduo *NodeDefinitionUpdateOne) SetNillableException(b *bool) *NodeDefinitionUpdateOne {
	if b != nil {
		nduo.SetException(*b)
	}
	return nduo
}

// SetPath sets the "path" field.
func (nduo *NodeDefinitionUpdateOne) SetPath(s string) *NodeDefinitionUpdateOne {
	nduo.mutation.SetPath(s)
	return nduo
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (nduo *NodeDefinitionUpdateOne) SetNillablePath(s *string) *NodeDefinitionUpdateOne {
	if s != nil {
		nduo.SetPath(*s)
	}
	return nduo
}

// SetBuiltin sets the "builtin" field.
func (nduo *NodeDefinitionUpdateOne) SetBuiltin(b bool) *NodeDefinitionUpdateOne {
	nduo.mutation.SetBuiltin(b)
	return nduo
}

// SetNillableBuiltin sets the "builtin" field if the given value is not nil.
func (nduo *NodeDefinitionUpdateOne) SetNillableBuiltin(b *bool) *NodeDefinitionUpdateOne {
	if b != nil {
		nduo.SetBuiltin(*b)
	}
	return nduo
}

// SetEnabled sets the "enabled" field.
func (nduo *NodeDefinitionUpdateOne) SetEnabled(b bool) *NodeDefinitionUpdateOne {
	nduo.mutation.SetEnabled(b)
	return nduo
}

// SetNillableEnabled sets the "enabled" field if the given value is not nil.
func (nduo *NodeDefinitionUpdateOne) SetNillableEnabled(b *bool) *NodeDefinitionUpdateOne {
	if b != nil {
		nduo.SetEnabled(*b)
	}
	return nduo
}

// SetUpdatedAt sets the "updated_at" field.
func (nduo *NodeDefinitionUpdateOne) SetUpdatedAt(t time.Time) *NodeDefinitionUpdateOne {
	nduo.mutation.SetUpdatedAt(t)
	return nduo
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (nduo *NodeDefinitionUpdateOne) SetNillableUpdatedAt(t *time.Time) *NodeDefinitionUpdateOne {
	if t != nil {
		nduo.SetUpdatedAt(*t)
	}
	return nduo
}

// Mutation returns the NodeDefinitionMutation object of the builder.
func (nduo *NodeDefinitionUpdateOne) Mutation() *NodeDefinitionMutation {
	return nduo.mutation
}

// Where appends a list predicates to the NodeDefinitionUpdate builder.
func (nduo *NodeDefinitionUpdateOne) Where(ps ...predicate.NodeDefinition) *NodeDefinitionUpdateOne {
	nduo.mutation.Where(ps...)
	return nduo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (nduo *NodeDefinitionUpdateOne) Select(field string, fields ...string) *NodeDefinitionUpdateOne {
	nduo.fields = append([]string{field}, fields...)
	return nduo
}

// Save executes the query and returns the updated NodeDefinition entity.
func (nduo *NodeDefinitionUpdateOne) Save(ctx context.Context) (*NodeDefinition, error) {
	return withHooks(ctx, nduo.sqlSave, nduo.mutation, nduo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (nduo *NodeDefinitionUpdateOne) SaveX(ctx context.Context) *NodeDefinition {
	node, err := nduo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (nduo *NodeDefinitionUpdateOne) Exec(ctx context.Context) error {
	_, err := nduo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nduo *NodeDefinitionUpdateOne) ExecX(ctx context.Context) {
	if err := nduo.Exec(ctx); err != nil {
		panic(err)
	}
}

func (nduo *NodeDefinitionUpdateOne) sqlSave(ctx context.Context) (_node *NodeDefinition, err error) {
	_spec := sqlgraph.NewUpdateSpec(nodedefinition.Table, nodedefinition.Columns, sqlgraph.NewFieldSpec(nodedefinition.FieldID, field.TypeUUID))
	id, ok := nduo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "NodeDefinition.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := nduo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, nodedefinition.FieldID)
		for _, f := range fields {
			if !nodedefinition.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != nodedefinition.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := nduo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := nduo.mutation.PluginName(); ok {
		_spec.SetField(nodedefinition.FieldPluginName, field.TypeString, value)
	}
	if value, ok := nduo.mutation.Name(); ok {
		_spec.SetField(nodedefinition.FieldName, field.TypeString, value)
	}
	if value, ok := nduo.mutation.Author(); ok {
		_spec.SetField(nodedefinition.FieldAuthor, field.TypeString, value)
	}
	if value, ok := nduo.mutation.Description(); ok {
		_spec.SetField(nodedefinition.FieldDescription, field.TypeString, value)
	}
	if value, ok := nduo.mutation.Icon(); ok {
		_spec.SetField(nodedefinition.FieldIcon, field.TypeString, value)
	}
	if value, ok := nduo.mutation.GetType(); ok {
		_spec.SetField(nodedefinition.FieldType, field.TypeString, value)
	}
	if value, ok := nduo.mutation.Version(); ok {
		_spec.SetField(nodedefinition.FieldVersion, field.TypeString, value)
	}
	if value, ok := nduo.mutation.Category(); ok {
		_spec.SetField(nodedefinition.FieldCategory, field.TypeString, value)
	}
	if value, ok := nduo.mutation.InputParams(); ok {
		_spec.SetField(nodedefinition.FieldInputParams, field.TypeJSON, value)
	}
	if value, ok := nduo.mutation.AppendedInputParams(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, nodedefinition.FieldInputParams, value)
		})
	}
	if value, ok := nduo.mutation.OutputParams(); ok {
		_spec.SetField(nodedefinition.FieldOutputParams, field.TypeJSON, value)
	}
	if value, ok := nduo.mutation.AppendedOutputParams(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, nodedefinition.FieldOutputParams, value)
		})
	}
	if value, ok := nduo.mutation.InputPorts(); ok {
		_spec.SetField(nodedefinition.FieldInputPorts, field.TypeJSON, value)
	}
	if value, ok := nduo.mutation.AppendedInputPorts(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, nodedefinition.FieldInputPorts, value)
		})
	}
	if value, ok := nduo.mutation.OutputPorts(); ok {
		_spec.SetField(nodedefinition.FieldOutputPorts, field.TypeJSON, value)
	}
	if value, ok := nduo.mutation.AppendedOutputPorts(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, nodedefinition.FieldOutputPorts, value)
		})
	}
	if value, ok := nduo.mutation.Exception(); ok {
		_spec.SetField(nodedefinition.FieldException, field.TypeBool, value)
	}
	if value, ok := nduo.mutation.Path(); ok {
		_spec.SetField(nodedefinition.FieldPath, field.TypeString, value)
	}
	if value, ok := nduo.mutation.Builtin(); ok {
		_spec.SetField(nodedefinition.FieldBuiltin, field.TypeBool, value)
	}
	if value, ok := nduo.mutation.Enabled(); ok {
		_spec.SetField(nodedefinition.FieldEnabled, field.TypeBool, value)
	}
	if value, ok := nduo.mutation.UpdatedAt(); ok {
		_spec.SetField(nodedefinition.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &NodeDefinition{config: nduo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, nduo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{nodedefinition.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	nduo.mutation.done = true
	return _node, nil
}
