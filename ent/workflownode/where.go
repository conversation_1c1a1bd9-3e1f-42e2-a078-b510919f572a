// Code generated by ent, DO NOT EDIT.

package workflownode

import (
	"resflow/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldID, id))
}

// WorkflowID applies equality check predicate on the "workflow_id" field. It's identical to WorkflowIDEQ.
func WorkflowID(v uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldWorkflowID, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldName, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldDescription, v))
}

// IconType applies equality check predicate on the "icon_type" field. It's identical to IconTypeEQ.
func IconType(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldIconType, v))
}

// IconBgColor applies equality check predicate on the "icon_bg_color" field. It's identical to IconBgColorEQ.
func IconBgColor(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldIconBgColor, v))
}

// IconData applies equality check predicate on the "icon_data" field. It's identical to IconDataEQ.
func IconData(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldIconData, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldType, v))
}

// Version applies equality check predicate on the "version" field. It's identical to VersionEQ.
func Version(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldVersion, v))
}

// PluginName applies equality check predicate on the "plugin_name" field. It's identical to PluginNameEQ.
func PluginName(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldPluginName, v))
}

// PluginVersion applies equality check predicate on the "plugin_version" field. It's identical to PluginVersionEQ.
func PluginVersion(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldPluginVersion, v))
}

// InputParams applies equality check predicate on the "input_params" field. It's identical to InputParamsEQ.
func InputParams(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldInputParams, v))
}

// OutputParams applies equality check predicate on the "output_params" field. It's identical to OutputParamsEQ.
func OutputParams(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldOutputParams, v))
}

// InputPorts applies equality check predicate on the "input_ports" field. It's identical to InputPortsEQ.
func InputPorts(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldInputPorts, v))
}

// OutputPorts applies equality check predicate on the "output_ports" field. It's identical to OutputPortsEQ.
func OutputPorts(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldOutputPorts, v))
}

// Position applies equality check predicate on the "position" field. It's identical to PositionEQ.
func Position(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldPosition, v))
}

// ConfigData applies equality check predicate on the "config_data" field. It's identical to ConfigDataEQ.
func ConfigData(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldConfigData, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldUpdatedAt, v))
}

// WorkflowIDEQ applies the EQ predicate on the "workflow_id" field.
func WorkflowIDEQ(v uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldWorkflowID, v))
}

// WorkflowIDNEQ applies the NEQ predicate on the "workflow_id" field.
func WorkflowIDNEQ(v uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldWorkflowID, v))
}

// WorkflowIDIn applies the In predicate on the "workflow_id" field.
func WorkflowIDIn(vs ...uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldWorkflowID, vs...))
}

// WorkflowIDNotIn applies the NotIn predicate on the "workflow_id" field.
func WorkflowIDNotIn(vs ...uuid.UUID) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldWorkflowID, vs...))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldName, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldDescription, v))
}

// IconTypeEQ applies the EQ predicate on the "icon_type" field.
func IconTypeEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldIconType, v))
}

// IconTypeNEQ applies the NEQ predicate on the "icon_type" field.
func IconTypeNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldIconType, v))
}

// IconTypeIn applies the In predicate on the "icon_type" field.
func IconTypeIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldIconType, vs...))
}

// IconTypeNotIn applies the NotIn predicate on the "icon_type" field.
func IconTypeNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldIconType, vs...))
}

// IconTypeGT applies the GT predicate on the "icon_type" field.
func IconTypeGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldIconType, v))
}

// IconTypeGTE applies the GTE predicate on the "icon_type" field.
func IconTypeGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldIconType, v))
}

// IconTypeLT applies the LT predicate on the "icon_type" field.
func IconTypeLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldIconType, v))
}

// IconTypeLTE applies the LTE predicate on the "icon_type" field.
func IconTypeLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldIconType, v))
}

// IconTypeContains applies the Contains predicate on the "icon_type" field.
func IconTypeContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldIconType, v))
}

// IconTypeHasPrefix applies the HasPrefix predicate on the "icon_type" field.
func IconTypeHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldIconType, v))
}

// IconTypeHasSuffix applies the HasSuffix predicate on the "icon_type" field.
func IconTypeHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldIconType, v))
}

// IconTypeEqualFold applies the EqualFold predicate on the "icon_type" field.
func IconTypeEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldIconType, v))
}

// IconTypeContainsFold applies the ContainsFold predicate on the "icon_type" field.
func IconTypeContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldIconType, v))
}

// IconBgColorEQ applies the EQ predicate on the "icon_bg_color" field.
func IconBgColorEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldIconBgColor, v))
}

// IconBgColorNEQ applies the NEQ predicate on the "icon_bg_color" field.
func IconBgColorNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldIconBgColor, v))
}

// IconBgColorIn applies the In predicate on the "icon_bg_color" field.
func IconBgColorIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldIconBgColor, vs...))
}

// IconBgColorNotIn applies the NotIn predicate on the "icon_bg_color" field.
func IconBgColorNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldIconBgColor, vs...))
}

// IconBgColorGT applies the GT predicate on the "icon_bg_color" field.
func IconBgColorGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldIconBgColor, v))
}

// IconBgColorGTE applies the GTE predicate on the "icon_bg_color" field.
func IconBgColorGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldIconBgColor, v))
}

// IconBgColorLT applies the LT predicate on the "icon_bg_color" field.
func IconBgColorLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldIconBgColor, v))
}

// IconBgColorLTE applies the LTE predicate on the "icon_bg_color" field.
func IconBgColorLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldIconBgColor, v))
}

// IconBgColorContains applies the Contains predicate on the "icon_bg_color" field.
func IconBgColorContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldIconBgColor, v))
}

// IconBgColorHasPrefix applies the HasPrefix predicate on the "icon_bg_color" field.
func IconBgColorHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldIconBgColor, v))
}

// IconBgColorHasSuffix applies the HasSuffix predicate on the "icon_bg_color" field.
func IconBgColorHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldIconBgColor, v))
}

// IconBgColorEqualFold applies the EqualFold predicate on the "icon_bg_color" field.
func IconBgColorEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldIconBgColor, v))
}

// IconBgColorContainsFold applies the ContainsFold predicate on the "icon_bg_color" field.
func IconBgColorContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldIconBgColor, v))
}

// IconDataEQ applies the EQ predicate on the "icon_data" field.
func IconDataEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldIconData, v))
}

// IconDataNEQ applies the NEQ predicate on the "icon_data" field.
func IconDataNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldIconData, v))
}

// IconDataIn applies the In predicate on the "icon_data" field.
func IconDataIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldIconData, vs...))
}

// IconDataNotIn applies the NotIn predicate on the "icon_data" field.
func IconDataNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldIconData, vs...))
}

// IconDataGT applies the GT predicate on the "icon_data" field.
func IconDataGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldIconData, v))
}

// IconDataGTE applies the GTE predicate on the "icon_data" field.
func IconDataGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldIconData, v))
}

// IconDataLT applies the LT predicate on the "icon_data" field.
func IconDataLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldIconData, v))
}

// IconDataLTE applies the LTE predicate on the "icon_data" field.
func IconDataLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldIconData, v))
}

// IconDataContains applies the Contains predicate on the "icon_data" field.
func IconDataContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldIconData, v))
}

// IconDataHasPrefix applies the HasPrefix predicate on the "icon_data" field.
func IconDataHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldIconData, v))
}

// IconDataHasSuffix applies the HasSuffix predicate on the "icon_data" field.
func IconDataHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldIconData, v))
}

// IconDataEqualFold applies the EqualFold predicate on the "icon_data" field.
func IconDataEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldIconData, v))
}

// IconDataContainsFold applies the ContainsFold predicate on the "icon_data" field.
func IconDataContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldIconData, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldType, v))
}

// VersionEQ applies the EQ predicate on the "version" field.
func VersionEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldVersion, v))
}

// VersionNEQ applies the NEQ predicate on the "version" field.
func VersionNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldVersion, v))
}

// VersionIn applies the In predicate on the "version" field.
func VersionIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldVersion, vs...))
}

// VersionNotIn applies the NotIn predicate on the "version" field.
func VersionNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldVersion, vs...))
}

// VersionGT applies the GT predicate on the "version" field.
func VersionGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldVersion, v))
}

// VersionGTE applies the GTE predicate on the "version" field.
func VersionGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldVersion, v))
}

// VersionLT applies the LT predicate on the "version" field.
func VersionLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldVersion, v))
}

// VersionLTE applies the LTE predicate on the "version" field.
func VersionLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldVersion, v))
}

// VersionContains applies the Contains predicate on the "version" field.
func VersionContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldVersion, v))
}

// VersionHasPrefix applies the HasPrefix predicate on the "version" field.
func VersionHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldVersion, v))
}

// VersionHasSuffix applies the HasSuffix predicate on the "version" field.
func VersionHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldVersion, v))
}

// VersionEqualFold applies the EqualFold predicate on the "version" field.
func VersionEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldVersion, v))
}

// VersionContainsFold applies the ContainsFold predicate on the "version" field.
func VersionContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldVersion, v))
}

// PluginNameEQ applies the EQ predicate on the "plugin_name" field.
func PluginNameEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldPluginName, v))
}

// PluginNameNEQ applies the NEQ predicate on the "plugin_name" field.
func PluginNameNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldPluginName, v))
}

// PluginNameIn applies the In predicate on the "plugin_name" field.
func PluginNameIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldPluginName, vs...))
}

// PluginNameNotIn applies the NotIn predicate on the "plugin_name" field.
func PluginNameNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldPluginName, vs...))
}

// PluginNameGT applies the GT predicate on the "plugin_name" field.
func PluginNameGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldPluginName, v))
}

// PluginNameGTE applies the GTE predicate on the "plugin_name" field.
func PluginNameGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldPluginName, v))
}

// PluginNameLT applies the LT predicate on the "plugin_name" field.
func PluginNameLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldPluginName, v))
}

// PluginNameLTE applies the LTE predicate on the "plugin_name" field.
func PluginNameLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldPluginName, v))
}

// PluginNameContains applies the Contains predicate on the "plugin_name" field.
func PluginNameContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldPluginName, v))
}

// PluginNameHasPrefix applies the HasPrefix predicate on the "plugin_name" field.
func PluginNameHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldPluginName, v))
}

// PluginNameHasSuffix applies the HasSuffix predicate on the "plugin_name" field.
func PluginNameHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldPluginName, v))
}

// PluginNameEqualFold applies the EqualFold predicate on the "plugin_name" field.
func PluginNameEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldPluginName, v))
}

// PluginNameContainsFold applies the ContainsFold predicate on the "plugin_name" field.
func PluginNameContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldPluginName, v))
}

// PluginVersionEQ applies the EQ predicate on the "plugin_version" field.
func PluginVersionEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldPluginVersion, v))
}

// PluginVersionNEQ applies the NEQ predicate on the "plugin_version" field.
func PluginVersionNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldPluginVersion, v))
}

// PluginVersionIn applies the In predicate on the "plugin_version" field.
func PluginVersionIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldPluginVersion, vs...))
}

// PluginVersionNotIn applies the NotIn predicate on the "plugin_version" field.
func PluginVersionNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldPluginVersion, vs...))
}

// PluginVersionGT applies the GT predicate on the "plugin_version" field.
func PluginVersionGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldPluginVersion, v))
}

// PluginVersionGTE applies the GTE predicate on the "plugin_version" field.
func PluginVersionGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldPluginVersion, v))
}

// PluginVersionLT applies the LT predicate on the "plugin_version" field.
func PluginVersionLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldPluginVersion, v))
}

// PluginVersionLTE applies the LTE predicate on the "plugin_version" field.
func PluginVersionLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldPluginVersion, v))
}

// PluginVersionContains applies the Contains predicate on the "plugin_version" field.
func PluginVersionContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldPluginVersion, v))
}

// PluginVersionHasPrefix applies the HasPrefix predicate on the "plugin_version" field.
func PluginVersionHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldPluginVersion, v))
}

// PluginVersionHasSuffix applies the HasSuffix predicate on the "plugin_version" field.
func PluginVersionHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldPluginVersion, v))
}

// PluginVersionEqualFold applies the EqualFold predicate on the "plugin_version" field.
func PluginVersionEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldPluginVersion, v))
}

// PluginVersionContainsFold applies the ContainsFold predicate on the "plugin_version" field.
func PluginVersionContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldPluginVersion, v))
}

// InputParamsEQ applies the EQ predicate on the "input_params" field.
func InputParamsEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldInputParams, v))
}

// InputParamsNEQ applies the NEQ predicate on the "input_params" field.
func InputParamsNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldInputParams, v))
}

// InputParamsIn applies the In predicate on the "input_params" field.
func InputParamsIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldInputParams, vs...))
}

// InputParamsNotIn applies the NotIn predicate on the "input_params" field.
func InputParamsNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldInputParams, vs...))
}

// InputParamsGT applies the GT predicate on the "input_params" field.
func InputParamsGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldInputParams, v))
}

// InputParamsGTE applies the GTE predicate on the "input_params" field.
func InputParamsGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldInputParams, v))
}

// InputParamsLT applies the LT predicate on the "input_params" field.
func InputParamsLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldInputParams, v))
}

// InputParamsLTE applies the LTE predicate on the "input_params" field.
func InputParamsLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldInputParams, v))
}

// InputParamsContains applies the Contains predicate on the "input_params" field.
func InputParamsContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldInputParams, v))
}

// InputParamsHasPrefix applies the HasPrefix predicate on the "input_params" field.
func InputParamsHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldInputParams, v))
}

// InputParamsHasSuffix applies the HasSuffix predicate on the "input_params" field.
func InputParamsHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldInputParams, v))
}

// InputParamsEqualFold applies the EqualFold predicate on the "input_params" field.
func InputParamsEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldInputParams, v))
}

// InputParamsContainsFold applies the ContainsFold predicate on the "input_params" field.
func InputParamsContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldInputParams, v))
}

// OutputParamsEQ applies the EQ predicate on the "output_params" field.
func OutputParamsEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldOutputParams, v))
}

// OutputParamsNEQ applies the NEQ predicate on the "output_params" field.
func OutputParamsNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldOutputParams, v))
}

// OutputParamsIn applies the In predicate on the "output_params" field.
func OutputParamsIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldOutputParams, vs...))
}

// OutputParamsNotIn applies the NotIn predicate on the "output_params" field.
func OutputParamsNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldOutputParams, vs...))
}

// OutputParamsGT applies the GT predicate on the "output_params" field.
func OutputParamsGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldOutputParams, v))
}

// OutputParamsGTE applies the GTE predicate on the "output_params" field.
func OutputParamsGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldOutputParams, v))
}

// OutputParamsLT applies the LT predicate on the "output_params" field.
func OutputParamsLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldOutputParams, v))
}

// OutputParamsLTE applies the LTE predicate on the "output_params" field.
func OutputParamsLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldOutputParams, v))
}

// OutputParamsContains applies the Contains predicate on the "output_params" field.
func OutputParamsContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldOutputParams, v))
}

// OutputParamsHasPrefix applies the HasPrefix predicate on the "output_params" field.
func OutputParamsHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldOutputParams, v))
}

// OutputParamsHasSuffix applies the HasSuffix predicate on the "output_params" field.
func OutputParamsHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldOutputParams, v))
}

// OutputParamsEqualFold applies the EqualFold predicate on the "output_params" field.
func OutputParamsEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldOutputParams, v))
}

// OutputParamsContainsFold applies the ContainsFold predicate on the "output_params" field.
func OutputParamsContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldOutputParams, v))
}

// InputPortsEQ applies the EQ predicate on the "input_ports" field.
func InputPortsEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldInputPorts, v))
}

// InputPortsNEQ applies the NEQ predicate on the "input_ports" field.
func InputPortsNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldInputPorts, v))
}

// InputPortsIn applies the In predicate on the "input_ports" field.
func InputPortsIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldInputPorts, vs...))
}

// InputPortsNotIn applies the NotIn predicate on the "input_ports" field.
func InputPortsNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldInputPorts, vs...))
}

// InputPortsGT applies the GT predicate on the "input_ports" field.
func InputPortsGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldInputPorts, v))
}

// InputPortsGTE applies the GTE predicate on the "input_ports" field.
func InputPortsGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldInputPorts, v))
}

// InputPortsLT applies the LT predicate on the "input_ports" field.
func InputPortsLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldInputPorts, v))
}

// InputPortsLTE applies the LTE predicate on the "input_ports" field.
func InputPortsLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldInputPorts, v))
}

// InputPortsContains applies the Contains predicate on the "input_ports" field.
func InputPortsContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldInputPorts, v))
}

// InputPortsHasPrefix applies the HasPrefix predicate on the "input_ports" field.
func InputPortsHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldInputPorts, v))
}

// InputPortsHasSuffix applies the HasSuffix predicate on the "input_ports" field.
func InputPortsHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldInputPorts, v))
}

// InputPortsEqualFold applies the EqualFold predicate on the "input_ports" field.
func InputPortsEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldInputPorts, v))
}

// InputPortsContainsFold applies the ContainsFold predicate on the "input_ports" field.
func InputPortsContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldInputPorts, v))
}

// OutputPortsEQ applies the EQ predicate on the "output_ports" field.
func OutputPortsEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldOutputPorts, v))
}

// OutputPortsNEQ applies the NEQ predicate on the "output_ports" field.
func OutputPortsNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldOutputPorts, v))
}

// OutputPortsIn applies the In predicate on the "output_ports" field.
func OutputPortsIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldOutputPorts, vs...))
}

// OutputPortsNotIn applies the NotIn predicate on the "output_ports" field.
func OutputPortsNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldOutputPorts, vs...))
}

// OutputPortsGT applies the GT predicate on the "output_ports" field.
func OutputPortsGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldOutputPorts, v))
}

// OutputPortsGTE applies the GTE predicate on the "output_ports" field.
func OutputPortsGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldOutputPorts, v))
}

// OutputPortsLT applies the LT predicate on the "output_ports" field.
func OutputPortsLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldOutputPorts, v))
}

// OutputPortsLTE applies the LTE predicate on the "output_ports" field.
func OutputPortsLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldOutputPorts, v))
}

// OutputPortsContains applies the Contains predicate on the "output_ports" field.
func OutputPortsContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldOutputPorts, v))
}

// OutputPortsHasPrefix applies the HasPrefix predicate on the "output_ports" field.
func OutputPortsHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldOutputPorts, v))
}

// OutputPortsHasSuffix applies the HasSuffix predicate on the "output_ports" field.
func OutputPortsHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldOutputPorts, v))
}

// OutputPortsEqualFold applies the EqualFold predicate on the "output_ports" field.
func OutputPortsEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldOutputPorts, v))
}

// OutputPortsContainsFold applies the ContainsFold predicate on the "output_ports" field.
func OutputPortsContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldOutputPorts, v))
}

// PositionEQ applies the EQ predicate on the "position" field.
func PositionEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldPosition, v))
}

// PositionNEQ applies the NEQ predicate on the "position" field.
func PositionNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldPosition, v))
}

// PositionIn applies the In predicate on the "position" field.
func PositionIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldPosition, vs...))
}

// PositionNotIn applies the NotIn predicate on the "position" field.
func PositionNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldPosition, vs...))
}

// PositionGT applies the GT predicate on the "position" field.
func PositionGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldPosition, v))
}

// PositionGTE applies the GTE predicate on the "position" field.
func PositionGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldPosition, v))
}

// PositionLT applies the LT predicate on the "position" field.
func PositionLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldPosition, v))
}

// PositionLTE applies the LTE predicate on the "position" field.
func PositionLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldPosition, v))
}

// PositionContains applies the Contains predicate on the "position" field.
func PositionContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldPosition, v))
}

// PositionHasPrefix applies the HasPrefix predicate on the "position" field.
func PositionHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldPosition, v))
}

// PositionHasSuffix applies the HasSuffix predicate on the "position" field.
func PositionHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldPosition, v))
}

// PositionEqualFold applies the EqualFold predicate on the "position" field.
func PositionEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldPosition, v))
}

// PositionContainsFold applies the ContainsFold predicate on the "position" field.
func PositionContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldPosition, v))
}

// ConfigDataEQ applies the EQ predicate on the "config_data" field.
func ConfigDataEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldConfigData, v))
}

// ConfigDataNEQ applies the NEQ predicate on the "config_data" field.
func ConfigDataNEQ(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldConfigData, v))
}

// ConfigDataIn applies the In predicate on the "config_data" field.
func ConfigDataIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldConfigData, vs...))
}

// ConfigDataNotIn applies the NotIn predicate on the "config_data" field.
func ConfigDataNotIn(vs ...string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldConfigData, vs...))
}

// ConfigDataGT applies the GT predicate on the "config_data" field.
func ConfigDataGT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldConfigData, v))
}

// ConfigDataGTE applies the GTE predicate on the "config_data" field.
func ConfigDataGTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldConfigData, v))
}

// ConfigDataLT applies the LT predicate on the "config_data" field.
func ConfigDataLT(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldConfigData, v))
}

// ConfigDataLTE applies the LTE predicate on the "config_data" field.
func ConfigDataLTE(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldConfigData, v))
}

// ConfigDataContains applies the Contains predicate on the "config_data" field.
func ConfigDataContains(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContains(FieldConfigData, v))
}

// ConfigDataHasPrefix applies the HasPrefix predicate on the "config_data" field.
func ConfigDataHasPrefix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasPrefix(FieldConfigData, v))
}

// ConfigDataHasSuffix applies the HasSuffix predicate on the "config_data" field.
func ConfigDataHasSuffix(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldHasSuffix(FieldConfigData, v))
}

// ConfigDataIsNil applies the IsNil predicate on the "config_data" field.
func ConfigDataIsNil() predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIsNull(FieldConfigData))
}

// ConfigDataNotNil applies the NotNil predicate on the "config_data" field.
func ConfigDataNotNil() predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotNull(FieldConfigData))
}

// ConfigDataEqualFold applies the EqualFold predicate on the "config_data" field.
func ConfigDataEqualFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEqualFold(FieldConfigData, v))
}

// ConfigDataContainsFold applies the ContainsFold predicate on the "config_data" field.
func ConfigDataContainsFold(v string) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldContainsFold(FieldConfigData, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.FieldLTE(FieldUpdatedAt, v))
}

// HasWorkflow applies the HasEdge predicate on the "workflow" edge.
func HasWorkflow() predicate.WorkflowNode {
	return predicate.WorkflowNode(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, WorkflowTable, WorkflowColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasWorkflowWith applies the HasEdge predicate on the "workflow" edge with a given conditions (other predicates).
func HasWorkflowWith(preds ...predicate.Workflow) predicate.WorkflowNode {
	return predicate.WorkflowNode(func(s *sql.Selector) {
		step := newWorkflowStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WorkflowNode) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WorkflowNode) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WorkflowNode) predicate.WorkflowNode {
	return predicate.WorkflowNode(sql.NotPredicates(p))
}
