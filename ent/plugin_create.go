// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"resflow/ent/plugin"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// PluginCreate is the builder for creating a Plugin entity.
type PluginCreate struct {
	config
	mutation *PluginMutation
	hooks    []Hook
}

// SetName sets the "name" field.
func (pc *PluginCreate) SetName(s string) *PluginCreate {
	pc.mutation.SetName(s)
	return pc
}

// SetVersion sets the "version" field.
func (pc *PluginCreate) SetVersion(s string) *PluginCreate {
	pc.mutation.SetVersion(s)
	return pc
}

// SetAuthor sets the "author" field.
func (pc *PluginCreate) SetAuthor(s string) *PluginCreate {
	pc.mutation.SetAuthor(s)
	return pc
}

// SetDisplayName sets the "display_name" field.
func (pc *PluginCreate) SetDisplayName(s string) *PluginCreate {
	pc.mutation.SetDisplayName(s)
	return pc
}

// SetDescription sets the "description" field.
func (pc *PluginCreate) SetDescription(s string) *PluginCreate {
	pc.mutation.SetDescription(s)
	return pc
}

// SetIcon sets the "icon" field.
func (pc *PluginCreate) SetIcon(s string) *PluginCreate {
	pc.mutation.SetIcon(s)
	return pc
}

// SetPath sets the "path" field.
func (pc *PluginCreate) SetPath(s string) *PluginCreate {
	pc.mutation.SetPath(s)
	return pc
}

// SetBuiltin sets the "builtin" field.
func (pc *PluginCreate) SetBuiltin(b bool) *PluginCreate {
	pc.mutation.SetBuiltin(b)
	return pc
}

// SetEnabled sets the "enabled" field.
func (pc *PluginCreate) SetEnabled(b bool) *PluginCreate {
	pc.mutation.SetEnabled(b)
	return pc
}

// SetCreatedAt sets the "created_at" field.
func (pc *PluginCreate) SetCreatedAt(t time.Time) *PluginCreate {
	pc.mutation.SetCreatedAt(t)
	return pc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (pc *PluginCreate) SetNillableCreatedAt(t *time.Time) *PluginCreate {
	if t != nil {
		pc.SetCreatedAt(*t)
	}
	return pc
}

// SetUpdatedAt sets the "updated_at" field.
func (pc *PluginCreate) SetUpdatedAt(t time.Time) *PluginCreate {
	pc.mutation.SetUpdatedAt(t)
	return pc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (pc *PluginCreate) SetNillableUpdatedAt(t *time.Time) *PluginCreate {
	if t != nil {
		pc.SetUpdatedAt(*t)
	}
	return pc
}

// SetID sets the "id" field.
func (pc *PluginCreate) SetID(u uuid.UUID) *PluginCreate {
	pc.mutation.SetID(u)
	return pc
}

// Mutation returns the PluginMutation object of the builder.
func (pc *PluginCreate) Mutation() *PluginMutation {
	return pc.mutation
}

// Save creates the Plugin in the database.
func (pc *PluginCreate) Save(ctx context.Context) (*Plugin, error) {
	pc.defaults()
	return withHooks(ctx, pc.sqlSave, pc.mutation, pc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (pc *PluginCreate) SaveX(ctx context.Context) *Plugin {
	v, err := pc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pc *PluginCreate) Exec(ctx context.Context) error {
	_, err := pc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pc *PluginCreate) ExecX(ctx context.Context) {
	if err := pc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pc *PluginCreate) defaults() {
	if _, ok := pc.mutation.CreatedAt(); !ok {
		v := plugin.DefaultCreatedAt()
		pc.mutation.SetCreatedAt(v)
	}
	if _, ok := pc.mutation.UpdatedAt(); !ok {
		v := plugin.DefaultUpdatedAt()
		pc.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (pc *PluginCreate) check() error {
	if _, ok := pc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Plugin.name"`)}
	}
	if _, ok := pc.mutation.Version(); !ok {
		return &ValidationError{Name: "version", err: errors.New(`ent: missing required field "Plugin.version"`)}
	}
	if _, ok := pc.mutation.Author(); !ok {
		return &ValidationError{Name: "author", err: errors.New(`ent: missing required field "Plugin.author"`)}
	}
	if _, ok := pc.mutation.DisplayName(); !ok {
		return &ValidationError{Name: "display_name", err: errors.New(`ent: missing required field "Plugin.display_name"`)}
	}
	if _, ok := pc.mutation.Description(); !ok {
		return &ValidationError{Name: "description", err: errors.New(`ent: missing required field "Plugin.description"`)}
	}
	if _, ok := pc.mutation.Icon(); !ok {
		return &ValidationError{Name: "icon", err: errors.New(`ent: missing required field "Plugin.icon"`)}
	}
	if _, ok := pc.mutation.Path(); !ok {
		return &ValidationError{Name: "path", err: errors.New(`ent: missing required field "Plugin.path"`)}
	}
	if _, ok := pc.mutation.Builtin(); !ok {
		return &ValidationError{Name: "builtin", err: errors.New(`ent: missing required field "Plugin.builtin"`)}
	}
	if _, ok := pc.mutation.Enabled(); !ok {
		return &ValidationError{Name: "enabled", err: errors.New(`ent: missing required field "Plugin.enabled"`)}
	}
	if _, ok := pc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Plugin.created_at"`)}
	}
	if _, ok := pc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Plugin.updated_at"`)}
	}
	return nil
}

func (pc *PluginCreate) sqlSave(ctx context.Context) (*Plugin, error) {
	if err := pc.check(); err != nil {
		return nil, err
	}
	_node, _spec := pc.createSpec()
	if err := sqlgraph.CreateNode(ctx, pc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	pc.mutation.id = &_node.ID
	pc.mutation.done = true
	return _node, nil
}

func (pc *PluginCreate) createSpec() (*Plugin, *sqlgraph.CreateSpec) {
	var (
		_node = &Plugin{config: pc.config}
		_spec = sqlgraph.NewCreateSpec(plugin.Table, sqlgraph.NewFieldSpec(plugin.FieldID, field.TypeUUID))
	)
	if id, ok := pc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := pc.mutation.Name(); ok {
		_spec.SetField(plugin.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := pc.mutation.Version(); ok {
		_spec.SetField(plugin.FieldVersion, field.TypeString, value)
		_node.Version = value
	}
	if value, ok := pc.mutation.Author(); ok {
		_spec.SetField(plugin.FieldAuthor, field.TypeString, value)
		_node.Author = value
	}
	if value, ok := pc.mutation.DisplayName(); ok {
		_spec.SetField(plugin.FieldDisplayName, field.TypeString, value)
		_node.DisplayName = value
	}
	if value, ok := pc.mutation.Description(); ok {
		_spec.SetField(plugin.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := pc.mutation.Icon(); ok {
		_spec.SetField(plugin.FieldIcon, field.TypeString, value)
		_node.Icon = value
	}
	if value, ok := pc.mutation.Path(); ok {
		_spec.SetField(plugin.FieldPath, field.TypeString, value)
		_node.Path = value
	}
	if value, ok := pc.mutation.Builtin(); ok {
		_spec.SetField(plugin.FieldBuiltin, field.TypeBool, value)
		_node.Builtin = value
	}
	if value, ok := pc.mutation.Enabled(); ok {
		_spec.SetField(plugin.FieldEnabled, field.TypeBool, value)
		_node.Enabled = value
	}
	if value, ok := pc.mutation.CreatedAt(); ok {
		_spec.SetField(plugin.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := pc.mutation.UpdatedAt(); ok {
		_spec.SetField(plugin.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	return _node, _spec
}

// PluginCreateBulk is the builder for creating many Plugin entities in bulk.
type PluginCreateBulk struct {
	config
	err      error
	builders []*PluginCreate
}

// Save creates the Plugin entities in the database.
func (pcb *PluginCreateBulk) Save(ctx context.Context) ([]*Plugin, error) {
	if pcb.err != nil {
		return nil, pcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(pcb.builders))
	nodes := make([]*Plugin, len(pcb.builders))
	mutators := make([]Mutator, len(pcb.builders))
	for i := range pcb.builders {
		func(i int, root context.Context) {
			builder := pcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*PluginMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, pcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, pcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, pcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (pcb *PluginCreateBulk) SaveX(ctx context.Context) []*Plugin {
	v, err := pcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pcb *PluginCreateBulk) Exec(ctx context.Context) error {
	_, err := pcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pcb *PluginCreateBulk) ExecX(ctx context.Context) {
	if err := pcb.Exec(ctx); err != nil {
		panic(err)
	}
}
