// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"resflow/ent/nodedefinition"
	"resflow/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// NodeDefinitionDelete is the builder for deleting a NodeDefinition entity.
type NodeDefinitionDelete struct {
	config
	hooks    []Hook
	mutation *NodeDefinitionMutation
}

// Where appends a list predicates to the NodeDefinitionDelete builder.
func (ndd *NodeDefinitionDelete) Where(ps ...predicate.NodeDefinition) *NodeDefinitionDelete {
	ndd.mutation.Where(ps...)
	return ndd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (ndd *NodeDefinitionDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, ndd.sqlExec, ndd.mutation, ndd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (ndd *NodeDefinitionDelete) ExecX(ctx context.Context) int {
	n, err := ndd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (ndd *NodeDefinitionDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(nodedefinition.Table, sqlgraph.NewFieldSpec(nodedefinition.FieldID, field.TypeUUID))
	if ps := ndd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, ndd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	ndd.mutation.done = true
	return affected, err
}

// NodeDefinitionDeleteOne is the builder for deleting a single NodeDefinition entity.
type NodeDefinitionDeleteOne struct {
	ndd *NodeDefinitionDelete
}

// Where appends a list predicates to the NodeDefinitionDelete builder.
func (nddo *NodeDefinitionDeleteOne) Where(ps ...predicate.NodeDefinition) *NodeDefinitionDeleteOne {
	nddo.ndd.mutation.Where(ps...)
	return nddo
}

// Exec executes the deletion query.
func (nddo *NodeDefinitionDeleteOne) Exec(ctx context.Context) error {
	n, err := nddo.ndd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{nodedefinition.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (nddo *NodeDefinitionDeleteOne) ExecX(ctx context.Context) {
	if err := nddo.Exec(ctx); err != nil {
		panic(err)
	}
}
