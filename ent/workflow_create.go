// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"resflow/ent/user"
	"resflow/ent/workflow"
	"resflow/ent/workflownode"
	"resflow/ent/workflowsedge"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// WorkflowCreate is the builder for creating a Workflow entity.
type WorkflowCreate struct {
	config
	mutation *WorkflowMutation
	hooks    []Hook
}

// SetUserID sets the "user_id" field.
func (wc *WorkflowCreate) SetUserID(u uuid.UUID) *WorkflowCreate {
	wc.mutation.SetUserID(u)
	return wc
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (wc *WorkflowCreate) SetNillableUserID(u *uuid.UUID) *WorkflowCreate {
	if u != nil {
		wc.SetUserID(*u)
	}
	return wc
}

// SetName sets the "name" field.
func (wc *WorkflowCreate) SetName(s string) *WorkflowCreate {
	wc.mutation.SetName(s)
	return wc
}

// SetIconType sets the "icon_type" field.
func (wc *WorkflowCreate) SetIconType(s string) *WorkflowCreate {
	wc.mutation.SetIconType(s)
	return wc
}

// SetIconBgColor sets the "icon_bg_color" field.
func (wc *WorkflowCreate) SetIconBgColor(s string) *WorkflowCreate {
	wc.mutation.SetIconBgColor(s)
	return wc
}

// SetIconData sets the "icon_data" field.
func (wc *WorkflowCreate) SetIconData(s string) *WorkflowCreate {
	wc.mutation.SetIconData(s)
	return wc
}

// SetDescription sets the "description" field.
func (wc *WorkflowCreate) SetDescription(s string) *WorkflowCreate {
	wc.mutation.SetDescription(s)
	return wc
}

// SetStatus sets the "status" field.
func (wc *WorkflowCreate) SetStatus(i int) *WorkflowCreate {
	wc.mutation.SetStatus(i)
	return wc
}

// SetViewport sets the "viewport" field.
func (wc *WorkflowCreate) SetViewport(s string) *WorkflowCreate {
	wc.mutation.SetViewport(s)
	return wc
}

// SetCreatedAt sets the "created_at" field.
func (wc *WorkflowCreate) SetCreatedAt(t time.Time) *WorkflowCreate {
	wc.mutation.SetCreatedAt(t)
	return wc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (wc *WorkflowCreate) SetNillableCreatedAt(t *time.Time) *WorkflowCreate {
	if t != nil {
		wc.SetCreatedAt(*t)
	}
	return wc
}

// SetUpdatedAt sets the "updated_at" field.
func (wc *WorkflowCreate) SetUpdatedAt(t time.Time) *WorkflowCreate {
	wc.mutation.SetUpdatedAt(t)
	return wc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wc *WorkflowCreate) SetNillableUpdatedAt(t *time.Time) *WorkflowCreate {
	if t != nil {
		wc.SetUpdatedAt(*t)
	}
	return wc
}

// SetID sets the "id" field.
func (wc *WorkflowCreate) SetID(u uuid.UUID) *WorkflowCreate {
	wc.mutation.SetID(u)
	return wc
}

// SetUser sets the "user" edge to the User entity.
func (wc *WorkflowCreate) SetUser(u *User) *WorkflowCreate {
	return wc.SetUserID(u.ID)
}

// AddNodeIDs adds the "nodes" edge to the WorkflowNode entity by IDs.
func (wc *WorkflowCreate) AddNodeIDs(ids ...uuid.UUID) *WorkflowCreate {
	wc.mutation.AddNodeIDs(ids...)
	return wc
}

// AddNodes adds the "nodes" edges to the WorkflowNode entity.
func (wc *WorkflowCreate) AddNodes(w ...*WorkflowNode) *WorkflowCreate {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wc.AddNodeIDs(ids...)
}

// AddWorkflowEdgeIDs adds the "workflow_edges" edge to the WorkflowsEdge entity by IDs.
func (wc *WorkflowCreate) AddWorkflowEdgeIDs(ids ...uuid.UUID) *WorkflowCreate {
	wc.mutation.AddWorkflowEdgeIDs(ids...)
	return wc
}

// AddWorkflowEdges adds the "workflow_edges" edges to the WorkflowsEdge entity.
func (wc *WorkflowCreate) AddWorkflowEdges(w ...*WorkflowsEdge) *WorkflowCreate {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wc.AddWorkflowEdgeIDs(ids...)
}

// Mutation returns the WorkflowMutation object of the builder.
func (wc *WorkflowCreate) Mutation() *WorkflowMutation {
	return wc.mutation
}

// Save creates the Workflow in the database.
func (wc *WorkflowCreate) Save(ctx context.Context) (*Workflow, error) {
	wc.defaults()
	return withHooks(ctx, wc.sqlSave, wc.mutation, wc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (wc *WorkflowCreate) SaveX(ctx context.Context) *Workflow {
	v, err := wc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wc *WorkflowCreate) Exec(ctx context.Context) error {
	_, err := wc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wc *WorkflowCreate) ExecX(ctx context.Context) {
	if err := wc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wc *WorkflowCreate) defaults() {
	if _, ok := wc.mutation.CreatedAt(); !ok {
		v := workflow.DefaultCreatedAt()
		wc.mutation.SetCreatedAt(v)
	}
	if _, ok := wc.mutation.UpdatedAt(); !ok {
		v := workflow.DefaultUpdatedAt()
		wc.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wc *WorkflowCreate) check() error {
	if _, ok := wc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Workflow.name"`)}
	}
	if _, ok := wc.mutation.IconType(); !ok {
		return &ValidationError{Name: "icon_type", err: errors.New(`ent: missing required field "Workflow.icon_type"`)}
	}
	if _, ok := wc.mutation.IconBgColor(); !ok {
		return &ValidationError{Name: "icon_bg_color", err: errors.New(`ent: missing required field "Workflow.icon_bg_color"`)}
	}
	if _, ok := wc.mutation.IconData(); !ok {
		return &ValidationError{Name: "icon_data", err: errors.New(`ent: missing required field "Workflow.icon_data"`)}
	}
	if _, ok := wc.mutation.Description(); !ok {
		return &ValidationError{Name: "description", err: errors.New(`ent: missing required field "Workflow.description"`)}
	}
	if _, ok := wc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Workflow.status"`)}
	}
	if _, ok := wc.mutation.Viewport(); !ok {
		return &ValidationError{Name: "viewport", err: errors.New(`ent: missing required field "Workflow.viewport"`)}
	}
	if _, ok := wc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Workflow.created_at"`)}
	}
	if _, ok := wc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Workflow.updated_at"`)}
	}
	return nil
}

func (wc *WorkflowCreate) sqlSave(ctx context.Context) (*Workflow, error) {
	if err := wc.check(); err != nil {
		return nil, err
	}
	_node, _spec := wc.createSpec()
	if err := sqlgraph.CreateNode(ctx, wc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	wc.mutation.id = &_node.ID
	wc.mutation.done = true
	return _node, nil
}

func (wc *WorkflowCreate) createSpec() (*Workflow, *sqlgraph.CreateSpec) {
	var (
		_node = &Workflow{config: wc.config}
		_spec = sqlgraph.NewCreateSpec(workflow.Table, sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID))
	)
	if id, ok := wc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := wc.mutation.Name(); ok {
		_spec.SetField(workflow.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := wc.mutation.IconType(); ok {
		_spec.SetField(workflow.FieldIconType, field.TypeString, value)
		_node.IconType = value
	}
	if value, ok := wc.mutation.IconBgColor(); ok {
		_spec.SetField(workflow.FieldIconBgColor, field.TypeString, value)
		_node.IconBgColor = value
	}
	if value, ok := wc.mutation.IconData(); ok {
		_spec.SetField(workflow.FieldIconData, field.TypeString, value)
		_node.IconData = value
	}
	if value, ok := wc.mutation.Description(); ok {
		_spec.SetField(workflow.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := wc.mutation.Status(); ok {
		_spec.SetField(workflow.FieldStatus, field.TypeInt, value)
		_node.Status = value
	}
	if value, ok := wc.mutation.Viewport(); ok {
		_spec.SetField(workflow.FieldViewport, field.TypeString, value)
		_node.Viewport = value
	}
	if value, ok := wc.mutation.CreatedAt(); ok {
		_spec.SetField(workflow.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := wc.mutation.UpdatedAt(); ok {
		_spec.SetField(workflow.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if nodes := wc.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflow.UserTable,
			Columns: []string{workflow.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.UserID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := wc.mutation.NodesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.NodesTable,
			Columns: []string{workflow.NodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := wc.mutation.WorkflowEdgesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.WorkflowEdgesTable,
			Columns: []string{workflow.WorkflowEdgesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflowsedge.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// WorkflowCreateBulk is the builder for creating many Workflow entities in bulk.
type WorkflowCreateBulk struct {
	config
	err      error
	builders []*WorkflowCreate
}

// Save creates the Workflow entities in the database.
func (wcb *WorkflowCreateBulk) Save(ctx context.Context) ([]*Workflow, error) {
	if wcb.err != nil {
		return nil, wcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(wcb.builders))
	nodes := make([]*Workflow, len(wcb.builders))
	mutators := make([]Mutator, len(wcb.builders))
	for i := range wcb.builders {
		func(i int, root context.Context) {
			builder := wcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*WorkflowMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, wcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, wcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, wcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (wcb *WorkflowCreateBulk) SaveX(ctx context.Context) []*Workflow {
	v, err := wcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wcb *WorkflowCreateBulk) Exec(ctx context.Context) error {
	_, err := wcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wcb *WorkflowCreateBulk) ExecX(ctx context.Context) {
	if err := wcb.Exec(ctx); err != nil {
		panic(err)
	}
}
