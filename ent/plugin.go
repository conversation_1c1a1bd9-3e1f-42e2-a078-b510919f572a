// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"resflow/ent/plugin"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

// Plugin is the model entity for the Plugin schema.
type Plugin struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// Version holds the value of the "version" field.
	Version string `json:"version,omitempty"`
	// Author holds the value of the "author" field.
	Author string `json:"author,omitempty"`
	// DisplayName holds the value of the "display_name" field.
	DisplayName string `json:"display_name,omitempty"`
	// Description holds the value of the "description" field.
	Description string `json:"description,omitempty"`
	// Icon holds the value of the "icon" field.
	Icon string `json:"icon,omitempty"`
	// Path holds the value of the "path" field.
	Path string `json:"path,omitempty"`
	// Builtin holds the value of the "builtin" field.
	Builtin bool `json:"builtin,omitempty"`
	// Enabled holds the value of the "enabled" field.
	Enabled bool `json:"enabled,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Plugin) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case plugin.FieldBuiltin, plugin.FieldEnabled:
			values[i] = new(sql.NullBool)
		case plugin.FieldName, plugin.FieldVersion, plugin.FieldAuthor, plugin.FieldDisplayName, plugin.FieldDescription, plugin.FieldIcon, plugin.FieldPath:
			values[i] = new(sql.NullString)
		case plugin.FieldCreatedAt, plugin.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case plugin.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Plugin fields.
func (pl *Plugin) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case plugin.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				pl.ID = *value
			}
		case plugin.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				pl.Name = value.String
			}
		case plugin.FieldVersion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field version", values[i])
			} else if value.Valid {
				pl.Version = value.String
			}
		case plugin.FieldAuthor:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field author", values[i])
			} else if value.Valid {
				pl.Author = value.String
			}
		case plugin.FieldDisplayName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field display_name", values[i])
			} else if value.Valid {
				pl.DisplayName = value.String
			}
		case plugin.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				pl.Description = value.String
			}
		case plugin.FieldIcon:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field icon", values[i])
			} else if value.Valid {
				pl.Icon = value.String
			}
		case plugin.FieldPath:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field path", values[i])
			} else if value.Valid {
				pl.Path = value.String
			}
		case plugin.FieldBuiltin:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field builtin", values[i])
			} else if value.Valid {
				pl.Builtin = value.Bool
			}
		case plugin.FieldEnabled:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field enabled", values[i])
			} else if value.Valid {
				pl.Enabled = value.Bool
			}
		case plugin.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				pl.CreatedAt = value.Time
			}
		case plugin.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				pl.UpdatedAt = value.Time
			}
		default:
			pl.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Plugin.
// This includes values selected through modifiers, order, etc.
func (pl *Plugin) Value(name string) (ent.Value, error) {
	return pl.selectValues.Get(name)
}

// Update returns a builder for updating this Plugin.
// Note that you need to call Plugin.Unwrap() before calling this method if this Plugin
// was returned from a transaction, and the transaction was committed or rolled back.
func (pl *Plugin) Update() *PluginUpdateOne {
	return NewPluginClient(pl.config).UpdateOne(pl)
}

// Unwrap unwraps the Plugin entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (pl *Plugin) Unwrap() *Plugin {
	_tx, ok := pl.config.driver.(*txDriver)
	if !ok {
		panic("ent: Plugin is not a transactional entity")
	}
	pl.config.driver = _tx.drv
	return pl
}

// String implements the fmt.Stringer.
func (pl *Plugin) String() string {
	var builder strings.Builder
	builder.WriteString("Plugin(")
	builder.WriteString(fmt.Sprintf("id=%v, ", pl.ID))
	builder.WriteString("name=")
	builder.WriteString(pl.Name)
	builder.WriteString(", ")
	builder.WriteString("version=")
	builder.WriteString(pl.Version)
	builder.WriteString(", ")
	builder.WriteString("author=")
	builder.WriteString(pl.Author)
	builder.WriteString(", ")
	builder.WriteString("display_name=")
	builder.WriteString(pl.DisplayName)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(pl.Description)
	builder.WriteString(", ")
	builder.WriteString("icon=")
	builder.WriteString(pl.Icon)
	builder.WriteString(", ")
	builder.WriteString("path=")
	builder.WriteString(pl.Path)
	builder.WriteString(", ")
	builder.WriteString("builtin=")
	builder.WriteString(fmt.Sprintf("%v", pl.Builtin))
	builder.WriteString(", ")
	builder.WriteString("enabled=")
	builder.WriteString(fmt.Sprintf("%v", pl.Enabled))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(pl.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(pl.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Plugins is a parsable slice of Plugin.
type Plugins []*Plugin
