// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"
	"resflow/ent/nodedefinition"
	"resflow/ent/predicate"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// NodeDefinitionQuery is the builder for querying NodeDefinition entities.
type NodeDefinitionQuery struct {
	config
	ctx        *QueryContext
	order      []nodedefinition.OrderOption
	inters     []Interceptor
	predicates []predicate.NodeDefinition
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the NodeDefinitionQuery builder.
func (ndq *NodeDefinitionQuery) Where(ps ...predicate.NodeDefinition) *NodeDefinitionQuery {
	ndq.predicates = append(ndq.predicates, ps...)
	return ndq
}

// Limit the number of records to be returned by this query.
func (ndq *NodeDefinitionQuery) Limit(limit int) *NodeDefinitionQuery {
	ndq.ctx.Limit = &limit
	return ndq
}

// Offset to start from.
func (ndq *NodeDefinitionQuery) Offset(offset int) *NodeDefinitionQuery {
	ndq.ctx.Offset = &offset
	return ndq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (ndq *NodeDefinitionQuery) Unique(unique bool) *NodeDefinitionQuery {
	ndq.ctx.Unique = &unique
	return ndq
}

// Order specifies how the records should be ordered.
func (ndq *NodeDefinitionQuery) Order(o ...nodedefinition.OrderOption) *NodeDefinitionQuery {
	ndq.order = append(ndq.order, o...)
	return ndq
}

// First returns the first NodeDefinition entity from the query.
// Returns a *NotFoundError when no NodeDefinition was found.
func (ndq *NodeDefinitionQuery) First(ctx context.Context) (*NodeDefinition, error) {
	nodes, err := ndq.Limit(1).All(setContextOp(ctx, ndq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{nodedefinition.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (ndq *NodeDefinitionQuery) FirstX(ctx context.Context) *NodeDefinition {
	node, err := ndq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first NodeDefinition ID from the query.
// Returns a *NotFoundError when no NodeDefinition ID was found.
func (ndq *NodeDefinitionQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = ndq.Limit(1).IDs(setContextOp(ctx, ndq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{nodedefinition.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (ndq *NodeDefinitionQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := ndq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single NodeDefinition entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one NodeDefinition entity is found.
// Returns a *NotFoundError when no NodeDefinition entities are found.
func (ndq *NodeDefinitionQuery) Only(ctx context.Context) (*NodeDefinition, error) {
	nodes, err := ndq.Limit(2).All(setContextOp(ctx, ndq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{nodedefinition.Label}
	default:
		return nil, &NotSingularError{nodedefinition.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (ndq *NodeDefinitionQuery) OnlyX(ctx context.Context) *NodeDefinition {
	node, err := ndq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only NodeDefinition ID in the query.
// Returns a *NotSingularError when more than one NodeDefinition ID is found.
// Returns a *NotFoundError when no entities are found.
func (ndq *NodeDefinitionQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = ndq.Limit(2).IDs(setContextOp(ctx, ndq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{nodedefinition.Label}
	default:
		err = &NotSingularError{nodedefinition.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (ndq *NodeDefinitionQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := ndq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of NodeDefinitions.
func (ndq *NodeDefinitionQuery) All(ctx context.Context) ([]*NodeDefinition, error) {
	ctx = setContextOp(ctx, ndq.ctx, ent.OpQueryAll)
	if err := ndq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*NodeDefinition, *NodeDefinitionQuery]()
	return withInterceptors[[]*NodeDefinition](ctx, ndq, qr, ndq.inters)
}

// AllX is like All, but panics if an error occurs.
func (ndq *NodeDefinitionQuery) AllX(ctx context.Context) []*NodeDefinition {
	nodes, err := ndq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of NodeDefinition IDs.
func (ndq *NodeDefinitionQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if ndq.ctx.Unique == nil && ndq.path != nil {
		ndq.Unique(true)
	}
	ctx = setContextOp(ctx, ndq.ctx, ent.OpQueryIDs)
	if err = ndq.Select(nodedefinition.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (ndq *NodeDefinitionQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := ndq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (ndq *NodeDefinitionQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, ndq.ctx, ent.OpQueryCount)
	if err := ndq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, ndq, querierCount[*NodeDefinitionQuery](), ndq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (ndq *NodeDefinitionQuery) CountX(ctx context.Context) int {
	count, err := ndq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (ndq *NodeDefinitionQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, ndq.ctx, ent.OpQueryExist)
	switch _, err := ndq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (ndq *NodeDefinitionQuery) ExistX(ctx context.Context) bool {
	exist, err := ndq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the NodeDefinitionQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (ndq *NodeDefinitionQuery) Clone() *NodeDefinitionQuery {
	if ndq == nil {
		return nil
	}
	return &NodeDefinitionQuery{
		config:     ndq.config,
		ctx:        ndq.ctx.Clone(),
		order:      append([]nodedefinition.OrderOption{}, ndq.order...),
		inters:     append([]Interceptor{}, ndq.inters...),
		predicates: append([]predicate.NodeDefinition{}, ndq.predicates...),
		// clone intermediate query.
		sql:  ndq.sql.Clone(),
		path: ndq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		PluginName string `json:"plugin_name,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.NodeDefinition.Query().
//		GroupBy(nodedefinition.FieldPluginName).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (ndq *NodeDefinitionQuery) GroupBy(field string, fields ...string) *NodeDefinitionGroupBy {
	ndq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &NodeDefinitionGroupBy{build: ndq}
	grbuild.flds = &ndq.ctx.Fields
	grbuild.label = nodedefinition.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		PluginName string `json:"plugin_name,omitempty"`
//	}
//
//	client.NodeDefinition.Query().
//		Select(nodedefinition.FieldPluginName).
//		Scan(ctx, &v)
func (ndq *NodeDefinitionQuery) Select(fields ...string) *NodeDefinitionSelect {
	ndq.ctx.Fields = append(ndq.ctx.Fields, fields...)
	sbuild := &NodeDefinitionSelect{NodeDefinitionQuery: ndq}
	sbuild.label = nodedefinition.Label
	sbuild.flds, sbuild.scan = &ndq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a NodeDefinitionSelect configured with the given aggregations.
func (ndq *NodeDefinitionQuery) Aggregate(fns ...AggregateFunc) *NodeDefinitionSelect {
	return ndq.Select().Aggregate(fns...)
}

func (ndq *NodeDefinitionQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range ndq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, ndq); err != nil {
				return err
			}
		}
	}
	for _, f := range ndq.ctx.Fields {
		if !nodedefinition.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if ndq.path != nil {
		prev, err := ndq.path(ctx)
		if err != nil {
			return err
		}
		ndq.sql = prev
	}
	return nil
}

func (ndq *NodeDefinitionQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*NodeDefinition, error) {
	var (
		nodes = []*NodeDefinition{}
		_spec = ndq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*NodeDefinition).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &NodeDefinition{config: ndq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, ndq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (ndq *NodeDefinitionQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := ndq.querySpec()
	_spec.Node.Columns = ndq.ctx.Fields
	if len(ndq.ctx.Fields) > 0 {
		_spec.Unique = ndq.ctx.Unique != nil && *ndq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, ndq.driver, _spec)
}

func (ndq *NodeDefinitionQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(nodedefinition.Table, nodedefinition.Columns, sqlgraph.NewFieldSpec(nodedefinition.FieldID, field.TypeUUID))
	_spec.From = ndq.sql
	if unique := ndq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if ndq.path != nil {
		_spec.Unique = true
	}
	if fields := ndq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, nodedefinition.FieldID)
		for i := range fields {
			if fields[i] != nodedefinition.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := ndq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := ndq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := ndq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := ndq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (ndq *NodeDefinitionQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(ndq.driver.Dialect())
	t1 := builder.Table(nodedefinition.Table)
	columns := ndq.ctx.Fields
	if len(columns) == 0 {
		columns = nodedefinition.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if ndq.sql != nil {
		selector = ndq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if ndq.ctx.Unique != nil && *ndq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range ndq.predicates {
		p(selector)
	}
	for _, p := range ndq.order {
		p(selector)
	}
	if offset := ndq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := ndq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// NodeDefinitionGroupBy is the group-by builder for NodeDefinition entities.
type NodeDefinitionGroupBy struct {
	selector
	build *NodeDefinitionQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ndgb *NodeDefinitionGroupBy) Aggregate(fns ...AggregateFunc) *NodeDefinitionGroupBy {
	ndgb.fns = append(ndgb.fns, fns...)
	return ndgb
}

// Scan applies the selector query and scans the result into the given value.
func (ndgb *NodeDefinitionGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ndgb.build.ctx, ent.OpQueryGroupBy)
	if err := ndgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*NodeDefinitionQuery, *NodeDefinitionGroupBy](ctx, ndgb.build, ndgb, ndgb.build.inters, v)
}

func (ndgb *NodeDefinitionGroupBy) sqlScan(ctx context.Context, root *NodeDefinitionQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ndgb.fns))
	for _, fn := range ndgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ndgb.flds)+len(ndgb.fns))
		for _, f := range *ndgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ndgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ndgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// NodeDefinitionSelect is the builder for selecting fields of NodeDefinition entities.
type NodeDefinitionSelect struct {
	*NodeDefinitionQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (nds *NodeDefinitionSelect) Aggregate(fns ...AggregateFunc) *NodeDefinitionSelect {
	nds.fns = append(nds.fns, fns...)
	return nds
}

// Scan applies the selector query and scans the result into the given value.
func (nds *NodeDefinitionSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, nds.ctx, ent.OpQuerySelect)
	if err := nds.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*NodeDefinitionQuery, *NodeDefinitionSelect](ctx, nds.NodeDefinitionQuery, nds, nds.inters, v)
}

func (nds *NodeDefinitionSelect) sqlScan(ctx context.Context, root *NodeDefinitionQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(nds.fns))
	for _, fn := range nds.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*nds.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := nds.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
