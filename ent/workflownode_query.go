// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"
	"resflow/ent/predicate"
	"resflow/ent/workflow"
	"resflow/ent/workflownode"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// WorkflowNodeQuery is the builder for querying WorkflowNode entities.
type WorkflowNodeQuery struct {
	config
	ctx          *QueryContext
	order        []workflownode.OrderOption
	inters       []Interceptor
	predicates   []predicate.WorkflowNode
	withWorkflow *WorkflowQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the WorkflowNodeQuery builder.
func (wnq *WorkflowNodeQuery) Where(ps ...predicate.WorkflowNode) *WorkflowNodeQuery {
	wnq.predicates = append(wnq.predicates, ps...)
	return wnq
}

// Limit the number of records to be returned by this query.
func (wnq *WorkflowNodeQuery) Limit(limit int) *WorkflowNodeQuery {
	wnq.ctx.Limit = &limit
	return wnq
}

// Offset to start from.
func (wnq *WorkflowNodeQuery) Offset(offset int) *WorkflowNodeQuery {
	wnq.ctx.Offset = &offset
	return wnq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (wnq *WorkflowNodeQuery) Unique(unique bool) *WorkflowNodeQuery {
	wnq.ctx.Unique = &unique
	return wnq
}

// Order specifies how the records should be ordered.
func (wnq *WorkflowNodeQuery) Order(o ...workflownode.OrderOption) *WorkflowNodeQuery {
	wnq.order = append(wnq.order, o...)
	return wnq
}

// QueryWorkflow chains the current query on the "workflow" edge.
func (wnq *WorkflowNodeQuery) QueryWorkflow() *WorkflowQuery {
	query := (&WorkflowClient{config: wnq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := wnq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := wnq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(workflownode.Table, workflownode.FieldID, selector),
			sqlgraph.To(workflow.Table, workflow.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, workflownode.WorkflowTable, workflownode.WorkflowColumn),
		)
		fromU = sqlgraph.SetNeighbors(wnq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first WorkflowNode entity from the query.
// Returns a *NotFoundError when no WorkflowNode was found.
func (wnq *WorkflowNodeQuery) First(ctx context.Context) (*WorkflowNode, error) {
	nodes, err := wnq.Limit(1).All(setContextOp(ctx, wnq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{workflownode.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (wnq *WorkflowNodeQuery) FirstX(ctx context.Context) *WorkflowNode {
	node, err := wnq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first WorkflowNode ID from the query.
// Returns a *NotFoundError when no WorkflowNode ID was found.
func (wnq *WorkflowNodeQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = wnq.Limit(1).IDs(setContextOp(ctx, wnq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{workflownode.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (wnq *WorkflowNodeQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := wnq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single WorkflowNode entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one WorkflowNode entity is found.
// Returns a *NotFoundError when no WorkflowNode entities are found.
func (wnq *WorkflowNodeQuery) Only(ctx context.Context) (*WorkflowNode, error) {
	nodes, err := wnq.Limit(2).All(setContextOp(ctx, wnq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{workflownode.Label}
	default:
		return nil, &NotSingularError{workflownode.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (wnq *WorkflowNodeQuery) OnlyX(ctx context.Context) *WorkflowNode {
	node, err := wnq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only WorkflowNode ID in the query.
// Returns a *NotSingularError when more than one WorkflowNode ID is found.
// Returns a *NotFoundError when no entities are found.
func (wnq *WorkflowNodeQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = wnq.Limit(2).IDs(setContextOp(ctx, wnq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{workflownode.Label}
	default:
		err = &NotSingularError{workflownode.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (wnq *WorkflowNodeQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := wnq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of WorkflowNodes.
func (wnq *WorkflowNodeQuery) All(ctx context.Context) ([]*WorkflowNode, error) {
	ctx = setContextOp(ctx, wnq.ctx, ent.OpQueryAll)
	if err := wnq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*WorkflowNode, *WorkflowNodeQuery]()
	return withInterceptors[[]*WorkflowNode](ctx, wnq, qr, wnq.inters)
}

// AllX is like All, but panics if an error occurs.
func (wnq *WorkflowNodeQuery) AllX(ctx context.Context) []*WorkflowNode {
	nodes, err := wnq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of WorkflowNode IDs.
func (wnq *WorkflowNodeQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if wnq.ctx.Unique == nil && wnq.path != nil {
		wnq.Unique(true)
	}
	ctx = setContextOp(ctx, wnq.ctx, ent.OpQueryIDs)
	if err = wnq.Select(workflownode.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (wnq *WorkflowNodeQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := wnq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (wnq *WorkflowNodeQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, wnq.ctx, ent.OpQueryCount)
	if err := wnq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, wnq, querierCount[*WorkflowNodeQuery](), wnq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (wnq *WorkflowNodeQuery) CountX(ctx context.Context) int {
	count, err := wnq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (wnq *WorkflowNodeQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, wnq.ctx, ent.OpQueryExist)
	switch _, err := wnq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (wnq *WorkflowNodeQuery) ExistX(ctx context.Context) bool {
	exist, err := wnq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the WorkflowNodeQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (wnq *WorkflowNodeQuery) Clone() *WorkflowNodeQuery {
	if wnq == nil {
		return nil
	}
	return &WorkflowNodeQuery{
		config:       wnq.config,
		ctx:          wnq.ctx.Clone(),
		order:        append([]workflownode.OrderOption{}, wnq.order...),
		inters:       append([]Interceptor{}, wnq.inters...),
		predicates:   append([]predicate.WorkflowNode{}, wnq.predicates...),
		withWorkflow: wnq.withWorkflow.Clone(),
		// clone intermediate query.
		sql:  wnq.sql.Clone(),
		path: wnq.path,
	}
}

// WithWorkflow tells the query-builder to eager-load the nodes that are connected to
// the "workflow" edge. The optional arguments are used to configure the query builder of the edge.
func (wnq *WorkflowNodeQuery) WithWorkflow(opts ...func(*WorkflowQuery)) *WorkflowNodeQuery {
	query := (&WorkflowClient{config: wnq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	wnq.withWorkflow = query
	return wnq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		WorkflowID uuid.UUID `json:"workflow_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.WorkflowNode.Query().
//		GroupBy(workflownode.FieldWorkflowID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (wnq *WorkflowNodeQuery) GroupBy(field string, fields ...string) *WorkflowNodeGroupBy {
	wnq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &WorkflowNodeGroupBy{build: wnq}
	grbuild.flds = &wnq.ctx.Fields
	grbuild.label = workflownode.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		WorkflowID uuid.UUID `json:"workflow_id,omitempty"`
//	}
//
//	client.WorkflowNode.Query().
//		Select(workflownode.FieldWorkflowID).
//		Scan(ctx, &v)
func (wnq *WorkflowNodeQuery) Select(fields ...string) *WorkflowNodeSelect {
	wnq.ctx.Fields = append(wnq.ctx.Fields, fields...)
	sbuild := &WorkflowNodeSelect{WorkflowNodeQuery: wnq}
	sbuild.label = workflownode.Label
	sbuild.flds, sbuild.scan = &wnq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a WorkflowNodeSelect configured with the given aggregations.
func (wnq *WorkflowNodeQuery) Aggregate(fns ...AggregateFunc) *WorkflowNodeSelect {
	return wnq.Select().Aggregate(fns...)
}

func (wnq *WorkflowNodeQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range wnq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, wnq); err != nil {
				return err
			}
		}
	}
	for _, f := range wnq.ctx.Fields {
		if !workflownode.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if wnq.path != nil {
		prev, err := wnq.path(ctx)
		if err != nil {
			return err
		}
		wnq.sql = prev
	}
	return nil
}

func (wnq *WorkflowNodeQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*WorkflowNode, error) {
	var (
		nodes       = []*WorkflowNode{}
		_spec       = wnq.querySpec()
		loadedTypes = [1]bool{
			wnq.withWorkflow != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*WorkflowNode).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &WorkflowNode{config: wnq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, wnq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := wnq.withWorkflow; query != nil {
		if err := wnq.loadWorkflow(ctx, query, nodes, nil,
			func(n *WorkflowNode, e *Workflow) { n.Edges.Workflow = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (wnq *WorkflowNodeQuery) loadWorkflow(ctx context.Context, query *WorkflowQuery, nodes []*WorkflowNode, init func(*WorkflowNode), assign func(*WorkflowNode, *Workflow)) error {
	ids := make([]uuid.UUID, 0, len(nodes))
	nodeids := make(map[uuid.UUID][]*WorkflowNode)
	for i := range nodes {
		fk := nodes[i].WorkflowID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(workflow.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "workflow_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (wnq *WorkflowNodeQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := wnq.querySpec()
	_spec.Node.Columns = wnq.ctx.Fields
	if len(wnq.ctx.Fields) > 0 {
		_spec.Unique = wnq.ctx.Unique != nil && *wnq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, wnq.driver, _spec)
}

func (wnq *WorkflowNodeQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(workflownode.Table, workflownode.Columns, sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID))
	_spec.From = wnq.sql
	if unique := wnq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if wnq.path != nil {
		_spec.Unique = true
	}
	if fields := wnq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, workflownode.FieldID)
		for i := range fields {
			if fields[i] != workflownode.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if wnq.withWorkflow != nil {
			_spec.Node.AddColumnOnce(workflownode.FieldWorkflowID)
		}
	}
	if ps := wnq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := wnq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := wnq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := wnq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (wnq *WorkflowNodeQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(wnq.driver.Dialect())
	t1 := builder.Table(workflownode.Table)
	columns := wnq.ctx.Fields
	if len(columns) == 0 {
		columns = workflownode.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if wnq.sql != nil {
		selector = wnq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if wnq.ctx.Unique != nil && *wnq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range wnq.predicates {
		p(selector)
	}
	for _, p := range wnq.order {
		p(selector)
	}
	if offset := wnq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := wnq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// WorkflowNodeGroupBy is the group-by builder for WorkflowNode entities.
type WorkflowNodeGroupBy struct {
	selector
	build *WorkflowNodeQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (wngb *WorkflowNodeGroupBy) Aggregate(fns ...AggregateFunc) *WorkflowNodeGroupBy {
	wngb.fns = append(wngb.fns, fns...)
	return wngb
}

// Scan applies the selector query and scans the result into the given value.
func (wngb *WorkflowNodeGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, wngb.build.ctx, ent.OpQueryGroupBy)
	if err := wngb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WorkflowNodeQuery, *WorkflowNodeGroupBy](ctx, wngb.build, wngb, wngb.build.inters, v)
}

func (wngb *WorkflowNodeGroupBy) sqlScan(ctx context.Context, root *WorkflowNodeQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(wngb.fns))
	for _, fn := range wngb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*wngb.flds)+len(wngb.fns))
		for _, f := range *wngb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*wngb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := wngb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// WorkflowNodeSelect is the builder for selecting fields of WorkflowNode entities.
type WorkflowNodeSelect struct {
	*WorkflowNodeQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (wns *WorkflowNodeSelect) Aggregate(fns ...AggregateFunc) *WorkflowNodeSelect {
	wns.fns = append(wns.fns, fns...)
	return wns
}

// Scan applies the selector query and scans the result into the given value.
func (wns *WorkflowNodeSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, wns.ctx, ent.OpQuerySelect)
	if err := wns.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WorkflowNodeQuery, *WorkflowNodeSelect](ctx, wns.WorkflowNodeQuery, wns, wns.inters, v)
}

func (wns *WorkflowNodeSelect) sqlScan(ctx context.Context, root *WorkflowNodeQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(wns.fns))
	for _, fn := range wns.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*wns.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := wns.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
