// Code generated by ent, DO NOT EDIT.

package nodedefinition

import (
	"resflow/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLTE(FieldID, id))
}

// PluginName applies equality check predicate on the "plugin_name" field. It's identical to PluginNameEQ.
func PluginName(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldPluginName, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldName, v))
}

// Author applies equality check predicate on the "author" field. It's identical to AuthorEQ.
func Author(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldAuthor, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldDescription, v))
}

// Icon applies equality check predicate on the "icon" field. It's identical to IconEQ.
func Icon(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldIcon, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldType, v))
}

// Version applies equality check predicate on the "version" field. It's identical to VersionEQ.
func Version(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldVersion, v))
}

// Category applies equality check predicate on the "category" field. It's identical to CategoryEQ.
func Category(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldCategory, v))
}

// Exception applies equality check predicate on the "exception" field. It's identical to ExceptionEQ.
func Exception(v bool) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldException, v))
}

// Path applies equality check predicate on the "path" field. It's identical to PathEQ.
func Path(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldPath, v))
}

// Builtin applies equality check predicate on the "builtin" field. It's identical to BuiltinEQ.
func Builtin(v bool) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldBuiltin, v))
}

// Enabled applies equality check predicate on the "enabled" field. It's identical to EnabledEQ.
func Enabled(v bool) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldEnabled, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldUpdatedAt, v))
}

// PluginNameEQ applies the EQ predicate on the "plugin_name" field.
func PluginNameEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldPluginName, v))
}

// PluginNameNEQ applies the NEQ predicate on the "plugin_name" field.
func PluginNameNEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNEQ(FieldPluginName, v))
}

// PluginNameIn applies the In predicate on the "plugin_name" field.
func PluginNameIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldIn(FieldPluginName, vs...))
}

// PluginNameNotIn applies the NotIn predicate on the "plugin_name" field.
func PluginNameNotIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNotIn(FieldPluginName, vs...))
}

// PluginNameGT applies the GT predicate on the "plugin_name" field.
func PluginNameGT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGT(FieldPluginName, v))
}

// PluginNameGTE applies the GTE predicate on the "plugin_name" field.
func PluginNameGTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGTE(FieldPluginName, v))
}

// PluginNameLT applies the LT predicate on the "plugin_name" field.
func PluginNameLT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLT(FieldPluginName, v))
}

// PluginNameLTE applies the LTE predicate on the "plugin_name" field.
func PluginNameLTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLTE(FieldPluginName, v))
}

// PluginNameContains applies the Contains predicate on the "plugin_name" field.
func PluginNameContains(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContains(FieldPluginName, v))
}

// PluginNameHasPrefix applies the HasPrefix predicate on the "plugin_name" field.
func PluginNameHasPrefix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasPrefix(FieldPluginName, v))
}

// PluginNameHasSuffix applies the HasSuffix predicate on the "plugin_name" field.
func PluginNameHasSuffix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasSuffix(FieldPluginName, v))
}

// PluginNameEqualFold applies the EqualFold predicate on the "plugin_name" field.
func PluginNameEqualFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEqualFold(FieldPluginName, v))
}

// PluginNameContainsFold applies the ContainsFold predicate on the "plugin_name" field.
func PluginNameContainsFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContainsFold(FieldPluginName, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContainsFold(FieldName, v))
}

// AuthorEQ applies the EQ predicate on the "author" field.
func AuthorEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldAuthor, v))
}

// AuthorNEQ applies the NEQ predicate on the "author" field.
func AuthorNEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNEQ(FieldAuthor, v))
}

// AuthorIn applies the In predicate on the "author" field.
func AuthorIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldIn(FieldAuthor, vs...))
}

// AuthorNotIn applies the NotIn predicate on the "author" field.
func AuthorNotIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNotIn(FieldAuthor, vs...))
}

// AuthorGT applies the GT predicate on the "author" field.
func AuthorGT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGT(FieldAuthor, v))
}

// AuthorGTE applies the GTE predicate on the "author" field.
func AuthorGTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGTE(FieldAuthor, v))
}

// AuthorLT applies the LT predicate on the "author" field.
func AuthorLT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLT(FieldAuthor, v))
}

// AuthorLTE applies the LTE predicate on the "author" field.
func AuthorLTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLTE(FieldAuthor, v))
}

// AuthorContains applies the Contains predicate on the "author" field.
func AuthorContains(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContains(FieldAuthor, v))
}

// AuthorHasPrefix applies the HasPrefix predicate on the "author" field.
func AuthorHasPrefix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasPrefix(FieldAuthor, v))
}

// AuthorHasSuffix applies the HasSuffix predicate on the "author" field.
func AuthorHasSuffix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasSuffix(FieldAuthor, v))
}

// AuthorEqualFold applies the EqualFold predicate on the "author" field.
func AuthorEqualFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEqualFold(FieldAuthor, v))
}

// AuthorContainsFold applies the ContainsFold predicate on the "author" field.
func AuthorContainsFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContainsFold(FieldAuthor, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContainsFold(FieldDescription, v))
}

// IconEQ applies the EQ predicate on the "icon" field.
func IconEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldIcon, v))
}

// IconNEQ applies the NEQ predicate on the "icon" field.
func IconNEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNEQ(FieldIcon, v))
}

// IconIn applies the In predicate on the "icon" field.
func IconIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldIn(FieldIcon, vs...))
}

// IconNotIn applies the NotIn predicate on the "icon" field.
func IconNotIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNotIn(FieldIcon, vs...))
}

// IconGT applies the GT predicate on the "icon" field.
func IconGT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGT(FieldIcon, v))
}

// IconGTE applies the GTE predicate on the "icon" field.
func IconGTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGTE(FieldIcon, v))
}

// IconLT applies the LT predicate on the "icon" field.
func IconLT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLT(FieldIcon, v))
}

// IconLTE applies the LTE predicate on the "icon" field.
func IconLTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLTE(FieldIcon, v))
}

// IconContains applies the Contains predicate on the "icon" field.
func IconContains(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContains(FieldIcon, v))
}

// IconHasPrefix applies the HasPrefix predicate on the "icon" field.
func IconHasPrefix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasPrefix(FieldIcon, v))
}

// IconHasSuffix applies the HasSuffix predicate on the "icon" field.
func IconHasSuffix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasSuffix(FieldIcon, v))
}

// IconEqualFold applies the EqualFold predicate on the "icon" field.
func IconEqualFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEqualFold(FieldIcon, v))
}

// IconContainsFold applies the ContainsFold predicate on the "icon" field.
func IconContainsFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContainsFold(FieldIcon, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContainsFold(FieldType, v))
}

// VersionEQ applies the EQ predicate on the "version" field.
func VersionEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldVersion, v))
}

// VersionNEQ applies the NEQ predicate on the "version" field.
func VersionNEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNEQ(FieldVersion, v))
}

// VersionIn applies the In predicate on the "version" field.
func VersionIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldIn(FieldVersion, vs...))
}

// VersionNotIn applies the NotIn predicate on the "version" field.
func VersionNotIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNotIn(FieldVersion, vs...))
}

// VersionGT applies the GT predicate on the "version" field.
func VersionGT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGT(FieldVersion, v))
}

// VersionGTE applies the GTE predicate on the "version" field.
func VersionGTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGTE(FieldVersion, v))
}

// VersionLT applies the LT predicate on the "version" field.
func VersionLT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLT(FieldVersion, v))
}

// VersionLTE applies the LTE predicate on the "version" field.
func VersionLTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLTE(FieldVersion, v))
}

// VersionContains applies the Contains predicate on the "version" field.
func VersionContains(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContains(FieldVersion, v))
}

// VersionHasPrefix applies the HasPrefix predicate on the "version" field.
func VersionHasPrefix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasPrefix(FieldVersion, v))
}

// VersionHasSuffix applies the HasSuffix predicate on the "version" field.
func VersionHasSuffix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasSuffix(FieldVersion, v))
}

// VersionEqualFold applies the EqualFold predicate on the "version" field.
func VersionEqualFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEqualFold(FieldVersion, v))
}

// VersionContainsFold applies the ContainsFold predicate on the "version" field.
func VersionContainsFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContainsFold(FieldVersion, v))
}

// CategoryEQ applies the EQ predicate on the "category" field.
func CategoryEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldCategory, v))
}

// CategoryNEQ applies the NEQ predicate on the "category" field.
func CategoryNEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNEQ(FieldCategory, v))
}

// CategoryIn applies the In predicate on the "category" field.
func CategoryIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldIn(FieldCategory, vs...))
}

// CategoryNotIn applies the NotIn predicate on the "category" field.
func CategoryNotIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNotIn(FieldCategory, vs...))
}

// CategoryGT applies the GT predicate on the "category" field.
func CategoryGT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGT(FieldCategory, v))
}

// CategoryGTE applies the GTE predicate on the "category" field.
func CategoryGTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGTE(FieldCategory, v))
}

// CategoryLT applies the LT predicate on the "category" field.
func CategoryLT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLT(FieldCategory, v))
}

// CategoryLTE applies the LTE predicate on the "category" field.
func CategoryLTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLTE(FieldCategory, v))
}

// CategoryContains applies the Contains predicate on the "category" field.
func CategoryContains(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContains(FieldCategory, v))
}

// CategoryHasPrefix applies the HasPrefix predicate on the "category" field.
func CategoryHasPrefix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasPrefix(FieldCategory, v))
}

// CategoryHasSuffix applies the HasSuffix predicate on the "category" field.
func CategoryHasSuffix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasSuffix(FieldCategory, v))
}

// CategoryEqualFold applies the EqualFold predicate on the "category" field.
func CategoryEqualFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEqualFold(FieldCategory, v))
}

// CategoryContainsFold applies the ContainsFold predicate on the "category" field.
func CategoryContainsFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContainsFold(FieldCategory, v))
}

// ExceptionEQ applies the EQ predicate on the "exception" field.
func ExceptionEQ(v bool) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldException, v))
}

// ExceptionNEQ applies the NEQ predicate on the "exception" field.
func ExceptionNEQ(v bool) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNEQ(FieldException, v))
}

// PathEQ applies the EQ predicate on the "path" field.
func PathEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldPath, v))
}

// PathNEQ applies the NEQ predicate on the "path" field.
func PathNEQ(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNEQ(FieldPath, v))
}

// PathIn applies the In predicate on the "path" field.
func PathIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldIn(FieldPath, vs...))
}

// PathNotIn applies the NotIn predicate on the "path" field.
func PathNotIn(vs ...string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNotIn(FieldPath, vs...))
}

// PathGT applies the GT predicate on the "path" field.
func PathGT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGT(FieldPath, v))
}

// PathGTE applies the GTE predicate on the "path" field.
func PathGTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGTE(FieldPath, v))
}

// PathLT applies the LT predicate on the "path" field.
func PathLT(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLT(FieldPath, v))
}

// PathLTE applies the LTE predicate on the "path" field.
func PathLTE(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLTE(FieldPath, v))
}

// PathContains applies the Contains predicate on the "path" field.
func PathContains(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContains(FieldPath, v))
}

// PathHasPrefix applies the HasPrefix predicate on the "path" field.
func PathHasPrefix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasPrefix(FieldPath, v))
}

// PathHasSuffix applies the HasSuffix predicate on the "path" field.
func PathHasSuffix(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldHasSuffix(FieldPath, v))
}

// PathEqualFold applies the EqualFold predicate on the "path" field.
func PathEqualFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEqualFold(FieldPath, v))
}

// PathContainsFold applies the ContainsFold predicate on the "path" field.
func PathContainsFold(v string) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldContainsFold(FieldPath, v))
}

// BuiltinEQ applies the EQ predicate on the "builtin" field.
func BuiltinEQ(v bool) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldBuiltin, v))
}

// BuiltinNEQ applies the NEQ predicate on the "builtin" field.
func BuiltinNEQ(v bool) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNEQ(FieldBuiltin, v))
}

// EnabledEQ applies the EQ predicate on the "enabled" field.
func EnabledEQ(v bool) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldEnabled, v))
}

// EnabledNEQ applies the NEQ predicate on the "enabled" field.
func EnabledNEQ(v bool) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNEQ(FieldEnabled, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.FieldLTE(FieldUpdatedAt, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.NodeDefinition) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.NodeDefinition) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.NodeDefinition) predicate.NodeDefinition {
	return predicate.NodeDefinition(sql.NotPredicates(p))
}
