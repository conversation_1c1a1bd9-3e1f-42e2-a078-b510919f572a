// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"resflow/ent/nodedefinition"
	"resflow/internal/plugin/domain"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

// NodeDefinition is the model entity for the NodeDefinition schema.
type NodeDefinition struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// PluginName holds the value of the "plugin_name" field.
	PluginName string `json:"plugin_name,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// Author holds the value of the "author" field.
	Author string `json:"author,omitempty"`
	// Description holds the value of the "description" field.
	Description string `json:"description,omitempty"`
	// Icon holds the value of the "icon" field.
	Icon string `json:"icon,omitempty"`
	// Type holds the value of the "type" field.
	Type string `json:"type,omitempty"`
	// Version holds the value of the "version" field.
	Version string `json:"version,omitempty"`
	// Category holds the value of the "category" field.
	Category string `json:"category,omitempty"`
	// InputParams holds the value of the "input_params" field.
	InputParams []*domain.NodeParam `json:"input_params,omitempty"`
	// OutputParams holds the value of the "output_params" field.
	OutputParams []*domain.NodeParam `json:"output_params,omitempty"`
	// InputPorts holds the value of the "input_ports" field.
	InputPorts []*domain.NodePort `json:"input_ports,omitempty"`
	// OutputPorts holds the value of the "output_ports" field.
	OutputPorts []*domain.NodePort `json:"output_ports,omitempty"`
	// Exception holds the value of the "exception" field.
	Exception bool `json:"exception,omitempty"`
	// Path holds the value of the "path" field.
	Path string `json:"path,omitempty"`
	// Builtin holds the value of the "builtin" field.
	Builtin bool `json:"builtin,omitempty"`
	// Enabled holds the value of the "enabled" field.
	Enabled bool `json:"enabled,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*NodeDefinition) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case nodedefinition.FieldInputParams, nodedefinition.FieldOutputParams, nodedefinition.FieldInputPorts, nodedefinition.FieldOutputPorts:
			values[i] = new([]byte)
		case nodedefinition.FieldException, nodedefinition.FieldBuiltin, nodedefinition.FieldEnabled:
			values[i] = new(sql.NullBool)
		case nodedefinition.FieldPluginName, nodedefinition.FieldName, nodedefinition.FieldAuthor, nodedefinition.FieldDescription, nodedefinition.FieldIcon, nodedefinition.FieldType, nodedefinition.FieldVersion, nodedefinition.FieldCategory, nodedefinition.FieldPath:
			values[i] = new(sql.NullString)
		case nodedefinition.FieldCreatedAt, nodedefinition.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case nodedefinition.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the NodeDefinition fields.
func (nd *NodeDefinition) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case nodedefinition.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				nd.ID = *value
			}
		case nodedefinition.FieldPluginName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field plugin_name", values[i])
			} else if value.Valid {
				nd.PluginName = value.String
			}
		case nodedefinition.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				nd.Name = value.String
			}
		case nodedefinition.FieldAuthor:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field author", values[i])
			} else if value.Valid {
				nd.Author = value.String
			}
		case nodedefinition.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				nd.Description = value.String
			}
		case nodedefinition.FieldIcon:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field icon", values[i])
			} else if value.Valid {
				nd.Icon = value.String
			}
		case nodedefinition.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				nd.Type = value.String
			}
		case nodedefinition.FieldVersion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field version", values[i])
			} else if value.Valid {
				nd.Version = value.String
			}
		case nodedefinition.FieldCategory:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field category", values[i])
			} else if value.Valid {
				nd.Category = value.String
			}
		case nodedefinition.FieldInputParams:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field input_params", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &nd.InputParams); err != nil {
					return fmt.Errorf("unmarshal field input_params: %w", err)
				}
			}
		case nodedefinition.FieldOutputParams:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field output_params", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &nd.OutputParams); err != nil {
					return fmt.Errorf("unmarshal field output_params: %w", err)
				}
			}
		case nodedefinition.FieldInputPorts:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field input_ports", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &nd.InputPorts); err != nil {
					return fmt.Errorf("unmarshal field input_ports: %w", err)
				}
			}
		case nodedefinition.FieldOutputPorts:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field output_ports", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &nd.OutputPorts); err != nil {
					return fmt.Errorf("unmarshal field output_ports: %w", err)
				}
			}
		case nodedefinition.FieldException:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field exception", values[i])
			} else if value.Valid {
				nd.Exception = value.Bool
			}
		case nodedefinition.FieldPath:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field path", values[i])
			} else if value.Valid {
				nd.Path = value.String
			}
		case nodedefinition.FieldBuiltin:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field builtin", values[i])
			} else if value.Valid {
				nd.Builtin = value.Bool
			}
		case nodedefinition.FieldEnabled:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field enabled", values[i])
			} else if value.Valid {
				nd.Enabled = value.Bool
			}
		case nodedefinition.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				nd.CreatedAt = value.Time
			}
		case nodedefinition.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				nd.UpdatedAt = value.Time
			}
		default:
			nd.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the NodeDefinition.
// This includes values selected through modifiers, order, etc.
func (nd *NodeDefinition) Value(name string) (ent.Value, error) {
	return nd.selectValues.Get(name)
}

// Update returns a builder for updating this NodeDefinition.
// Note that you need to call NodeDefinition.Unwrap() before calling this method if this NodeDefinition
// was returned from a transaction, and the transaction was committed or rolled back.
func (nd *NodeDefinition) Update() *NodeDefinitionUpdateOne {
	return NewNodeDefinitionClient(nd.config).UpdateOne(nd)
}

// Unwrap unwraps the NodeDefinition entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (nd *NodeDefinition) Unwrap() *NodeDefinition {
	_tx, ok := nd.config.driver.(*txDriver)
	if !ok {
		panic("ent: NodeDefinition is not a transactional entity")
	}
	nd.config.driver = _tx.drv
	return nd
}

// String implements the fmt.Stringer.
func (nd *NodeDefinition) String() string {
	var builder strings.Builder
	builder.WriteString("NodeDefinition(")
	builder.WriteString(fmt.Sprintf("id=%v, ", nd.ID))
	builder.WriteString("plugin_name=")
	builder.WriteString(nd.PluginName)
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(nd.Name)
	builder.WriteString(", ")
	builder.WriteString("author=")
	builder.WriteString(nd.Author)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(nd.Description)
	builder.WriteString(", ")
	builder.WriteString("icon=")
	builder.WriteString(nd.Icon)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(nd.Type)
	builder.WriteString(", ")
	builder.WriteString("version=")
	builder.WriteString(nd.Version)
	builder.WriteString(", ")
	builder.WriteString("category=")
	builder.WriteString(nd.Category)
	builder.WriteString(", ")
	builder.WriteString("input_params=")
	builder.WriteString(fmt.Sprintf("%v", nd.InputParams))
	builder.WriteString(", ")
	builder.WriteString("output_params=")
	builder.WriteString(fmt.Sprintf("%v", nd.OutputParams))
	builder.WriteString(", ")
	builder.WriteString("input_ports=")
	builder.WriteString(fmt.Sprintf("%v", nd.InputPorts))
	builder.WriteString(", ")
	builder.WriteString("output_ports=")
	builder.WriteString(fmt.Sprintf("%v", nd.OutputPorts))
	builder.WriteString(", ")
	builder.WriteString("exception=")
	builder.WriteString(fmt.Sprintf("%v", nd.Exception))
	builder.WriteString(", ")
	builder.WriteString("path=")
	builder.WriteString(nd.Path)
	builder.WriteString(", ")
	builder.WriteString("builtin=")
	builder.WriteString(fmt.Sprintf("%v", nd.Builtin))
	builder.WriteString(", ")
	builder.WriteString("enabled=")
	builder.WriteString(fmt.Sprintf("%v", nd.Enabled))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(nd.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(nd.UpdatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// NodeDefinitions is a parsable slice of NodeDefinition.
type NodeDefinitions []*NodeDefinition
