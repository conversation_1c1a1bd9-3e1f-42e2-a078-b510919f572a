// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"resflow/ent/predicate"
	"resflow/ent/workflowsedge"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WorkflowsEdgeDelete is the builder for deleting a WorkflowsEdge entity.
type WorkflowsEdgeDelete struct {
	config
	hooks    []Hook
	mutation *WorkflowsEdgeMutation
}

// Where appends a list predicates to the WorkflowsEdgeDelete builder.
func (wed *WorkflowsEdgeDelete) Where(ps ...predicate.WorkflowsEdge) *WorkflowsEdgeDelete {
	wed.mutation.Where(ps...)
	return wed
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (wed *WorkflowsEdgeDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, wed.sqlExec, wed.mutation, wed.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (wed *WorkflowsEdgeDelete) ExecX(ctx context.Context) int {
	n, err := wed.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (wed *WorkflowsEdgeDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(workflowsedge.Table, sqlgraph.NewFieldSpec(workflowsedge.FieldID, field.TypeUUID))
	if ps := wed.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, wed.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	wed.mutation.done = true
	return affected, err
}

// WorkflowsEdgeDeleteOne is the builder for deleting a single WorkflowsEdge entity.
type WorkflowsEdgeDeleteOne struct {
	wed *WorkflowsEdgeDelete
}

// Where appends a list predicates to the WorkflowsEdgeDelete builder.
func (wedo *WorkflowsEdgeDeleteOne) Where(ps ...predicate.WorkflowsEdge) *WorkflowsEdgeDeleteOne {
	wedo.wed.mutation.Where(ps...)
	return wedo
}

// Exec executes the deletion query.
func (wedo *WorkflowsEdgeDeleteOne) Exec(ctx context.Context) error {
	n, err := wedo.wed.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{workflowsedge.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (wedo *WorkflowsEdgeDeleteOne) ExecX(ctx context.Context) {
	if err := wedo.Exec(ctx); err != nil {
		panic(err)
	}
}
