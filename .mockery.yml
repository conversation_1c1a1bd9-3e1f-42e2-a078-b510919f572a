all: false
dir: '{{.InterfaceDir}}'
filename: "mocks_moq.go"
force-file-write: true
formatter: goimports
include-auto-generated: false
log-level: info
structname: "Moq{{.InterfaceName}}"
pkgname: '{{.SrcPackageName}}'
recursive: false
require-template-schema-exists: true
template: matryer
template-schema: '{{.Template}}.schema.json'
packages:
  resflow/internal/plugin/domain:
    interfaces:
      PluginRepository: { }
      NodeDefinitionRepository: { }
      ManifestLoader: { }
  resflow/internal/user/domain:
    interfaces:
      UserRepository: { }
      PasswordEncoder: { }
  resflow/utils:
    interfaces:
      Manifest: { }
