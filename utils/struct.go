package utils

import (
	"encoding/json"
	"reflect"
	_type "resflow/type"
)

func CloneStruct(src interface{}) interface{} {
	// 获取源结构体的反射值
	srcVal := reflect.ValueOf(src)
	if srcVal.Kind() != reflect.Struct {
		return nil
	}

	// 获取源结构体的类型
	srcType := srcVal.Type()

	// 创建一个新的结构体实例
	newStruct := reflect.New(srcType).Elem()

	return newStruct.Interface()
}

func ConvertJsonToStruct(jsonStr _type.JSON, t reflect.Type) (interface{}, error) {
	// 创建结构体实例
	configInstance := reflect.New(t).Interface()

	// 解码JSON字符串到结构体实例
	err := json.Unmarshal([]byte(jsonStr), configInstance)
	if err != nil {
		return nil, err
	}

	return configInstance, nil
}

func ConvertJSONMapToStruct(data _type.JSONMap, t reflect.Type) (interface{}, error) {
	// 创建结构体实例
	configInstance := reflect.New(t).Interface()

	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(jsonData, configInstance)
	if err != nil {
		return nil, err
	}

	return configInstance, nil
}
