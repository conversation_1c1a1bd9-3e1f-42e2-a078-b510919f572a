package utils

import (
	"encoding/json"
	"google.golang.org/protobuf/types/known/structpb"
)

func Convert2PbStruct(raw json.RawMessage) (*structpb.Struct, error) {
	var value map[string]interface{}
	if err := json.Unmarshal(raw, &value); err != nil {
		return nil, err
	}
	return structpb.NewStruct(value)
}

func Convert2PbStructWithDefault(raw json.RawMessage, defaultValue *structpb.Struct) *structpb.Struct {
	value, err := Convert2PbStruct(raw)
	if err != nil {
		return defaultValue
	}
	return value
}

func Convert2JSON(s *structpb.Struct) (json.RawMessage, error) {
	jsonData, err := json.Marshal(s.AsMap())
	if err != nil {
		return nil, err
	}
	return jsonData, nil
}

func Convert2JSONWithDefault(s *structpb.Struct, defaultValue json.RawMessage) json.RawMessage {
	jsonData, err := Convert2JSON(s)
	if err != nil {
		return defaultValue
	}
	return jsonData
}
