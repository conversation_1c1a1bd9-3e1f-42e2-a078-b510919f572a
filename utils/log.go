package utils

import (
	"fmt"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"os"
	"resflow/configs"
	"time"
)

var Logger *zap.SugaredLogger

func init() {
	fileCore := initFileLog()
	consoleCore := initConsoleLog()
	combinedCore := zapcore.NewTee(fileCore, consoleCore)
	Logger = zap.New(combinedCore).Sugar()

	Logger.Debug("logger init success")
}

func getLogLevel() zapcore.Level {
	if configs.ParsedConfig.Debug {
		return zapcore.DebugLevel
	}
	return zapcore.InfoLevel
}

func getEncoderConfig() zapcore.EncoderConfig {
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = "timestamp"
	encoderConfig.EncodeTime = zapcore.TimeEncoderOfLayout("2006-01-02 15:04:05")
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
	encoderConfig.LevelKey = "level"
	encoderConfig.MessageKey = "message"
	return encoderConfig
}

func initConsoleLog() zapcore.Core {
	consoleWriter := zapcore.AddSync(os.Stdout)
	consoleEncoder := zapcore.NewConsoleEncoder(getEncoderConfig())
	return zapcore.NewCore(consoleEncoder, consoleWriter, getLogLevel())
}

func initFileLog() zapcore.Core {
	log := &lumberjack.Logger{
		Filename: fmt.Sprintf(
			"./logs/app-%s.log",
			time.Now().Format("2006-01-02"),
		), // 日志文件的位置
		MaxSize:    5,     // 文件最大尺寸（以MB为单位）
		MaxBackups: 30,    // 保留的最大旧文件数量
		MaxAge:     30,    // 保留旧文件的最大天数
		Compress:   false, // 是否压缩/归档旧文件
		LocalTime:  true,  // 使用本地时间创建时间戳
	}

	writer := zapcore.AddSync(log)
	return zapcore.NewCore(zapcore.NewJSONEncoder(getEncoderConfig()), writer, getLogLevel())
}
