// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: matryer

package utils

import (
	"sync"
)

// Ensure that MoqManifest does implement Manifest.
// If this is not the case, regenerate this file with mockery.
var _ Manifest[any] = &MoqManifest[any]{}

// MoqManifest is a mock implementation of Manifest.
//
//	func TestSomethingThatUsesManifest(t *testing.T) {
//
//		// make and configure a mocked Manifest
//		mockedManifest := &MoqManifest{
//			LoadFunc: func(path string) (*T, error) {
//				panic("mock out the Load method")
//			},
//		}
//
//		// use mockedManifest in code that requires Manifest
//		// and then make assertions.
//
//	}
type MoqManifest[T any] struct {
	// LoadFunc mocks the Load method.
	LoadFunc func(path string) (*T, error)

	// calls tracks calls to the methods.
	calls struct {
		// Load holds details about calls to the Load method.
		Load []struct {
			// Path is the path argument value.
			Path string
		}
	}
	lockLoad sync.RWMutex
}

// Load calls LoadFunc.
func (mock *MoqManifest[T]) Load(path string) (*T, error) {
	if mock.LoadFunc == nil {
		panic("MoqManifest.LoadFunc: method is nil but Manifest.Load was just called")
	}
	callInfo := struct {
		Path string
	}{
		Path: path,
	}
	mock.lockLoad.Lock()
	mock.calls.Load = append(mock.calls.Load, callInfo)
	mock.lockLoad.Unlock()
	return mock.LoadFunc(path)
}

// LoadCalls gets all the calls that were made to Load.
// Check the length with:
//
//	len(mockedManifest.LoadCalls())
func (mock *MoqManifest[T]) LoadCalls() []struct {
	Path string
} {
	var calls []struct {
		Path string
	}
	mock.lockLoad.RLock()
	calls = mock.calls.Load
	mock.lockLoad.RUnlock()
	return calls
}
