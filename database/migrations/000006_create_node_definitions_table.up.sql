CREATE TABLE IF NOT EXISTS node_definitions
(
    id             TEXT PRIMARY KEY,
    plugin_name    TEXT    NOT NULL,
    name           TEXT    NOT NULL,
    author         TEXT,
    description    TEXT,
    icon           TEXT,
    type           TEXT    NOT NULL,
    version        TEXT    NOT NULL,
    category       TEXT,
    input_params   BLOB,
    output_params  BLOB,
    input_ports    BLOB,
    output_ports   BLOB,
    exception      BOOLEAN NOT NULL,
    path           TEXT    NOT NULL,
    builtin        BOOLEAN NOT NULL,
    enabled        BOOLEAN NOT NULL,
    created_at     TIMESTAMP,
    updated_at     TIMESTAMP,
    UNIQUE (type, version, plugin_name)
)
