package query

import (
	"resflow/internal/plugin/domain"

	"golang.org/x/net/context"
)

type PluginQueryService struct {
	repository domain.PluginRepository
}

func NewPluginQueryService(repository domain.PluginRepository) *PluginQueryService {
	return &PluginQueryService{
		repository: repository,
	}
}

func (pqs *PluginQueryService) List(ctx context.Context) ([]*PluginDTO, error) {
	plugins, err := pqs.repository.List(ctx)
	if err != nil {
		return nil, err
	}
	return modelsToDTOs(plugins), nil
}
