package query

import "resflow/internal/plugin/domain"

type PluginD<PERSON> struct {
	ID          string
	Name        string
	Version     string
	Author      string
	DisplayName string
	Description string
	Icon        string
	Builtin     bool
	CreatedAt   string
	UpdatedAt   string
}

func modelToDTO(plugin *domain.Plugin) *PluginDTO {
	return &PluginDTO{
		ID:          plugin.ID().String(),
		Name:        plugin.Name(),
		Version:     plugin.Version().String(),
		Author:      plugin.Author(),
		DisplayName: plugin.DisplayName(),
		Description: plugin.Description(),
		Icon:        plugin.Icon(),
		Builtin:     plugin.Builtin(),
		CreatedAt:   plugin.CreatedAt().Format("2006-01-02 15:04:05"),
		UpdatedAt:   plugin.UpdatedAt().Format("2006-01-02 15:04:05"),
	}
}

func modelsToDTOs(plugins []*domain.Plugin) []*PluginDTO {
	pluginDTOs := make([]*PluginDTO, len(plugins))
	for i, plugin := range plugins {
		pluginDTOs[i] = modelToDTO(plugin)
	}
	return pluginDTOs
}
