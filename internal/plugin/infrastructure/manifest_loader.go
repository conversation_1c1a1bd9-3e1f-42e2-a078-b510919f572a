package infrastructure

import (
	"path/filepath"
	"resflow/internal/plugin/domain"
	"resflow/utils"
)

var pluginManifestFileName = "manifest.yaml"
var nodeManifestFileName = "node.yaml"

type manifestLoader struct {
}

func NewManifestLoader() domain.ManifestLoader {
	return &manifestLoader{}
}

func (ml manifestLoader) LoadPlugin(path string) (*domain.PluginManifest, error) {
	manifest := utils.NewManifest[domain.PluginManifest]()

	return manifest.Load(filepath.Join(path, pluginManifestFileName))
}

func (ml manifestLoader) LoadNode(path string) (*domain.NodeDefinitionManifest, error) {
	manifest := utils.NewManifest[domain.NodeDefinitionManifest]()
	return manifest.Load(filepath.Join(path, nodeManifestFileName))
}
