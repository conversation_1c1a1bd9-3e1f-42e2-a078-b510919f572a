package application

import (
	"context"
	"errors"
	commondomain "resflow/internal/common/domain"
	"resflow/internal/plugin/domain"
	"resflow/utils"
)

var (
	ErrPluginAlreadyInstalled = errors.New("plugin already installed")
)

type PluginCommandService struct {
	scanner            *domain.PluginScanner
	repository         domain.PluginRepository
	manager            *domain.PluginManager
	nodeInstaller      *domain.NodeDefinitionManager
	transactionManager commondomain.TransactionManager
}

func NewPluginInstaller(scanner *domain.PluginScanner, repository domain.PluginRepository, manager *domain.PluginManager, nodeInstaller *domain.NodeDefinitionManager, transactionManager commondomain.TransactionManager) *PluginCommandService {
	return &PluginCommandService{
		scanner:            scanner,
		repository:         repository,
		manager:            manager,
		nodeInstaller:      nodeInstaller,
		transactionManager: transactionManager,
	}
}

func (pcs *PluginCommandService) InstallPluginFromDirectory(ctx context.Context, pluginPath string, builtin bool) error {
	plugin, err := pcs.scanner.ScanPluginDirectory(pluginPath, builtin)
	if err != nil {
		utils.Logger.Debugf("解析插件清单失败：%s", err.Error())
		return err
	}

	err = pcs.transactionManager.WithTx(ctx, func(ctx context.Context) error {
		err := pcs.manager.Install(ctx, plugin)
		if err != nil {
			return err
		}

		_, err = pcs.nodeInstaller.Install(ctx, plugin, plugin.NodeDefinitions())
		if err != nil {
			utils.Logger.Debugf("安装节点失败：%s", err.Error())
			return err
		}

		return pcs.repository.Save(ctx, plugin)
	})

	if err != nil {
		utils.Logger.Debugf("安装插件失败：%s", err.Error())
		return err
	}

	return nil
}
