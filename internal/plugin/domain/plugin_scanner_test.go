package domain

import (
	"errors"
	"os"
	"path/filepath"
	"resflow/internal/common/domain"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestPluginScanner_ScanPluginDirectory_Success(t *testing.T) {
	givePluginPath := t.TempDir()
	giveNodePath := filepath.Join(givePluginPath, "nodes", "test-node")
	err := os.Mkdir<PERSON>ll(giveNodePath, 0755)
	assert.NoError(t, err)

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*PluginManifest, error) {
			return &PluginManifest{
				Name:        "test-plugin",
				Version:     "1.0.0",
				Author:      "test-author",
				DisplayName: "Test Plugin",
				Description: "A test plugin",
				Icon:        "test-icon.png",
			}, nil
		},
		LoadNodeFunc: func(path string) (*NodeDefinitionManifest, error) {
			return &NodeDefinitionManifest{
				Name:         "test-node",
				Author:       "test-author",
				Version:      "1.0.0",
				Description:  "A test node",
				Icon:         "test-icon.png",
				Type:         "test-type",
				Category:     "test-category",
				Exception:    false,
				InputParams:  []*NodeParam{},
				OutputParams: []*NodeParam{},
				InputPorts:   []*NodePort{},
				OutputPorts:  []*NodePort{},
			}, nil
		},
	}

	wantPlugin := &Plugin{
		name: "test-plugin",
		version: &domain.Version{
			Major: 1,
			Minor: 0,
			Patch: 0,
		},
		author:      "test-author",
		displayName: "Test Plugin",
		description: "A test plugin",
		icon:        "test-icon.png",
		path:        givePluginPath,
		builtin:     false,
		enabled:     true,
		nodeDefinitions: []*NodeDefinition{
			{
				pluginName:   "test-plugin",
				name:         "test-node",
				author:       "test-author",
				description:  "A test node",
				icon:         "test-icon.png",
				_type:        "test-type",
				version:      &domain.Version{Major: 1, Minor: 0, Patch: 0},
				category:     "test-category",
				inputParams:  []*NodeParam{},
				outputParams: []*NodeParam{},
				inputPorts:   []*NodePort{},
				outputPorts:  []*NodePort{},
				exception:    false,
				path:         giveNodePath,
				builtin:      false,
				enabled:      true,
			},
		},
	}

	scanner := NewPluginScanner(manifestLoader)
	gotPlugin, err := scanner.ScanPluginDirectory(givePluginPath, false)
	assert.NoError(t, err)
	assert.Equal(t, wantPlugin.Name(), gotPlugin.Name())
	assert.Equal(t, wantPlugin.Version().String(), gotPlugin.Version().String())
	assert.Equal(t, wantPlugin.Author(), gotPlugin.Author())
	assert.Equal(t, wantPlugin.DisplayName(), gotPlugin.DisplayName())
	assert.Equal(t, wantPlugin.Description(), gotPlugin.Description())
	assert.Equal(t, wantPlugin.Icon(), gotPlugin.Icon())
	assert.Equal(t, wantPlugin.Path(), gotPlugin.Path())
	assert.Equal(t, wantPlugin.Builtin(), gotPlugin.Builtin())
	assert.Equal(t, wantPlugin.Enabled(), gotPlugin.Enabled())
	assert.Equal(t, len(wantPlugin.NodeDefinitions()), len(gotPlugin.NodeDefinitions()))

	wantNodeDefinitions := wantPlugin.NodeDefinitions()
	gotNodeDefinitions := gotPlugin.NodeDefinitions()
	assert.Equal(t, wantNodeDefinitions[0].Name(), gotNodeDefinitions[0].Name())
	assert.Equal(t, wantNodeDefinitions[0].PluginName(), gotNodeDefinitions[0].PluginName())
	assert.Equal(t, wantNodeDefinitions[0].Author(), gotNodeDefinitions[0].Author())
	assert.Equal(t, wantNodeDefinitions[0].Description(), gotNodeDefinitions[0].Description())
	assert.Equal(t, wantNodeDefinitions[0].Icon(), gotNodeDefinitions[0].Icon())
	assert.Equal(t, wantNodeDefinitions[0].Type(), gotNodeDefinitions[0].Type())
	assert.Equal(t, wantNodeDefinitions[0].Version().String(), gotNodeDefinitions[0].Version().String())
	assert.Equal(t, wantNodeDefinitions[0].Category(), gotNodeDefinitions[0].Category())
	assert.Equal(t, wantNodeDefinitions[0].InputParams(), gotNodeDefinitions[0].InputParams())
	assert.Equal(t, wantNodeDefinitions[0].OutputParams(), gotNodeDefinitions[0].OutputParams())
	assert.Equal(t, wantNodeDefinitions[0].InputPorts(), gotNodeDefinitions[0].InputPorts())
	assert.Equal(t, wantNodeDefinitions[0].OutputPorts(), gotNodeDefinitions[0].OutputPorts())
	assert.Equal(t, wantNodeDefinitions[0].Exception(), gotNodeDefinitions[0].Exception())
	assert.Equal(t, wantNodeDefinitions[0].Path(), gotNodeDefinitions[0].Path())
	assert.Equal(t, wantNodeDefinitions[0].Builtin(), gotNodeDefinitions[0].Builtin())
	assert.Equal(t, wantNodeDefinitions[0].Enabled(), gotNodeDefinitions[0].Enabled())
}

func TestPluginScanner_ScanPluginDirectory_LoadPluginManifestError(t *testing.T) {
	givePluginPath := t.TempDir()

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*PluginManifest, error) {
			return nil, errors.New("failed to load plugin manifest")
		},
	}

	scanner := NewPluginScanner(manifestLoader)
	gotPlugin, err := scanner.ScanPluginDirectory(givePluginPath, false)

	assert.Error(t, err)
	assert.Equal(t, ErrLoadPluginManifest, err)
	assert.Nil(t, gotPlugin)
}

func TestPluginScanner_ScanPluginDirectory_InvalidPluginManifest(t *testing.T) {
	givePluginPath := t.TempDir()

	tests := []struct {
		name     string
		manifest *PluginManifest
		wantErr  error
	}{
		{
			name: "插件名称为空",
			manifest: &PluginManifest{
				Name:    "",
				Version: "1.0.0",
			},
			wantErr: ErrPluginNameAndVersionRequired,
		},
		{
			name: "插件版本为空",
			manifest: &PluginManifest{
				Name:    "test-plugin",
				Version: "",
			},
			wantErr: ErrPluginNameAndVersionRequired,
		},
		{
			name: "插件版本格式错误",
			manifest: &PluginManifest{
				Name:    "test-plugin",
				Version: "invalid-version",
			},
			wantErr: ErrInvalidPluginVersionFormat,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			manifestLoader := &MoqManifestLoader{
				LoadPluginFunc: func(path string) (*PluginManifest, error) {
					return tt.manifest, nil
				},
			}

			scanner := NewPluginScanner(manifestLoader)
			gotPlugin, err := scanner.ScanPluginDirectory(givePluginPath, false)

			assert.Error(t, err)
			assert.Equal(t, tt.wantErr, err)
			assert.Nil(t, gotPlugin)
		})
	}
}

func TestPluginScanner_ScanPluginDirectory_NoNodesDirectory(t *testing.T) {
	givePluginPath := t.TempDir()
	// 不创建 nodes 目录

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*PluginManifest, error) {
			return &PluginManifest{
				Name:        "test-plugin",
				Version:     "1.0.0",
				Author:      "test-author",
				DisplayName: "Test Plugin",
				Description: "A test plugin",
				Icon:        "test-icon.png",
			}, nil
		},
	}

	scanner := NewPluginScanner(manifestLoader)
	gotPlugin, err := scanner.ScanPluginDirectory(givePluginPath, false)

	assert.Error(t, err)
	assert.Equal(t, ErrReadNodesDir, err)
	assert.Nil(t, gotPlugin)
}

func TestPluginScanner_ScanPluginDirectory_EmptyNodesDirectory(t *testing.T) {
	givePluginPath := t.TempDir()
	giveNodesPath := filepath.Join(givePluginPath, "nodes")
	err := os.MkdirAll(giveNodesPath, 0755)
	require.NoError(t, err)
	// nodes 目录为空

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*PluginManifest, error) {
			return &PluginManifest{
				Name:        "test-plugin",
				Version:     "1.0.0",
				Author:      "test-author",
				DisplayName: "Test Plugin",
				Description: "A test plugin",
				Icon:        "test-icon.png",
			}, nil
		},
	}

	scanner := NewPluginScanner(manifestLoader)
	gotPlugin, err := scanner.ScanPluginDirectory(givePluginPath, false)

	assert.NoError(t, err)
	assert.NotNil(t, gotPlugin)
	assert.Equal(t, "test-plugin", gotPlugin.Name())
	assert.Empty(t, gotPlugin.NodeDefinitions()) // 没有节点定义
}

func TestPluginScanner_ScanPluginDirectory_LoadNodeManifestError(t *testing.T) {
	givePluginPath := t.TempDir()
	giveNodePath := filepath.Join(givePluginPath, "nodes", "test-node")
	err := os.MkdirAll(giveNodePath, 0755)
	require.NoError(t, err)

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*PluginManifest, error) {
			return &PluginManifest{
				Name:        "test-plugin",
				Version:     "1.0.0",
				Author:      "test-author",
				DisplayName: "Test Plugin",
				Description: "A test plugin",
				Icon:        "test-icon.png",
			}, nil
		},
		LoadNodeFunc: func(path string) (*NodeDefinitionManifest, error) {
			return nil, errors.New("failed to load node manifest")
		},
	}

	scanner := NewPluginScanner(manifestLoader)
	gotPlugin, err := scanner.ScanPluginDirectory(givePluginPath, false)

	assert.Error(t, err)
	assert.Equal(t, ErrLoadNodeDefinitionManifest, err)
	assert.Nil(t, gotPlugin)
}

func TestPluginScanner_ScanPluginDirectory_InvalidNodeManifest(t *testing.T) {
	givePluginPath := t.TempDir()
	giveNodePath := filepath.Join(givePluginPath, "nodes", "test-node")
	err := os.MkdirAll(giveNodePath, 0755)
	require.NoError(t, err)

	tests := []struct {
		name         string
		nodeManifest *NodeDefinitionManifest
		wantErr      error
	}{
		{
			name: "节点类型为空",
			nodeManifest: &NodeDefinitionManifest{
				Name:    "test-node",
				Version: "1.0.0",
				Type:    "", // 空类型
			},
			wantErr: ErrNodeInfoMissing,
		},
		{
			name: "节点版本为空",
			nodeManifest: &NodeDefinitionManifest{
				Name:    "test-node",
				Type:    "processor",
				Version: "", // 空版本
			},
			wantErr: ErrNodeInfoMissing,
		},
		{
			name: "节点版本格式错误",
			nodeManifest: &NodeDefinitionManifest{
				Name:    "test-node",
				Type:    "processor",
				Version: "invalid-version", // 无效版本格式
			},
			wantErr: ErrInvalidNodeDefinitionVersionFormat,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			manifestLoader := &MoqManifestLoader{
				LoadPluginFunc: func(path string) (*PluginManifest, error) {
					return &PluginManifest{
						Name:        "test-plugin",
						Version:     "1.0.0",
						Author:      "test-author",
						DisplayName: "Test Plugin",
						Description: "A test plugin",
						Icon:        "test-icon.png",
					}, nil
				},
				LoadNodeFunc: func(path string) (*NodeDefinitionManifest, error) {
					return tt.nodeManifest, nil
				},
			}

			scanner := NewPluginScanner(manifestLoader)
			gotPlugin, err := scanner.ScanPluginDirectory(givePluginPath, false)

			assert.Error(t, err)
			assert.Equal(t, tt.wantErr, err)
			assert.Nil(t, gotPlugin)
		})
	}
}

func TestPluginScanner_ScanPluginDirectory_MultipleNodes(t *testing.T) {
	givePluginPath := t.TempDir()
	giveNode1Path := filepath.Join(givePluginPath, "nodes", "node1")
	giveNode2Path := filepath.Join(givePluginPath, "nodes", "node2")
	err := os.MkdirAll(giveNode1Path, 0755)
	require.NoError(t, err)
	err = os.MkdirAll(giveNode2Path, 0755)
	require.NoError(t, err)

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*PluginManifest, error) {
			return &PluginManifest{
				Name:        "test-plugin",
				Version:     "1.0.0",
				Author:      "test-author",
				DisplayName: "Test Plugin",
				Description: "A test plugin",
				Icon:        "test-icon.png",
			}, nil
		},
		LoadNodeFunc: func(path string) (*NodeDefinitionManifest, error) {
			if filepath.Base(path) == "node1" {
				return &NodeDefinitionManifest{
					Name:         "node1",
					Author:       "test-author",
					Version:      "1.0.0",
					Description:  "First test node",
					Icon:         "node1-icon.png",
					Type:         "processor",
					Category:     "data",
					Exception:    false,
					InputParams:  []*NodeParam{},
					OutputParams: []*NodeParam{},
					InputPorts:   []*NodePort{},
					OutputPorts:  []*NodePort{},
				}, nil
			}
			return &NodeDefinitionManifest{
				Name:         "node2",
				Author:       "test-author",
				Version:      "1.0.0",
				Description:  "Second test node",
				Icon:         "node2-icon.png",
				Type:         "transformer",
				Category:     "processing",
				Exception:    true,
				InputParams:  []*NodeParam{},
				OutputParams: []*NodeParam{},
				InputPorts:   []*NodePort{},
				OutputPorts:  []*NodePort{},
			}, nil
		},
	}

	scanner := NewPluginScanner(manifestLoader)
	gotPlugin, err := scanner.ScanPluginDirectory(givePluginPath, false)

	assert.NoError(t, err)
	assert.NotNil(t, gotPlugin)
	assert.Equal(t, "test-plugin", gotPlugin.Name())
	assert.Len(t, gotPlugin.NodeDefinitions(), 2)

	nodeDefinitions := gotPlugin.NodeDefinitions()
	// 验证第一个节点
	node1 := findNodeByName(nodeDefinitions, "node1")
	assert.NotNil(t, node1)
	assert.Equal(t, "processor", node1.Type())
	assert.Equal(t, "data", node1.Category())
	assert.False(t, node1.Exception())

	// 验证第二个节点
	node2 := findNodeByName(nodeDefinitions, "node2")
	assert.NotNil(t, node2)
	assert.Equal(t, "transformer", node2.Type())
	assert.Equal(t, "processing", node2.Category())
	assert.True(t, node2.Exception())
}

func TestPluginScanner_ScanPluginDirectory_BuiltinPlugin(t *testing.T) {
	givePluginPath := t.TempDir()
	giveNodePath := filepath.Join(givePluginPath, "nodes", "test-node")
	err := os.MkdirAll(giveNodePath, 0755)
	require.NoError(t, err)

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*PluginManifest, error) {
			return &PluginManifest{
				Name:        "builtin-plugin",
				Version:     "1.0.0",
				Author:      "system",
				DisplayName: "Builtin Plugin",
				Description: "A builtin plugin",
				Icon:        "builtin-icon.png",
			}, nil
		},
		LoadNodeFunc: func(path string) (*NodeDefinitionManifest, error) {
			return &NodeDefinitionManifest{
				Name:         "builtin-node",
				Author:       "system",
				Version:      "1.0.0",
				Description:  "A builtin node",
				Icon:         "builtin-node-icon.png",
				Type:         "builtin-type",
				Category:     "system",
				Exception:    false,
				InputParams:  []*NodeParam{},
				OutputParams: []*NodeParam{},
				InputPorts:   []*NodePort{},
				OutputPorts:  []*NodePort{},
			}, nil
		},
	}

	scanner := NewPluginScanner(manifestLoader)
	gotPlugin, err := scanner.ScanPluginDirectory(givePluginPath, true) // builtin = true

	assert.NoError(t, err)
	assert.NotNil(t, gotPlugin)
	assert.Equal(t, "builtin-plugin", gotPlugin.Name())
	assert.True(t, gotPlugin.Builtin()) // 验证是内置插件
	assert.True(t, gotPlugin.Enabled())

	nodeDefinitions := gotPlugin.NodeDefinitions()
	assert.Len(t, nodeDefinitions, 1)
	assert.True(t, nodeDefinitions[0].Builtin()) // 验证节点也是内置的
}

func TestPluginScanner_ScanPluginDirectory_NodesWithFiles(t *testing.T) {
	givePluginPath := t.TempDir()
	giveNodesPath := filepath.Join(givePluginPath, "nodes")
	err := os.MkdirAll(giveNodesPath, 0755)
	require.NoError(t, err)

	// 创建一个文件（不是目录）
	giveFilePath := filepath.Join(giveNodesPath, "not-a-directory.txt")
	err = os.WriteFile(giveFilePath, []byte("test content"), 0644)
	require.NoError(t, err)

	// 创建一个正常的节点目录
	giveNodePath := filepath.Join(giveNodesPath, "test-node")
	err = os.MkdirAll(giveNodePath, 0755)
	require.NoError(t, err)

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*PluginManifest, error) {
			return &PluginManifest{
				Name:        "test-plugin",
				Version:     "1.0.0",
				Author:      "test-author",
				DisplayName: "Test Plugin",
				Description: "A test plugin",
				Icon:        "test-icon.png",
			}, nil
		},
		LoadNodeFunc: func(path string) (*NodeDefinitionManifest, error) {
			return &NodeDefinitionManifest{
				Name:         "test-node",
				Author:       "test-author",
				Version:      "1.0.0",
				Description:  "A test node",
				Icon:         "test-icon.png",
				Type:         "test-type",
				Category:     "test-category",
				Exception:    false,
				InputParams:  []*NodeParam{},
				OutputParams: []*NodeParam{},
				InputPorts:   []*NodePort{},
				OutputPorts:  []*NodePort{},
			}, nil
		},
	}

	scanner := NewPluginScanner(manifestLoader)
	gotPlugin, err := scanner.ScanPluginDirectory(givePluginPath, false)

	assert.NoError(t, err)
	assert.NotNil(t, gotPlugin)
	assert.Equal(t, "test-plugin", gotPlugin.Name())
	// 应该只有一个节点定义（文件被忽略）
	assert.Len(t, gotPlugin.NodeDefinitions(), 1)
	assert.Equal(t, "test-node", gotPlugin.NodeDefinitions()[0].Name())
}

func TestNewPluginScanner(t *testing.T) {
	manifestLoader := &MoqManifestLoader{}
	scanner := NewPluginScanner(manifestLoader)

	assert.NotNil(t, scanner)
	assert.Equal(t, manifestLoader, scanner.manifestLoader)
}

func TestPluginScanner_ScanPluginDirectory_DuplicateNodeDefinitions(t *testing.T) {
	givePluginPath := t.TempDir()
	giveNode1Path := filepath.Join(givePluginPath, "nodes", "node1")
	giveNode2Path := filepath.Join(givePluginPath, "nodes", "node2")
	err := os.MkdirAll(giveNode1Path, 0755)
	require.NoError(t, err)
	err = os.MkdirAll(giveNode2Path, 0755)
	require.NoError(t, err)

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*PluginManifest, error) {
			return &PluginManifest{
				Name:        "test-plugin",
				Version:     "1.0.0",
				Author:      "test-author",
				DisplayName: "Test Plugin",
				Description: "A test plugin",
				Icon:        "test-icon.png",
			}, nil
		},
		LoadNodeFunc: func(path string) (*NodeDefinitionManifest, error) {
			// 两个节点都返回相同的类型和版本，应该产生重复
			return &NodeDefinitionManifest{
				Name:         "duplicate-node",
				Author:       "test-author",
				Version:      "1.0.0",
				Description:  "A duplicate node",
				Icon:         "duplicate-icon.png",
				Type:         "duplicate-type", // 相同的类型
				Category:     "test",
				Exception:    false,
				InputParams:  []*NodeParam{},
				OutputParams: []*NodeParam{},
				InputPorts:   []*NodePort{},
				OutputPorts:  []*NodePort{},
			}, nil
		},
	}

	scanner := NewPluginScanner(manifestLoader)
	gotPlugin, err := scanner.ScanPluginDirectory(givePluginPath, false)

	assert.NoError(t, err)
	assert.NotNil(t, gotPlugin)
	assert.Equal(t, "test-plugin", gotPlugin.Name())
	// 由于重复的节点定义会被跳过，应该只有一个节点定义
	assert.Len(t, gotPlugin.NodeDefinitions(), 1)
	assert.Equal(t, "duplicate-node", gotPlugin.NodeDefinitions()[0].Name())
}

func TestPluginScanner_ErrorConstants(t *testing.T) {
	// 测试错误常量的定义
	assert.NotEmpty(t, ErrLoadPluginManifest.Error())
	assert.NotEmpty(t, ErrNodeInfoMissing.Error())
	assert.NotEmpty(t, ErrReadNodesDir.Error())
	assert.NotEmpty(t, ErrLoadNodeDefinitionManifest.Error())

	// 验证错误信息内容
	assert.Contains(t, ErrLoadPluginManifest.Error(), "plugin")
	assert.Contains(t, ErrNodeInfoMissing.Error(), "node")
	assert.Contains(t, ErrReadNodesDir.Error(), "nodes")
	assert.Contains(t, ErrLoadNodeDefinitionManifest.Error(), "node definition")
}

// 辅助函数：根据名称查找节点定义
func findNodeByName(nodes []*NodeDefinition, name string) *NodeDefinition {
	for _, node := range nodes {
		if node.Name() == name {
			return node
		}
	}
	return nil
}
