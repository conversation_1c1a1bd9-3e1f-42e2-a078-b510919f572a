package domain

import (
	"context"
	"errors"
)

var (
	ErrPluginAlreadyInstalled = errors.New("plugin already installed")
)

type PluginManager struct {
	repository PluginRepository
}

func NewPluginManager(repository PluginRepository) *PluginManager {
	return &PluginManager{
		repository: repository,
	}
}

func (pm *PluginManager) Install(ctx context.Context, plugin *Plugin) error {
	existingPlugin, err := pm.repository.FindByNameAndVersion(ctx, plugin.Name(), plugin.Version().String())
	if err != nil {
		return err
	}
	if existingPlugin != nil {
		return ErrPluginAlreadyInstalled
	}
	plugin.Enable()
	return nil
}

func (pm *PluginManager) EnablePlugin(ctx context.Context, plugin *Plugin) error {
	err := pm.repository.DeactivateOtherVersion(ctx, plugin.Version().String())
	if err != nil {
		return err
	}
	plugin.Enable()
	return nil
}
