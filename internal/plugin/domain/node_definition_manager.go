package domain

import (
	"context"
)

type NodeDefinitionManager struct {
	repository NodeDefinitionRepository
}

func NewNodeDefinitionManager(repository NodeDefinitionRepository) *NodeDefinitionManager {
	return &NodeDefinitionManager{repository: repository}
}

func (ndi NodeDefinitionManager) Install(ctx context.Context, plugin *Plugin, nodeDefinitions []*NodeDefinition) ([]*NodeDefinition, error) {
	installedNodeDefinitions := make([]*NodeDefinition, 0)
	for _, nodeDefinition := range nodeDefinitions {
		found, err := ndi.repository.FindByTypeAndVersion(ctx, plugin.Name(), nodeDefinition.Type(), nodeDefinition.Version().String())
		if err != nil {
			return nil, err
		}
		latest, err := ndi.repository.FindLatestByType(ctx, plugin.Name(), nodeDefinition.Type())
		if err != nil {
			return nil, err
		}

		if found != nil {
			continue
		}

		if latest != nil {
			if nodeDefinition.Version().Compare(latest.Version()) > 0 {
				err = ndi.upgradeNodeDefinition(ctx, plugin, nodeDefinition)
				if err != nil {
					return nil, err
				}
			}
		} else {
			nodeDefinition.Activate()
		}

		installedNodeDefinitions = append(installedNodeDefinitions, nodeDefinition)
		err = ndi.repository.Save(ctx, nodeDefinition)
		if err != nil {
			return nil, err
		}
	}

	return installedNodeDefinitions, nil
}

func (ndi NodeDefinitionManager) upgradeNodeDefinition(ctx context.Context, plugin *Plugin, nodeDefinition *NodeDefinition) error {
	err := ndi.repository.DisableByType(ctx, plugin.Name(), nodeDefinition.Type())
	if err != nil {
		return err
	}
	nodeDefinition.Activate()
	return nil
}
