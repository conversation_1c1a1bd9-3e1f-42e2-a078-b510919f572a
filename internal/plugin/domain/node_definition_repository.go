package domain

import "context"

type NodeDefinitionRepository interface {
	FindByTypeAndVersion(ctx context.Context, pluginName, typeVar, version string) (*NodeDefinition, error)
	FindLatestByType(ctx context.Context, pluginName, typeVar string) (*NodeDefinition, error)
	Save(ctx context.Context, nodeDefinition *NodeDefinition) error
	DisableByType(ctx context.Context, pluginName, typeVar string) error
}
