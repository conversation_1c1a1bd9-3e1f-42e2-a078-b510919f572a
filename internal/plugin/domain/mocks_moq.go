// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: matryer

package domain

import (
	"context"
	"sync"
)

// Ensure that MoqManifestLoader does implement ManifestLoader.
// If this is not the case, regenerate this file with mockery.
var _ ManifestLoader = &MoqManifestLoader{}

// MoqManifestLoader is a mock implementation of ManifestLoader.
//
//	func TestSomethingThatUsesManifestLoader(t *testing.T) {
//
//		// make and configure a mocked ManifestLoader
//		mockedManifestLoader := &MoqManifestLoader{
//			LoadNodeFunc: func(path string) (*NodeDefinitionManifest, error) {
//				panic("mock out the LoadNode method")
//			},
//			LoadPluginFunc: func(path string) (*PluginManifest, error) {
//				panic("mock out the LoadPlugin method")
//			},
//		}
//
//		// use mockedManifestLoader in code that requires <PERSON><PERSON><PERSON><PERSON>oa<PERSON>
//		// and then make assertions.
//
//	}
type MoqManifestLoader struct {
	// LoadNodeFunc mocks the LoadNode method.
	LoadNodeFunc func(path string) (*NodeDefinitionManifest, error)

	// LoadPluginFunc mocks the LoadPlugin method.
	LoadPluginFunc func(path string) (*PluginManifest, error)

	// calls tracks calls to the methods.
	calls struct {
		// LoadNode holds details about calls to the LoadNode method.
		LoadNode []struct {
			// Path is the path argument value.
			Path string
		}
		// LoadPlugin holds details about calls to the LoadPlugin method.
		LoadPlugin []struct {
			// Path is the path argument value.
			Path string
		}
	}
	lockLoadNode   sync.RWMutex
	lockLoadPlugin sync.RWMutex
}

// LoadNode calls LoadNodeFunc.
func (mock *MoqManifestLoader) LoadNode(path string) (*NodeDefinitionManifest, error) {
	if mock.LoadNodeFunc == nil {
		panic("MoqManifestLoader.LoadNodeFunc: method is nil but ManifestLoader.LoadNode was just called")
	}
	callInfo := struct {
		Path string
	}{
		Path: path,
	}
	mock.lockLoadNode.Lock()
	mock.calls.LoadNode = append(mock.calls.LoadNode, callInfo)
	mock.lockLoadNode.Unlock()
	return mock.LoadNodeFunc(path)
}

// LoadNodeCalls gets all the calls that were made to LoadNode.
// Check the length with:
//
//	len(mockedManifestLoader.LoadNodeCalls())
func (mock *MoqManifestLoader) LoadNodeCalls() []struct {
	Path string
} {
	var calls []struct {
		Path string
	}
	mock.lockLoadNode.RLock()
	calls = mock.calls.LoadNode
	mock.lockLoadNode.RUnlock()
	return calls
}

// LoadPlugin calls LoadPluginFunc.
func (mock *MoqManifestLoader) LoadPlugin(path string) (*PluginManifest, error) {
	if mock.LoadPluginFunc == nil {
		panic("MoqManifestLoader.LoadPluginFunc: method is nil but ManifestLoader.LoadPlugin was just called")
	}
	callInfo := struct {
		Path string
	}{
		Path: path,
	}
	mock.lockLoadPlugin.Lock()
	mock.calls.LoadPlugin = append(mock.calls.LoadPlugin, callInfo)
	mock.lockLoadPlugin.Unlock()
	return mock.LoadPluginFunc(path)
}

// LoadPluginCalls gets all the calls that were made to LoadPlugin.
// Check the length with:
//
//	len(mockedManifestLoader.LoadPluginCalls())
func (mock *MoqManifestLoader) LoadPluginCalls() []struct {
	Path string
} {
	var calls []struct {
		Path string
	}
	mock.lockLoadPlugin.RLock()
	calls = mock.calls.LoadPlugin
	mock.lockLoadPlugin.RUnlock()
	return calls
}

// Ensure that MoqNodeDefinitionRepository does implement NodeDefinitionRepository.
// If this is not the case, regenerate this file with mockery.
var _ NodeDefinitionRepository = &MoqNodeDefinitionRepository{}

// MoqNodeDefinitionRepository is a mock implementation of NodeDefinitionRepository.
//
//	func TestSomethingThatUsesNodeDefinitionRepository(t *testing.T) {
//
//		// make and configure a mocked NodeDefinitionRepository
//		mockedNodeDefinitionRepository := &MoqNodeDefinitionRepository{
//			DisableByTypeFunc: func(ctx context.Context, pluginName string, typeVar string) error {
//				panic("mock out the DisableByType method")
//			},
//			FindByTypeAndVersionFunc: func(ctx context.Context, pluginName string, typeVar string, version string) (*NodeDefinition, error) {
//				panic("mock out the FindByTypeAndVersion method")
//			},
//			FindLatestByTypeFunc: func(ctx context.Context, pluginName string, typeVar string) (*NodeDefinition, error) {
//				panic("mock out the FindLatestByType method")
//			},
//			SaveFunc: func(ctx context.Context, nodeDefinition *NodeDefinition) error {
//				panic("mock out the Save method")
//			},
//		}
//
//		// use mockedNodeDefinitionRepository in code that requires NodeDefinitionRepository
//		// and then make assertions.
//
//	}
type MoqNodeDefinitionRepository struct {
	// DisableByTypeFunc mocks the DisableByType method.
	DisableByTypeFunc func(ctx context.Context, pluginName string, typeVar string) error

	// FindByTypeAndVersionFunc mocks the FindByTypeAndVersion method.
	FindByTypeAndVersionFunc func(ctx context.Context, pluginName string, typeVar string, version string) (*NodeDefinition, error)

	// FindLatestByTypeFunc mocks the FindLatestByType method.
	FindLatestByTypeFunc func(ctx context.Context, pluginName string, typeVar string) (*NodeDefinition, error)

	// SaveFunc mocks the Save method.
	SaveFunc func(ctx context.Context, nodeDefinition *NodeDefinition) error

	// calls tracks calls to the methods.
	calls struct {
		// DisableByType holds details about calls to the DisableByType method.
		DisableByType []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// PluginName is the pluginName argument value.
			PluginName string
			// TypeVar is the typeVar argument value.
			TypeVar string
		}
		// FindByTypeAndVersion holds details about calls to the FindByTypeAndVersion method.
		FindByTypeAndVersion []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// PluginName is the pluginName argument value.
			PluginName string
			// TypeVar is the typeVar argument value.
			TypeVar string
			// Version is the version argument value.
			Version string
		}
		// FindLatestByType holds details about calls to the FindLatestByType method.
		FindLatestByType []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// PluginName is the pluginName argument value.
			PluginName string
			// TypeVar is the typeVar argument value.
			TypeVar string
		}
		// Save holds details about calls to the Save method.
		Save []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// NodeDefinition is the nodeDefinition argument value.
			NodeDefinition *NodeDefinition
		}
	}
	lockDisableByType        sync.RWMutex
	lockFindByTypeAndVersion sync.RWMutex
	lockFindLatestByType     sync.RWMutex
	lockSave                 sync.RWMutex
}

// DisableByType calls DisableByTypeFunc.
func (mock *MoqNodeDefinitionRepository) DisableByType(ctx context.Context, pluginName string, typeVar string) error {
	if mock.DisableByTypeFunc == nil {
		panic("MoqNodeDefinitionRepository.DisableByTypeFunc: method is nil but NodeDefinitionRepository.DisableByType was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		PluginName string
		TypeVar    string
	}{
		Ctx:        ctx,
		PluginName: pluginName,
		TypeVar:    typeVar,
	}
	mock.lockDisableByType.Lock()
	mock.calls.DisableByType = append(mock.calls.DisableByType, callInfo)
	mock.lockDisableByType.Unlock()
	return mock.DisableByTypeFunc(ctx, pluginName, typeVar)
}

// DisableByTypeCalls gets all the calls that were made to DisableByType.
// Check the length with:
//
//	len(mockedNodeDefinitionRepository.DisableByTypeCalls())
func (mock *MoqNodeDefinitionRepository) DisableByTypeCalls() []struct {
	Ctx        context.Context
	PluginName string
	TypeVar    string
} {
	var calls []struct {
		Ctx        context.Context
		PluginName string
		TypeVar    string
	}
	mock.lockDisableByType.RLock()
	calls = mock.calls.DisableByType
	mock.lockDisableByType.RUnlock()
	return calls
}

// FindByTypeAndVersion calls FindByTypeAndVersionFunc.
func (mock *MoqNodeDefinitionRepository) FindByTypeAndVersion(ctx context.Context, pluginName string, typeVar string, version string) (*NodeDefinition, error) {
	if mock.FindByTypeAndVersionFunc == nil {
		panic("MoqNodeDefinitionRepository.FindByTypeAndVersionFunc: method is nil but NodeDefinitionRepository.FindByTypeAndVersion was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		PluginName string
		TypeVar    string
		Version    string
	}{
		Ctx:        ctx,
		PluginName: pluginName,
		TypeVar:    typeVar,
		Version:    version,
	}
	mock.lockFindByTypeAndVersion.Lock()
	mock.calls.FindByTypeAndVersion = append(mock.calls.FindByTypeAndVersion, callInfo)
	mock.lockFindByTypeAndVersion.Unlock()
	return mock.FindByTypeAndVersionFunc(ctx, pluginName, typeVar, version)
}

// FindByTypeAndVersionCalls gets all the calls that were made to FindByTypeAndVersion.
// Check the length with:
//
//	len(mockedNodeDefinitionRepository.FindByTypeAndVersionCalls())
func (mock *MoqNodeDefinitionRepository) FindByTypeAndVersionCalls() []struct {
	Ctx        context.Context
	PluginName string
	TypeVar    string
	Version    string
} {
	var calls []struct {
		Ctx        context.Context
		PluginName string
		TypeVar    string
		Version    string
	}
	mock.lockFindByTypeAndVersion.RLock()
	calls = mock.calls.FindByTypeAndVersion
	mock.lockFindByTypeAndVersion.RUnlock()
	return calls
}

// FindLatestByType calls FindLatestByTypeFunc.
func (mock *MoqNodeDefinitionRepository) FindLatestByType(ctx context.Context, pluginName string, typeVar string) (*NodeDefinition, error) {
	if mock.FindLatestByTypeFunc == nil {
		panic("MoqNodeDefinitionRepository.FindLatestByTypeFunc: method is nil but NodeDefinitionRepository.FindLatestByType was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		PluginName string
		TypeVar    string
	}{
		Ctx:        ctx,
		PluginName: pluginName,
		TypeVar:    typeVar,
	}
	mock.lockFindLatestByType.Lock()
	mock.calls.FindLatestByType = append(mock.calls.FindLatestByType, callInfo)
	mock.lockFindLatestByType.Unlock()
	return mock.FindLatestByTypeFunc(ctx, pluginName, typeVar)
}

// FindLatestByTypeCalls gets all the calls that were made to FindLatestByType.
// Check the length with:
//
//	len(mockedNodeDefinitionRepository.FindLatestByTypeCalls())
func (mock *MoqNodeDefinitionRepository) FindLatestByTypeCalls() []struct {
	Ctx        context.Context
	PluginName string
	TypeVar    string
} {
	var calls []struct {
		Ctx        context.Context
		PluginName string
		TypeVar    string
	}
	mock.lockFindLatestByType.RLock()
	calls = mock.calls.FindLatestByType
	mock.lockFindLatestByType.RUnlock()
	return calls
}

// Save calls SaveFunc.
func (mock *MoqNodeDefinitionRepository) Save(ctx context.Context, nodeDefinition *NodeDefinition) error {
	if mock.SaveFunc == nil {
		panic("MoqNodeDefinitionRepository.SaveFunc: method is nil but NodeDefinitionRepository.Save was just called")
	}
	callInfo := struct {
		Ctx            context.Context
		NodeDefinition *NodeDefinition
	}{
		Ctx:            ctx,
		NodeDefinition: nodeDefinition,
	}
	mock.lockSave.Lock()
	mock.calls.Save = append(mock.calls.Save, callInfo)
	mock.lockSave.Unlock()
	return mock.SaveFunc(ctx, nodeDefinition)
}

// SaveCalls gets all the calls that were made to Save.
// Check the length with:
//
//	len(mockedNodeDefinitionRepository.SaveCalls())
func (mock *MoqNodeDefinitionRepository) SaveCalls() []struct {
	Ctx            context.Context
	NodeDefinition *NodeDefinition
} {
	var calls []struct {
		Ctx            context.Context
		NodeDefinition *NodeDefinition
	}
	mock.lockSave.RLock()
	calls = mock.calls.Save
	mock.lockSave.RUnlock()
	return calls
}

// Ensure that MoqPluginRepository does implement PluginRepository.
// If this is not the case, regenerate this file with mockery.
var _ PluginRepository = &MoqPluginRepository{}

// MoqPluginRepository is a mock implementation of PluginRepository.
//
//	func TestSomethingThatUsesPluginRepository(t *testing.T) {
//
//		// make and configure a mocked PluginRepository
//		mockedPluginRepository := &MoqPluginRepository{
//			DeactivateOtherVersionFunc: func(ctx context.Context, version string) error {
//				panic("mock out the DeactivateOtherVersion method")
//			},
//			FindByNameAndVersionFunc: func(ctx context.Context, name string, version string) (*Plugin, error) {
//				panic("mock out the FindByNameAndVersion method")
//			},
//			ListFunc: func(ctx context.Context) ([]*Plugin, error) {
//				panic("mock out the List method")
//			},
//			SaveFunc: func(ctx context.Context, plugin *Plugin) error {
//				panic("mock out the Save method")
//			},
//		}
//
//		// use mockedPluginRepository in code that requires PluginRepository
//		// and then make assertions.
//
//	}
type MoqPluginRepository struct {
	// DeactivateOtherVersionFunc mocks the DeactivateOtherVersion method.
	DeactivateOtherVersionFunc func(ctx context.Context, version string) error

	// FindByNameAndVersionFunc mocks the FindByNameAndVersion method.
	FindByNameAndVersionFunc func(ctx context.Context, name string, version string) (*Plugin, error)

	// ListFunc mocks the List method.
	ListFunc func(ctx context.Context) ([]*Plugin, error)

	// SaveFunc mocks the Save method.
	SaveFunc func(ctx context.Context, plugin *Plugin) error

	// calls tracks calls to the methods.
	calls struct {
		// DeactivateOtherVersion holds details about calls to the DeactivateOtherVersion method.
		DeactivateOtherVersion []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Version is the version argument value.
			Version string
		}
		// FindByNameAndVersion holds details about calls to the FindByNameAndVersion method.
		FindByNameAndVersion []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Name is the name argument value.
			Name string
			// Version is the version argument value.
			Version string
		}
		// List holds details about calls to the List method.
		List []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
		}
		// Save holds details about calls to the Save method.
		Save []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Plugin is the plugin argument value.
			Plugin *Plugin
		}
	}
	lockDeactivateOtherVersion sync.RWMutex
	lockFindByNameAndVersion   sync.RWMutex
	lockList                   sync.RWMutex
	lockSave                   sync.RWMutex
}

// DeactivateOtherVersion calls DeactivateOtherVersionFunc.
func (mock *MoqPluginRepository) DeactivateOtherVersion(ctx context.Context, version string) error {
	if mock.DeactivateOtherVersionFunc == nil {
		panic("MoqPluginRepository.DeactivateOtherVersionFunc: method is nil but PluginRepository.DeactivateOtherVersion was just called")
	}
	callInfo := struct {
		Ctx     context.Context
		Version string
	}{
		Ctx:     ctx,
		Version: version,
	}
	mock.lockDeactivateOtherVersion.Lock()
	mock.calls.DeactivateOtherVersion = append(mock.calls.DeactivateOtherVersion, callInfo)
	mock.lockDeactivateOtherVersion.Unlock()
	return mock.DeactivateOtherVersionFunc(ctx, version)
}

// DeactivateOtherVersionCalls gets all the calls that were made to DeactivateOtherVersion.
// Check the length with:
//
//	len(mockedPluginRepository.DeactivateOtherVersionCalls())
func (mock *MoqPluginRepository) DeactivateOtherVersionCalls() []struct {
	Ctx     context.Context
	Version string
} {
	var calls []struct {
		Ctx     context.Context
		Version string
	}
	mock.lockDeactivateOtherVersion.RLock()
	calls = mock.calls.DeactivateOtherVersion
	mock.lockDeactivateOtherVersion.RUnlock()
	return calls
}

// FindByNameAndVersion calls FindByNameAndVersionFunc.
func (mock *MoqPluginRepository) FindByNameAndVersion(ctx context.Context, name string, version string) (*Plugin, error) {
	if mock.FindByNameAndVersionFunc == nil {
		panic("MoqPluginRepository.FindByNameAndVersionFunc: method is nil but PluginRepository.FindByNameAndVersion was just called")
	}
	callInfo := struct {
		Ctx     context.Context
		Name    string
		Version string
	}{
		Ctx:     ctx,
		Name:    name,
		Version: version,
	}
	mock.lockFindByNameAndVersion.Lock()
	mock.calls.FindByNameAndVersion = append(mock.calls.FindByNameAndVersion, callInfo)
	mock.lockFindByNameAndVersion.Unlock()
	return mock.FindByNameAndVersionFunc(ctx, name, version)
}

// FindByNameAndVersionCalls gets all the calls that were made to FindByNameAndVersion.
// Check the length with:
//
//	len(mockedPluginRepository.FindByNameAndVersionCalls())
func (mock *MoqPluginRepository) FindByNameAndVersionCalls() []struct {
	Ctx     context.Context
	Name    string
	Version string
} {
	var calls []struct {
		Ctx     context.Context
		Name    string
		Version string
	}
	mock.lockFindByNameAndVersion.RLock()
	calls = mock.calls.FindByNameAndVersion
	mock.lockFindByNameAndVersion.RUnlock()
	return calls
}

// List calls ListFunc.
func (mock *MoqPluginRepository) List(ctx context.Context) ([]*Plugin, error) {
	if mock.ListFunc == nil {
		panic("MoqPluginRepository.ListFunc: method is nil but PluginRepository.List was just called")
	}
	callInfo := struct {
		Ctx context.Context
	}{
		Ctx: ctx,
	}
	mock.lockList.Lock()
	mock.calls.List = append(mock.calls.List, callInfo)
	mock.lockList.Unlock()
	return mock.ListFunc(ctx)
}

// ListCalls gets all the calls that were made to List.
// Check the length with:
//
//	len(mockedPluginRepository.ListCalls())
func (mock *MoqPluginRepository) ListCalls() []struct {
	Ctx context.Context
} {
	var calls []struct {
		Ctx context.Context
	}
	mock.lockList.RLock()
	calls = mock.calls.List
	mock.lockList.RUnlock()
	return calls
}

// Save calls SaveFunc.
func (mock *MoqPluginRepository) Save(ctx context.Context, plugin *Plugin) error {
	if mock.SaveFunc == nil {
		panic("MoqPluginRepository.SaveFunc: method is nil but PluginRepository.Save was just called")
	}
	callInfo := struct {
		Ctx    context.Context
		Plugin *Plugin
	}{
		Ctx:    ctx,
		Plugin: plugin,
	}
	mock.lockSave.Lock()
	mock.calls.Save = append(mock.calls.Save, callInfo)
	mock.lockSave.Unlock()
	return mock.SaveFunc(ctx, plugin)
}

// SaveCalls gets all the calls that were made to Save.
// Check the length with:
//
//	len(mockedPluginRepository.SaveCalls())
func (mock *MoqPluginRepository) SaveCalls() []struct {
	Ctx    context.Context
	Plugin *Plugin
} {
	var calls []struct {
		Ctx    context.Context
		Plugin *Plugin
	}
	mock.lockSave.RLock()
	calls = mock.calls.Save
	mock.lockSave.RUnlock()
	return calls
}
