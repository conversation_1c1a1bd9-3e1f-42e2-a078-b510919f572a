package domain

import (
	"errors"
	"os"
	"path/filepath"
)

var (
	ErrLoadPluginManifest         = errors.New("load plugin manifest failed")
	ErrNodeInfoMissing            = errors.New("node info missing")
	ErrReadNodesDir               = errors.New("read nodes directory failed")
	ErrLoadNodeDefinitionManifest = errors.New("load node definition manifest failed")
)

type PluginScanner struct {
	manifestLoader ManifestLoader
}

func NewPluginScanner(manifestLoader ManifestLoader) *PluginScanner {
	return &PluginScanner{
		manifestLoader: manifestLoader,
	}
}

func (ps PluginScanner) ScanPluginDirectory(dir string, builtin bool) (*Plugin, error) {
	manifest, err := ps.manifestLoader.LoadPlugin(dir)
	if err != nil {
		return nil, ErrLoadPluginManifest
	}
	pluginAggregate, err := NewPluginFromManifest(manifest, dir, builtin)
	if err != nil {
		return nil, err
	}

	nodeDefinitions, err := ps.scanNodesDirectory(pluginAggregate, filepath.Join(dir, "nodes"))
	if err != nil {
		return nil, err
	}

	for _, nodeDefinition := range nodeDefinitions {
		err = pluginAggregate.AddNodeDefinition(nodeDefinition)
		if err != nil {
			continue
		}
	}

	return pluginAggregate, nil
}

func (ps PluginScanner) scanNodesDirectory(plugin *Plugin, nodesPath string) ([]*NodeDefinition, error) {

	files, err := os.ReadDir(nodesPath)
	if err != nil {
		return nil, ErrReadNodesDir
	}

	nodeDefinitions := make([]*NodeDefinition, 0)
	for _, file := range files {
		if !file.IsDir() {
			continue
		}

		nodeDef, err := ps.scanNodeDirectory(plugin, filepath.Join(nodesPath, file.Name()))
		if err != nil {
			return nil, err
		}
		nodeDefinitions = append(nodeDefinitions, nodeDef)
	}
	return nodeDefinitions, nil
}

func (ps PluginScanner) scanNodeDirectory(plugin *Plugin, nodePath string) (*NodeDefinition, error) {
	nodeManifest, err := ps.manifestLoader.LoadNode(nodePath)
	if err != nil {
		return nil, ErrLoadNodeDefinitionManifest
	}

	if nodeManifest.Type == "" || nodeManifest.Version == "" {
		return nil, ErrNodeInfoMissing
	}

	return NewNodeDefinitionFromManifest(plugin, nodeManifest, nodePath)
}
