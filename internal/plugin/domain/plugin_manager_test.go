package domain

import (
	"context"
	"resflow/internal/common/domain"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPluginManager_Install_Success(t *testing.T) {
	mockRepo := &MoqPluginRepository{
		FindByNameAndVersionFunc: func(ctx context.Context, name string, version string) (*Plugin, error) {
			return nil, nil
		},
	}
	pm := NewPluginManager(mockRepo)
	plugin := &Plugin{
		name: "test",
		version: &domain.Version{
			Major: 1,
			Minor: 0,
			Patch: 0,
		},
	}
	err := pm.Install(context.Background(), plugin)
	assert.NoError(t, err)
	assert.True(t, plugin.Enabled())
}

func TestPluginManager_Install_AlreadyInstalled(t *testing.T) {
	mockRepo := &MoqPluginRepository{
		FindByNameAndVersionFunc: func(ctx context.Context, name string, version string) (*Plugin, error) {
			return &Plugin{}, nil
		},
	}
	pm := NewPluginManager(mockRepo)
	plugin := &Plugin{
		name: "test",
		version: &domain.Version{
			Major: 1,
			Minor: 0,
			Patch: 0,
		},
	}
	err := pm.Install(context.Background(), plugin)
	assert.ErrorIs(t, err, ErrPluginAlreadyInstalled)
	assert.False(t, plugin.Enabled())
}

func TestPluginManager_Install_Error(t *testing.T) {
	expectedError := assert.AnError
	mockRepo := &MoqPluginRepository{
		FindByNameAndVersionFunc: func(ctx context.Context, name string, version string) (*Plugin, error) {
			return nil, expectedError
		},
	}
	pm := NewPluginManager(mockRepo)
	plugin := &Plugin{
		name: "test",
		version: &domain.Version{
			Major: 1,
			Minor: 0,
			Patch: 0,
		},
	}
	err := pm.Install(context.Background(), plugin)
	assert.ErrorIs(t, err, expectedError)
	assert.False(t, plugin.Enabled())
}

func TestPluginManager_EnablePlugin_Success(t *testing.T) {
	mockRepo := &MoqPluginRepository{
		DeactivateOtherVersionFunc: func(ctx context.Context, version string) error {
			return nil
		},
	}
	pm := NewPluginManager(mockRepo)
	plugin := &Plugin{
		version: &domain.Version{
			Major: 1,
			Minor: 0,
			Patch: 0,
		},
		enabled: false,
	}
	err := pm.EnablePlugin(context.Background(), plugin)
	assert.NoError(t, err)
	assert.True(t, plugin.Enabled())
}

func TestPluginManager_EnablePlugin_Error(t *testing.T) {
	expectedError := assert.AnError
	mockRepo := &MoqPluginRepository{
		DeactivateOtherVersionFunc: func(ctx context.Context, version string) error {
			return expectedError
		},
	}
	pm := NewPluginManager(mockRepo)
	plugin := &Plugin{
		version: &domain.Version{
			Major: 1,
			Minor: 0,
			Patch: 0,
		},
		enabled: false,
	}
	err := pm.EnablePlugin(context.Background(), plugin)
	assert.ErrorIs(t, err, expectedError)
	assert.False(t, plugin.Enabled())
}
