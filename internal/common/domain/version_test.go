package domain

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewVersion(t *testing.T) {
	cases := []struct {
		CaseDoc string
		Give    string
		Want    *Version
		WantErr error
	}{
		{
			CaseDoc: "成功创建版本",
			Give:    "1.0.0",
			Want:    &Version{Major: 1, Minor: 0, Patch: 0},
			WantErr: nil,
		},
		{
			CaseDoc: "版本长度错误",
			Give:    "1.0",
			Want:    nil,
			WantErr: ErrInvalidVersionLength,
		},
		{
			CaseDoc: "版本长度错误",
			Give:    "1.0.0.0",
			Want:    nil,
			WantErr: ErrInvalidVersionLength,
		},
		{
			CaseDoc: "版本格式错误",
			Give:    "1.0.a",
			Want:    nil,
			WantErr: ErrInvalidVersionFormat,
		},
		{
			CaseDoc: "版本格式错误",
			Give:    "1.a.0",
			Want:    nil,
			WantErr: ErrInvalidVersionFormat,
		},
		{
			CaseDoc: "版本格式错误",
			Give:    "a.0.0",
			Want:    nil,
			WantErr: ErrInvalidVersionFormat,
		},
	}
	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			got, err := NewVersion(tc.Give)
			assert.Equal(t, tc.Want, got)
			assert.Equal(t, tc.WantErr, err)
		})
	}
}

func TestVersion_String(t *testing.T) {
	cases := []struct {
		CaseDoc string
		Give    *Version
		Want    string
	}{
		{
			CaseDoc: "成功",
			Give:    &Version{Major: 1, Minor: 0, Patch: 0},
			Want:    "1.0.0",
		},
	}
	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			got := tc.Give.String()
			assert.Equal(t, tc.Want, got)
		})
	}
}

func TestVersion_Compare(t *testing.T) {
	cases := []struct {
		CaseDoc string
		V1      *Version
		V2      *Version
		Want    int
	}{
		{
			CaseDoc: "v1.Major > v2.Major",
			V1:      &Version{Major: 1, Minor: 0, Patch: 0},
			V2:      &Version{Major: 0, Minor: 9, Patch: 9},
			Want:    1,
		},
		{
			CaseDoc: "v1.Major < v2.Major",
			V1:      &Version{Major: 0, Minor: 9, Patch: 9},
			V2:      &Version{Major: 1, Minor: 0, Patch: 0},
			Want:    -1,
		},
		{
			CaseDoc: "v1.Major = v2.Major",
			V1:      &Version{Major: 1, Minor: 0, Patch: 0},
			V2:      &Version{Major: 1, Minor: 0, Patch: 0},
			Want:    0,
		},
		{
			CaseDoc: "v1.Minor > v2.Minor",
			V1:      &Version{Major: 1, Minor: 1, Patch: 0},
			V2:      &Version{Major: 1, Minor: 0, Patch: 0},
			Want:    1,
		},
		{
			CaseDoc: "v1.Minor < v2.Minor",
			V1:      &Version{Major: 1, Minor: 0, Patch: 0},
			V2:      &Version{Major: 1, Minor: 1, Patch: 0},
			Want:    -1,
		},
		{
			CaseDoc: "v1.Minor = v2.Minor",
			V1:      &Version{Major: 1, Minor: 0, Patch: 0},
			V2:      &Version{Major: 1, Minor: 0, Patch: 0},
			Want:    0,
		},
		{
			CaseDoc: "v1.Patch > v2.Patch",
			V1:      &Version{Major: 1, Minor: 0, Patch: 1},
			V2:      &Version{Major: 1, Minor: 0, Patch: 0},
			Want:    1,
		},
		{
			CaseDoc: "v1.Patch < v2.Patch",
			V1:      &Version{Major: 1, Minor: 0, Patch: 0},
			V2:      &Version{Major: 1, Minor: 0, Patch: 1},
			Want:    -1,
		},
		{
			CaseDoc: "v1.Patch = v2.Patch",
			V1:      &Version{Major: 1, Minor: 0, Patch: 0},
			V2:      &Version{Major: 1, Minor: 0, Patch: 0},
			Want:    0,
		},
	}
	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			got := tc.V1.Compare(tc.V2)
			assert.Equal(t, tc.Want, got)
		})
	}
}
