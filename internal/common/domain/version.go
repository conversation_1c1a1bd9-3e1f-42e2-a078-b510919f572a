package domain

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
)

type Version struct {
	Major int
	Minor int
	Patch int
}

const versionLen = 3

var (
	ErrInvalidVersionLength = errors.New("invalid giveVersion length")
	ErrInvalidVersionFormat = errors.New("invalid giveVersion format")
)

func NewVersion(version string) (*Version, error) {
	parts := strings.Split(version, ".")
	if len(parts) != versionLen {
		return nil, ErrInvalidVersionLength
	}
	var intParts [versionLen]int
	for i, part := range parts {
		intPart, err := strconv.Atoi(part)
		if err != nil {
			return nil, ErrInvalidVersionFormat
		}
		intParts[i] = intPart
	}
	return &Version{
		Major: intParts[0],
		Minor: intParts[1],
		Patch: intParts[2],
	}, nil
}

func (v *Version) String() string {
	return fmt.Sprintf("%d.%d.%d", v.<PERSON>, v.<PERSON>, v.<PERSON>)
}

// Compare 比较版本大小 返回 -1(小于), 0(等于), 1(大于)
func (v *Version) Compare(other *Version) int {
	if v.Major != other.Major {
		if v.Major < other.Major {
			return -1
		}
		return 1
	}
	if v.Minor != other.Minor {
		if v.Minor < other.Minor {
			return -1
		}
		return 1
	}
	if v.Patch != other.Patch {
		if v.Patch < other.Patch {
			return -1
		}
		return 1
	}
	return 0
}
