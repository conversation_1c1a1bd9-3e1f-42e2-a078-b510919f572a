package domain

import (
	"time"

	"github.com/google/uuid"
)

type AggregateRoot struct {
	id        uuid.UUID
	createdAt time.Time
	updatedAt time.Time
}

func NewAggregateRoot(id uuid.UUID, createdAt, updatedAt time.Time) AggregateRoot {
	return AggregateRoot{
		id:        id,
		createdAt: createdAt,
		updatedAt: updatedAt,
	}
}

func (ar AggregateRoot) ID() uuid.UUID {
	return ar.id
}

func (ar AggregateRoot) CreatedAt() time.Time {
	return ar.createdAt
}

func (ar AggregateRoot) UpdatedAt() time.Time {
	return ar.updatedAt
}
