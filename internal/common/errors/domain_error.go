package errors

import "errors"

type ErrorCode string

type DomainError struct {
	Code    ErrorCode
	Message string
	Cause   error
}

func (e *DomainError) Error() string {
	return e.Message
}
func NewDomainError(code ErrorCode, msg string, cause error) *DomainError {
	return &DomainError{Code: code, Message: msg, Cause: cause}
}

func (e *DomainError) Is(target error) bool {
	var t *DomainError
	ok := errors.As(target, &t)
	if !ok {
		return false
	}
	return e.Code == t.Code
}

func IsDomainError(err, target error) bool {
	return errors.Is(err, target)
}
