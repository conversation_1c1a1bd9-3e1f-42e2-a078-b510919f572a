package errors

import "errors"

type ErrType int

const (
	ErrTypeUser ErrType = iota
	ErrTypeInternal
	ErrTypeUnauthenticated
	ErrTypeUnauthorized
)

type AppError struct {
	Type    ErrType
	Code    string
	Message string
	Cause   error
}

func NewAppError(typeVar ErrType, code string, msg string, cause error) *AppError {
	return &AppError{
		Type:    typeVar,
		Code:    code,
		Message: msg,
		Cause:   cause,
	}
}

func (e AppError) Error() string {
	return e.Message
}

func (e AppError) Unwrap() error {
	return e.Cause
}

func (e AppError) Is(target error) bool {
	var t *AppError
	ok := errors.As(target, &t)
	if !ok {
		return false
	}
	return e.Code == t.Code
}

func IsAppError(err, target error) bool {
	return errors.Is(err, target)
}
