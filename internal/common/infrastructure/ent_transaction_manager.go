package infrastructure

import (
	"context"
	"resflow/ent"
	"resflow/utils"
	"runtime/debug"
)

type EntTransactionManager struct {
	client *ent.Client
}

func NewEntTransactionManager(client *ent.Client) *EntTransactionManager {
	return &EntTransactionManager{client: client}
}

func (etm EntTransactionManager) WithTx(ctx context.Context, fn func(ctx context.Context) error) error {
	tx, err := etm.client.Tx(ctx)
	if err != nil {
		return err
	}
	defer func() {
		if v := recover(); v != nil {
			err = v.(error)
			utils.Logger.Debug("事务提交失败，准备回滚：", v)
			utils.Logger.Debug(string(debug.Stack()))
			if rerr := tx.Rollback(); rerr != nil {
				utils.Logger.Errorf("%w: rolling back transaction: %v", err, rerr)
			}
		}
	}()
	ctx = ent.NewTxContext(ctx, tx)
	if err := fn(ctx); err != nil {
		panic(err)
	}
	if err := tx.Commit(); err != nil {
		utils.Logger.<PERSON>rrorf("committing transaction: %w", err)
		return err
	}
	return nil
}
