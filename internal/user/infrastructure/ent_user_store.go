package infrastructure

import (
	"context"
	"resflow/database"
	"resflow/ent"
	"resflow/ent/user"
	"resflow/internal/user/domain"

	"github.com/google/uuid"
)

type EntUserStore struct {
	client *ent.Client
}

func NewEntUserStore(client *ent.Client) *EntUserStore {
	return &EntUserStore{client: client}
}

func (store *EntUserStore) Save(ctx context.Context, u *domain.User) error {
	exists, err := store.FindById(ctx, u.ID())
	if err != nil {
		return err
	}
	if exists != nil {
		_, err = database.GetTxOrClient(ctx, store.client).User.Update().SetUsername(u.Username()).SetNickname(u.Nickname()).SetPassword(string(u.Password())).SetStatus(u.Status().Int()).Save(ctx)
		return err
	}
	_, err = database.GetTxOrClient(ctx, store.client).User.Create().SetID(u.ID()).SetUsername(u.Username()).SetNickname(u.Nickname()).SetPassword(string(u.Password())).SetStatus(u.Status().Int()).Save(ctx)
	return err
}

func (store *EntUserStore) FindById(ctx context.Context, id uuid.UUID) (*domain.User, error) {
	u, err := store.client.User.Query().Where(user.ID(id)).First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return entityToModel(u), nil
}

func (store *EntUserStore) FindByUsername(ctx context.Context, username string) (*domain.User, error) {
	u, err := store.client.User.Query().Where(user.Username(username)).First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return entityToModel(u), nil
}

func entityToModel(entity *ent.User) *domain.User {
	return domain.ReconstructUser(
		entity.ID,
		entity.CreatedAt,
		entity.UpdatedAt,
		entity.Username,
		entity.Password,
		entity.Nickname,
		domain.UserStatus(entity.Status),
	)
}
