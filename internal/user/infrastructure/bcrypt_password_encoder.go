package infrastructure

import "golang.org/x/crypto/bcrypt"

type BcryptPasswordEncoder struct {
}

func NewBcryptPasswordEncoder() *BcryptPasswordEncoder {
	return &BcryptPasswordEncoder{}
}

func (encoder *BcryptPasswordEncoder) Encode(password string) (string, error) {
	hashed, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(hashed), err
}

func (encoder *BcryptPasswordEncoder) Verify(password string, hashedPassword string) error {
	bcryptHashed := []byte(hashedPassword)
	return bcrypt.CompareHashAndPassword(bcryptHashed, []byte(password))
}
