package application

import "resflow/internal/user/domain"

type UserDTO struct {
	ID        string `json:"id"`
	Username  string `json:"username"`
	Nickname  string `json:"nickname"`
	Status    string `json:"status"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
}

func UserModelToDTO(user *domain.User) *UserDTO {
	return &UserDTO{
		ID:        user.ID().String(),
		Username:  user.Username(),
		Nickname:  user.Nickname(),
		Status:    user.Status().String(),
		CreatedAt: user.CreatedAt().Format("2006-01-02 15:04:05"),
		UpdatedAt: user.UpdatedAt().Format("2006-01-02 15:04:05"),
	}
}
