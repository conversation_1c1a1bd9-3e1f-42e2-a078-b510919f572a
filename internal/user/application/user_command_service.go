package application

import (
	"context"
	"resflow/internal/common/errors"
	"resflow/internal/user/domain"
	"resflow/utils"
)

type UserCommandService struct {
	repository      domain.UserRepository
	passwordEncoder domain.PasswordEncoder
}

func NewUserCommandService(repository domain.UserRepository, passwordEncoder domain.PasswordEncoder) *UserCommandService {
	return &UserCommandService{
		repository:      repository,
		passwordEncoder: passwordEncoder,
	}
}

func (s *UserCommandService) CreateUser(ctx context.Context, createUserCmd *CreateUserCommand) error {
	exists, err := s.repository.FindByUsername(ctx, createUserCmd.Username)
	if err != nil {
		return errors.NewAppError(errors.ErrTypeInternal, ErrCodeFindUserFail, "查找用户失败", err)
	}
	if exists != nil {
		return ErrUserExisted
	}

	newUser, err := domain.NewUser(createUserCmd.Username, createUserCmd.Password, createUserCmd.Nickname, domain.UserStatusActive, s.passwordEncoder)
	if err != nil {
		utils.Logger.Debugf("创建用户失败：%s", err.Error())
		return errors.NewAppError(errors.ErrTypeInternal, ErrCodeCreateUserFail, "创建用户失败", err)
	}
	err = s.repository.Save(ctx, newUser)
	if err != nil {
		utils.Logger.Debugf("保存用户失败：%s", err.Error())
		return errors.NewAppError(errors.ErrTypeInternal, ErrCodeCreateUserFail, "创建用户失败", err)
	}
	return nil
}
