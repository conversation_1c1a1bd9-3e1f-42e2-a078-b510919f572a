package domain

import (
	"resflow/internal/common/domain"
	"time"

	"github.com/google/uuid"
)

type User struct {
	domain.AggregateRoot
	username string
	password Password
	nickname string
	status   UserStatus
}

func NewUser(username, password, nickname string, status UserStatus, encoder PasswordEncoder) (*User, error) {
	if len(username) < 5 {
		return nil, ErrUsernameTooShort
	}

	if len(password) < 8 {
		return nil, ErrWeakPassword
	}

	hashedPassword, err := NewPassword(password, encoder)
	if err != nil {
		return nil, ErrCryptPasswordFail
	}

	return &User{
		AggregateRoot: domain.NewAggregateRoot(uuid.New(), time.Now(), time.Now()),
		username:      username,
		password:      hashedPassword,
		nickname:      nickname,
		status:        status,
	}, nil
}

func ReconstructUser(id uuid.UUID, createdAt, updatedAt time.Time, username string, hashed string, nickname string, status UserStatus) *User {
	return &User{
		AggregateRoot: domain.NewAggregateRoot(id, createdAt, updatedAt),
		username:      username,
		password:      NewHashedPassword(hashed),
		nickname:      nickname,
		status:        status,
	}
}

func (u *User) Username() string {
	return u.username
}

func (u *User) Password() Password {
	return u.password
}

func (u *User) Nickname() string {
	return u.nickname
}

func (u *User) Status() UserStatus {
	return u.status
}

func (u *User) VerifyPassword(password string, passwordEncoder PasswordEncoder) bool {
	err := passwordEncoder.Verify(password, u.Password().String())
	return err == nil
}
