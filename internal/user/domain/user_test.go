package domain

import (
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func TestNewUser_Success(t *testing.T) {
	giveUsername := "test-username"
	giveNickname := "test-nickname"
	givePassword := "test-password"
	giveUserStatus := UserStatusActive

	mockPasswordEncoder := &MoqPasswordEncoder{
		EncodeFunc: func(password string) (string, error) {
			return "hashed-password", nil
		},
	}

	user, err := NewUser(giveUsername, givePassword, giveNickname, giveUserStatus, mockPasswordEncoder)
	assert.NoError(t, err)
	assert.Equal(t, giveUsername, user.Username())
	assert.Equal(t, giveNickname, user.Nickname())
	assert.Equal(t, UserStatusActive, user.Status())
	assert.Equal(t, "hashed-password", string(user.Password()))
}

func TestNewUser_WeakPassword(t *testing.T) {
	giveUsername := "test-username"
	giveNickname := "test-nickname"
	givePassword := "123"
	giveUserStatus := UserStatusActive

	mockPasswordEncoder := &MoqPasswordEncoder{
		EncodeFunc: func(password string) (string, error) {
			return "", ErrWeakPassword
		},
	}

	_, err := NewUser(giveUsername, givePassword, giveNickname, giveUserStatus, mockPasswordEncoder)
	assert.Equal(t, ErrWeakPassword, err)
}

func TestNewUser_UsernameTooShort(t *testing.T) {
	giveUsername := "name" // 短用户名
	giveNickname := "test-nickname"
	givePassword := "test-password"
	giveStatus := UserStatusActive

	mockPasswordEncoder := &MoqPasswordEncoder{
		EncodeFunc: func(password string) (string, error) {
			return "hashed-password", nil
		},
	}

	_, err := NewUser(giveUsername, givePassword, giveNickname, giveStatus, mockPasswordEncoder)
	assert.Equal(t, ErrUsernameTooShort, err)
}

func TestNewUser_CryptFail(t *testing.T) {
	giveUsername := "test-username"
	giveNickname := "test-nickname"
	givePassword := "test-password"
	giveStatus := UserStatusActive

	mockPasswordEncoder := &MoqPasswordEncoder{
		EncodeFunc: func(password string) (string, error) {
			return "", errors.New("crypt password fail")
		},
	}

	_, err := NewUser(giveUsername, givePassword, giveNickname, giveStatus, mockPasswordEncoder)
	assert.ErrorIs(t, ErrCryptPasswordFail, err)
}

func TestReconstructUser(t *testing.T) {
	giveUsername := "test-username"
	giveNickname := "test-nickname"
	givePassword := "hashed-password"
	giveStatus := UserStatusActive

	user := ReconstructUser(uuid.New(), time.Now(), time.Now(), giveUsername, givePassword, giveNickname, giveStatus)
	assert.Equal(t, giveUsername, user.Username())
	assert.Equal(t, giveNickname, user.Nickname())
	assert.Equal(t, giveStatus, user.Status())
	assert.Equal(t, givePassword, string(user.Password()))
}

func TestUser_VerifyPassword_Correct(t *testing.T) {
	mockPasswordEncoder := &MoqPasswordEncoder{
		EncodeFunc: func(password string) (string, error) {
			return "", nil
		},
		VerifyFunc: func(password string, hashedPassword string) error {
			return nil
		},
	}

	user, _ := NewUser("test-username", "test-password", "test-nickname", UserStatusActive, mockPasswordEncoder)
	assert.True(t, user.VerifyPassword("test-password", mockPasswordEncoder))
}

func TestUser_VerifyPassword_Incorrect(t *testing.T) {
	wantErr := errors.New("password incorrect")
	mockPasswordEncoder := &MoqPasswordEncoder{
		EncodeFunc: func(password string) (string, error) {
			return "", nil
		},
		VerifyFunc: func(password string, hashedPassword string) error {
			return wantErr
		},
	}

	user, _ := NewUser("test-username", "test-password", "test-nickname", UserStatusActive, mockPasswordEncoder)
	assert.False(t, user.VerifyPassword("wrong-password", mockPasswordEncoder))
}
