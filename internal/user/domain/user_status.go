package domain

import (
	"database/sql/driver"
	"errors"
)

type UserStatus int

const (
	UserStatusActive UserStatus = iota
	UserStatusInactive
)

func (us UserStatus) String() string {
	switch us {
	case UserStatusInactive:
		return "INACTIVE"
	case UserStatusActive:
		return "ACTIVE"
	default:
		return "ACTIVE"
	}
}

func (us UserStatus) Values() []string {
	return []string{UserStatusInactive.String(), UserStatusActive.String()}
}

func (us UserStatus) Value() (driver.Value, error) {
	return int64(us), nil
}

func (us *UserStatus) Scan(val any) error {
	var s string
	switch v := val.(type) {
	case nil:
		return errors.New("value is nil")
	case string:
		s = v
	case int64:
		s = UserStatus(v).String()
	}

	switch s {
	case UserStatusInactive.String():
		*us = UserStatusInactive
	case UserStatusActive.String():
		*us = UserStatusActive
	default:
		*us = UserStatusActive
	}
	return nil
}

func (us UserStatus) Int() int {
	return int(us)
}
