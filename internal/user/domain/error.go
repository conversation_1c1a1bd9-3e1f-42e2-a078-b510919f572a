package domain

import (
	"resflow/internal/common/errors"
)

var (
	ErrCodeWeakPassword      errors.ErrorCode = "WEAK_PASSWORD"
	ErrCodeUserAlreadyExists errors.ErrorCode = "USER_ALREADY_EXISTS"
	ErrCodeCryptPasswordFail errors.ErrorCode = "CRYPT_PASSWORD_FAIL"
	ErrCodeUsernameTooShort  errors.ErrorCode = "USERNAME_TOO_SHORT"
	ErrCodeFindUserFail      errors.ErrorCode = "FIND_USER_FAIL"
)

var (
	ErrWeakPassword      = errors.NewDomainError(ErrCodeWeakPassword, "密码太弱", nil)
	ErrUserAlreadyExists = errors.NewDomainError(ErrCodeUserAlreadyExists, "用户已存在", nil)
	ErrCryptPasswordFail = errors.NewDomainError(ErrCodeCryptPasswordFail, "密码加密失败", nil)
	ErrUsernameTooShort  = errors.NewDomainError(ErrCodeUsernameTooShort, "用户名太短", nil)
	ErrFindUserFail      = errors.NewDomainError(ErrCodeFindUserFail, "查找用户失败", nil)
)
