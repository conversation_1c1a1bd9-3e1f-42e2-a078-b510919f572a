package query

import (
	"context"
	"resflow/internal/common/errors"
	"resflow/internal/user/application"
	"resflow/internal/user/domain"

	"github.com/google/uuid"
)

type UserQueryService struct {
	repository domain.UserRepository
}

func NewUserQueryService(repository domain.UserRepository) *UserQueryService {
	return &UserQueryService{
		repository: repository,
	}
}

func (s *UserQueryService) GetUser(ctx context.Context, id uuid.UUID) (*application.UserDTO, *errors.AppError) {
	user, err := s.repository.FindById(ctx, id)
	if err != nil {
		return nil, errors.NewAppError(errors.ErrTypeInternal, application.ErrCodeFindUserFail, "查找用户失败", err)
	}
	if user == nil {
		return nil, errors.NewAppError(errors.ErrTypeUser, application.ErrCodeUserNotFound, "用户不存在", err)
	}
	return application.UserModelToDTO(user), nil
}
