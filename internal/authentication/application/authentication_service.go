package application

import (
	"context"
	"resflow/internal/authentication/domain"
	"resflow/internal/common/errors"

	"github.com/google/uuid"
)

type AuthenticationService struct {
	tokenService domain.TokenService
}

func NewAuthenticationService(tokenService domain.TokenService) *AuthenticationService {
	return &AuthenticationService{tokenService: tokenService}
}

func (s *AuthenticationService) GetUserIdByToken(ctx context.Context, token string) (uuid.UUID, error) {
	userId, err := s.tokenService.ParseToken(token)
	if err != nil {
		return uuid.UUID{}, errors.NewAppError(errors.ErrTypeUser, ErrCodeTokenParseFail, "token解析失败", err)
	}

	return userId, nil
}
