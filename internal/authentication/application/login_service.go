package application

import (
	"context"
	authdomain "resflow/internal/authentication/domain"
	"resflow/internal/common/errors"
	"resflow/internal/user/application"
	"resflow/internal/user/domain"
	"resflow/internal/user/infrastructure"
)

type LoginService struct {
	userRepository domain.UserRepository
	tokenService   authdomain.TokenService
}

func NewLoginService(userRepository domain.UserRepository, tokenService authdomain.TokenService) *LoginService {
	return &LoginService{
		userRepository: userRepository,
		tokenService:   tokenService,
	}
}

func (s *LoginService) Login(ctx context.Context, username, password string) (*TokenDTO, *errors.AppError) {
	user, err := s.userRepository.FindByUsername(ctx, username)
	if err != nil {
		return nil, errors.NewAppError(errors.ErrTypeUser, application.ErrCodeUserNotFound, "用户不存在", err)
	}
	if !user.VerifyPassword(password, infrastructure.NewBcryptPasswordEncoder()) {
		return nil, ErrPasswordIncorrect
	}
	token, _, err := s.tokenService.GenerateToken(user)
	if err != nil {
		return nil, errors.NewAppError(errors.ErrTypeInternal, ErrCodeTokenGenerateFail, "生成token失败", err)
	}
	refreshToken, _, err := s.tokenService.GenerateRefreshToken(user)
	if err != nil {
		return nil, errors.NewAppError(errors.ErrTypeInternal, ErrCodeTokenGenerateFail, "生成refresh token失败", err)
	}
	return &TokenDTO{
		Token:        token,
		RefreshToken: refreshToken,
	}, nil
}

func (s *LoginService) RefreshToken(ctx context.Context, refreshToken string) (*TokenDTO, *errors.AppError) {
	userId, err := s.tokenService.ParseRefreshToken(refreshToken)
	if err != nil {
		return nil, errors.NewAppError(errors.ErrTypeUser, ErrCodeTokenParseFail, "token解析失败", err)
	}
	user, err := s.userRepository.FindById(ctx, userId)
	if err != nil {
		return nil, errors.NewAppError(errors.ErrTypeUser, application.ErrCodeUserNotFound, "用户不存在", err)
	}
	accessToken, _, err := s.tokenService.GenerateToken(user)
	if err != nil {
		return nil, errors.NewAppError(errors.ErrTypeInternal, ErrCodeTokenGenerateFail, "生成token失败", err)
	}
	refreshToken, _, err = s.tokenService.GenerateRefreshToken(user)
	if err != nil {
		return nil, errors.NewAppError(errors.ErrTypeInternal, ErrCodeTokenGenerateFail, "生成refresh token失败", err)
	}
	return &TokenDTO{
		Token:        accessToken,
		RefreshToken: refreshToken,
	}, nil
}
