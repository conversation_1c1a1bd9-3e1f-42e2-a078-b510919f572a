package infrastructure

import (
	"resflow/internal/user/domain"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

type JWTTokenService struct {
	secret            string
	expiration        time.Duration
	refreshExpiration time.Duration
	subject           string
}

type TokenType string

const (
	AccessToken  TokenType = "access_token"
	RefreshToken TokenType = "refresh_token"
)

type JWTClaims struct {
	ID       string    `json:"id"`
	Username string    `json:"username"`
	Nickname string    `json:"nickname"`
	Type     TokenType `json:"type"`
	jwt.RegisteredClaims
}

func NewJWTTokenService(secret string, expiration time.Duration, refreshExpiration time.Duration, subject string) *JWTTokenService {
	return &JWTTokenService{
		secret:            secret,
		expiration:        expiration,
		refreshExpiration: refreshExpiration,
		subject:           subject,
	}
}

func (s *JWTTokenService) GenerateToken(user *domain.User) (string, time.Time, error) {
	now := time.Now()
	expiredAt := now.Add(s.expiration)

	claims := &JWTClaims{
		ID:       user.ID().String(),
		Username: user.Username(),
		Nickname: user.Nickname(),
		Type:     AccessToken,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiredAt),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Subject:   s.subject,
		},
	}

	token, err := jwt.NewWithClaims(jwt.SigningMethodHS256, claims).SignedString([]byte(s.secret))
	if err != nil {
		return "", time.Time{}, err
	}
	return token, expiredAt, nil
}

func (s *JWTTokenService) GenerateRefreshToken(user *domain.User) (string, time.Time, error) {
	now := time.Now()
	expiredAt := now.Add(s.refreshExpiration)

	claims := &JWTClaims{
		ID:       user.ID().String(),
		Username: user.Username(),
		Nickname: user.Nickname(),
		Type:     RefreshToken,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiredAt),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Subject:   s.subject,
		},
	}

	token, err := jwt.NewWithClaims(jwt.SigningMethodHS256, claims).SignedString([]byte(s.secret))
	if err != nil {
		return "", time.Time{}, err
	}
	return token, expiredAt, nil
}

func (s *JWTTokenService) ParseToken(token string) (uuid.UUID, error) {
	claims := &JWTClaims{}
	_, err := jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, jwt.ErrHashUnavailable
		}
		return []byte(s.secret), nil
	})
	if err != nil {
		return uuid.UUID{}, err
	}

	if claims.Type != AccessToken {
		return uuid.UUID{}, jwt.ErrInvalidType
	}

	expirationTime, err := claims.GetExpirationTime()
	if err != nil {
		return uuid.UUID{}, jwt.ErrTokenExpired
	}

	if expirationTime.Before(time.Now()) {
		return uuid.UUID{}, jwt.ErrTokenExpired
	}

	userId, err := uuid.Parse(claims.ID)
	if err != nil {
		return uuid.UUID{}, err
	}

	return userId, nil
}

func (s *JWTTokenService) ParseRefreshToken(token string) (uuid.UUID, error) {
	claims := &JWTClaims{}
	_, err := jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, jwt.ErrHashUnavailable
		}
		return []byte(s.secret), nil
	})
	if err != nil {
		return uuid.UUID{}, err
	}

	if claims.Type != RefreshToken {
		return uuid.UUID{}, jwt.ErrInvalidType
	}

	expirationTime, err := claims.GetExpirationTime()
	if err != nil {
		return uuid.UUID{}, jwt.ErrTokenExpired
	}

	if expirationTime.Before(time.Now()) {
		return uuid.UUID{}, jwt.ErrTokenExpired
	}

	userId, err := uuid.Parse(claims.ID)
	if err != nil {
		return uuid.UUID{}, err
	}

	return userId, nil
}
