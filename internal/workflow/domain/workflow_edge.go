package domain

import (
	"errors"
	basedomain "resflow/internal/common/domain"
	"time"

	"github.com/google/uuid"
)

type WorkflowEdge struct {
	basedomain.AggregateRoot
	fromNodeId uuid.UUID
	toNodeId   uuid.UUID
	fromPortId uuid.UUID
	toPortId   uuid.UUID
	_type      string
}

type WorkflowEdges []*WorkflowEdge

var (
	ErrFromNodeIdRequired = errors.New("from node id Required")
	ErrToNodeIdRequired   = errors.New("to node id Required")
	ErrFromPortIdRequired = errors.New("from port id Required")
	ErrToPortIdRequired   = errors.New("to port id Required")
)

func NewWorkflowEdge(fromNodeId, toNodeId, fromPortId, toPortId uuid.UUID, _type string) (*WorkflowEdge, error) {
	if fromNodeId.String() == "" {
		return nil, ErrFromNodeIdRequired
	}

	if toNodeId.String() == "" {
		return nil, ErrToNodeIdRequired
	}

	if fromPortId.String() == "" {
		return nil, ErrFromPortIdRequired
	}

	if toPortId.String() == "" {
		return nil, ErrToPortIdRequired
	}

	return &WorkflowEdge{
		AggregateRoot: basedomain.NewAggregateRoot(uuid.New(), time.Now(), time.Now()),
		fromNodeId:    fromNodeId,
		toNodeId:      toNodeId,
		fromPortId:    fromPortId,
		toPortId:      toPortId,
		_type:         _type,
	}, nil
}

func (e WorkflowEdge) FromNodeId() uuid.UUID {
	return e.fromNodeId
}

func (e WorkflowEdge) ToNodeId() uuid.UUID {
	return e.toNodeId
}

func (e WorkflowEdge) FromPortId() uuid.UUID {
	return e.fromPortId
}

func (e WorkflowEdge) ToPortId() uuid.UUID {
	return e.toPortId
}

func (e WorkflowEdge) Type() string {
	return e._type
}
