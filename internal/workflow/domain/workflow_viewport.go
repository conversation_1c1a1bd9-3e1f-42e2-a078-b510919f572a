package domain

import "github.com/bytedance/sonic"

type workflowViewport struct {
	X    float32 `json:"X"`
	Y    float32 `json:"Y"`
	Zoom float32 `json:"Zoom"`
}

func NewWorkflowViewport(x, y, zoom float32) *workflowViewport {
	return &workflowViewport{
		X:    x,
		Y:    y,
		Zoom: zoom,
	}
}

func NewDefaultWorkflowViewport() *workflowViewport {
	return NewWorkflowViewport(0, 0, 1)
}

func ReconstructWorkflowViewport(jsonStr string) *workflowViewport {
	var viewport *workflowViewport
	err := sonic.Unmarshal([]byte(jsonStr), viewport)
	if err != nil {
		return NewDefaultWorkflowViewport()
	}
	return viewport
}
