package domain

import (
	"github.com/bytedance/sonic"
)

type nodePosition struct {
	X float32 `json:"X"`
	Y float32 `json:"Y"`
}

func NewNodePosition(x, y float32) *nodePosition {
	return &nodePosition{
		X: x,
		Y: y,
	}
}

func NewDefaultNodePosition() *nodePosition {
	return NewNodePosition(0, 0)
}

func NewNodePositionFromJSONString(jsonStr string) (*nodePosition, error) {
	var position *nodePosition
	err := sonic.Unmarshal([]byte(jsonStr), position)
	if err != nil {
		return nil, err
	}
	return position, nil
}
