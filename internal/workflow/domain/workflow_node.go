package domain

import (
	"errors"
	basedomain "resflow/internal/common/domain"
	"time"

	"github.com/bytedance/sonic"
	"github.com/google/uuid"
)

var (
	ErrPositionRequired = errors.New("position Required")
)

type WorkflowNode struct {
	basedomain.AggregateRoot
	name          string
	description   string
	_type         string
	version       string
	icon          Icon
	pluginName    string
	pluginVersion string
	inputParams   NodeParams
	outputParams  NodeParams
	inputPorts    NodePorts
	outputPorts   NodePorts
	position      *nodePosition
	configData    map[string]interface{}
}

type WorkflowNodes []*WorkflowNode

var (
	ErrTypeRequired    = errors.New("type Required")
	ErrVersionRequired = errors.New("version Required")
)

func NewWorkflowNode(name, description, _type, version string, icon Icon, position *nodePosition, pluginName, pluginVersion string, inputParams, outputParams []NodeParam, inputPorts, outputPorts []NodePort, configData map[string]interface{},
) (*WorkflowNode, error) {
	if name == "" {
		return nil, ErrNameRequired
	}

	if _type == "" {
		return nil, ErrTypeRequired
	}

	if version == "" {
		return nil, ErrVersionRequired
	}

	if position == nil {
		return nil, ErrPositionRequired
	}

	return &WorkflowNode{
		AggregateRoot: basedomain.NewAggregateRoot(uuid.New(), time.Now(), time.Now()),
		name:          name,
		description:   description,
		_type:         _type,
		version:       version,
		icon:          icon,
		pluginName:    pluginName,
		pluginVersion: pluginVersion,
		inputParams:   inputParams,
		outputParams:  outputParams,
		inputPorts:    inputPorts,
		outputPorts:   outputPorts,
		position:      position,
		configData:    configData,
	}, nil
}

func ReconstructWorkflowNode(id uuid.UUID, createdAt, updatedAt time.Time, name, description, _type, version string, icon *icon, position *nodePosition, pluginName, pluginVersion string, inputParams, outputParams []NodeParam, inputPorts, outputPorts []NodePort, configData map[string]interface{},
) *WorkflowNode {
	return &WorkflowNode{
		AggregateRoot: basedomain.NewAggregateRoot(id, createdAt, updatedAt),
		name:          name,
		description:   description,
		_type:         _type,
		version:       version,
		icon:          icon,
		pluginName:    pluginName,
		pluginVersion: pluginVersion,
		inputParams:   inputParams,
		outputParams:  outputParams,
		inputPorts:    inputPorts,
		outputPorts:   outputPorts,
		position:      position,
		configData:    configData,
	}
}

func (n WorkflowNode) Name() string {
	return n.name
}

func (n WorkflowNode) Description() string {
	return n.description
}

func (n WorkflowNode) Type() string {
	return n._type
}

func (n WorkflowNode) Version() string {
	return n.version
}

func (n WorkflowNode) PluginName() string {
	return n.pluginName
}

func (n WorkflowNode) PluginVersion() string {
	return n.pluginVersion
}

func (n WorkflowNode) InputParams() NodeParams {
	return n.inputParams
}

func (n WorkflowNode) OutputParams() NodeParams {
	return n.outputParams
}

func (n WorkflowNode) InputPorts() NodePorts {
	return n.inputPorts
}

func (n WorkflowNode) OutputPorts() NodePorts {
	return n.outputPorts
}

func (n WorkflowNode) ConfigData() map[string]interface{} {
	return n.configData
}

func (n WorkflowNode) ConfigDataString() (string, error) {
	jsonBytes, err := sonic.Marshal(n.configData)
	if err != nil {
		return "", err
	}
	return string(jsonBytes), nil
}

func (n WorkflowNode) Icon() Icon {
	return n.icon
}

func (n WorkflowNode) Position() *nodePosition {
	return n.position
}
