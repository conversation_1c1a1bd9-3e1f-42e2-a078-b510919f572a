package domain

import "errors"

type IconType string

var (
	IconTypeText  IconType = "text"
	IconTypeImage IconType = "image"
	IconTypeSVG   IconType = "svg"
)

func (t IconType) String() string {
	return string(t)
}

type Icon interface {
	Type() IconType
	BackgroundColor() string
	IconData() string
}

type icon struct {
	iconType        IconType
	backgroundColor string
	text            string
	imageUrl        string
	svg             string
}

const WhiteColor = "#ffffff"

var (
	ErrTextTooLong      = errors.New("text too long")
	ErrImageUrlRequired = errors.New("image url Required")
	ErrSVGRequired      = errors.New("svg Required")
)

func NewTextIcon(text string, backgroundColor string) (*icon, error) {
	if len(text) > 1 {
		return nil, ErrTextTooLong
	}

	if backgroundColor == "" {
		backgroundColor = WhiteColor
	}

	return &icon{
		iconType:        IconTypeText,
		text:            text,
		backgroundColor: backgroundColor,
	}, nil
}

func NewImageIcon(url string) (*icon, error) {
	if url == "" {
		return nil, ErrImageUrlRequired
	}

	return &icon{
		iconType: IconTypeImage,
		imageUrl: url,
	}, nil
}

func NewSVGIcon(svg string, backgroundColor string) (*icon, error) {
	if svg == "" {
		return nil, ErrSVGRequired
	}

	if backgroundColor == "" {
		backgroundColor = WhiteColor
	}

	return &icon{
		iconType:        IconTypeSVG,
		svg:             svg,
		backgroundColor: backgroundColor,
	}, nil
}

func ReconstructIcon(iconType IconType, backgroundColor, iconData string) *icon {
	text, imageUrl, svg := "", "", ""
	switch iconType {
	case IconTypeText:
		text = iconData
	case IconTypeImage:
		imageUrl = iconData
	case IconTypeSVG:
		svg = iconData
	}
	return &icon{
		iconType:        iconType,
		backgroundColor: backgroundColor,
		text:            text,
		imageUrl:        imageUrl,
		svg:             svg,
	}
}

func (i *icon) Type() IconType {
	return i.iconType
}

func (i *icon) BackgroundColor() string {
	return i.backgroundColor
}

func (i *icon) IconData() string {
	switch i.iconType {
	case IconTypeText:
		return i.text
	case IconTypeImage:
		return i.imageUrl
	case IconTypeSVG:
		return i.svg
	default:
		return ""
	}
}
