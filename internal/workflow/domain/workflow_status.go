package domain

import (
	"database/sql/driver"
	"errors"
)

type WorkflowStatus int

const (
	WorkflowStatusActive WorkflowStatus = iota
	WorkflowStatusInactive
)

func (ws WorkflowStatus) String() string {
	switch ws {
	case WorkflowStatusInactive:
		return "INACTIVE"
	case WorkflowStatusActive:
		return "ACTIVE"
	default:
		return "ACTIVE"
	}
}

func (ws WorkflowStatus) Values() []string {
	return []string{WorkflowStatusInactive.String(), WorkflowStatusActive.String()}
}

func (ws WorkflowStatus) Value() (driver.Value, error) {
	return int64(ws), nil
}

func (ws *WorkflowStatus) Scan(val any) error {
	var s string
	switch t := val.(type) {
	case nil:
		return errors.New("workflow status is nil")
	case string:
		s = t
	case int64:
		s = WorkflowStatus(t).String()
	}

	switch s {
	case WorkflowStatusInactive.String():
		*ws = WorkflowStatusInactive
	case WorkflowStatusActive.String():
		*ws = WorkflowStatusActive
	default:
		*ws = WorkflowStatusActive
	}
	return nil
}

func (ws WorkflowStatus) Int() int {
	return int(ws)
}
