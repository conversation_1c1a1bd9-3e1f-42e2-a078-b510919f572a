package domain

import "github.com/bytedance/sonic"

type PortType string

const (
	PortTypeSource PortType = "source"
	PortTypeTarget PortType = "target"
)

type NodePort interface {
}

type nodePort struct {
	Id   string   `json:"id"`
	Type PortType `json:"type"`
}

type NodePorts []NodePort

func NewNodePort(id string, _type PortType) (*nodePort, error) {
	if id == "" {
		return nil, ErrIDRequired
	}

	return &nodePort{
		Id:   id,
		Type: _type,
	}, nil
}

func NewNodePortsFromJSONString(jsonStr string) ([]*nodePort, error) {
	var port []*nodePort
	err := sonic.Unmarshal([]byte(jsonStr), port)
	if err != nil {
		return nil, err
	}
	return port, nil
}
