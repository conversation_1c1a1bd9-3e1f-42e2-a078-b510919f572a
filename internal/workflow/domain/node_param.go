package domain

import "github.com/bytedance/sonic"

type ParamType string

const (
	ParamTypeString  ParamType = "string"
	ParamTypeNumber  ParamType = "number"
	ParamTypeBoolean ParamType = "boolean"
	ParamTypeObject  ParamType = "object"
	ParamTypeArray   ParamType = "array"
)

type NodeParam interface {
	IsRequired() bool
}

type nodeParam struct {
	Id          string      `json:"id"`
	Label       string      `json:"label"`
	Description string      `json:"description,omitempty"`
	Type        ParamType   `json:"type"`
	Required    bool        `json:"required"`
	Value       interface{} `json:"value,omitempty"`
}

type NodeParams []NodeParam

func NewNodeParam(id, label, description string, _type ParamType, required bool, value interface{}) (*nodeParam, error) {
	if id == "" {
		return nil, ErrIDRequired
	}

	return &nodeParam{
		Id:          id,
		Label:       label,
		Description: description,
		Type:        _type,
		Required:    required,
		Value:       value,
	}, nil
}

func NewNodeParamsFromJSONString(jsonStr string) (NodeParams, error) {
	var params NodeParams
	err := sonic.Unmarshal([]byte(jsonStr), &params)
	if err != nil {
		return nil, err
	}
	return params, nil
}

func (p nodeParam) IsRequired() bool {
	return p.Required
}
