package infrastructure

import (
	"context"
	"resflow/ent"
	"resflow/ent/enttest"
	"resflow/ent/workflow"
	"resflow/internal/common/infrastructure"
	"resflow/internal/workflow/domain"
	"testing"

	"entgo.io/ent/dialect/sql/schema"
	"github.com/bytedance/sonic"
	"github.com/google/uuid"
	_ "github.com/mattn/go-sqlite3"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func createEntTestClient(t *testing.T) *ent.Client {
	opts := []enttest.Option{
		enttest.WithOptions(ent.Log(t.Log)),
	}
	client := enttest.Open(t, "sqlite3",
		"file:ent?mode=memory&cache=shared&_fk=1", opts...)
	if err := client.Schema.Create(context.Background(), schema.WithGlobalUniqueID(true)); err != nil {
		t.Fatalf("failed creating schema resources: %v", err)
	}
	return client
}

func createTestUser(t *testing.T, client *ent.Client) *ent.User {
	return client.User.Create().
		SetID(uuid.New()).
		SetUsername("test").
		SetPassword("test").
		SetNickname("test").
		SetStatus(1).
		SaveX(t.Context())
}

func createTestIcon() domain.Icon {
	icon, _ := domain.NewTextIcon("T", domain.WhiteColor)
	return icon
}

func createNodeParam() domain.NodeParam {
	param, _ := domain.NewNodeParam("test-node-param", "test", "test", domain.ParamTypeString, true, "test")
	return param
}

func createNodePort() domain.NodePort {
	port, _ := domain.NewNodePort("test-port", domain.PortTypeSource)
	return port
}

func createWorkflowNode(name, _type, version string) *domain.WorkflowNode {
	inputParams := make(domain.NodeParams, 0, 1)
	inputParams = append(inputParams, createNodeParam())
	outputParams := make(domain.NodeParams, 0, 1)
	outputParams = append(outputParams, createNodeParam())
	inputPorts := make(domain.NodePorts, 0, 1)
	inputPorts = append(inputPorts, createNodePort())
	outputPorts := make(domain.NodePorts, 0, 1)
	outputPorts = append(outputPorts, createNodePort())

	node, _ := domain.NewWorkflowNode(name, "test", _type, version, createTestIcon(), domain.NewDefaultNodePosition(), "test", "test", inputParams, outputParams, inputPorts, outputPorts, map[string]interface{}{"test": "test"})
	return node
}

func createWorkflowEdge(fromNodeId, toNodeId, fromPortId, toPortId uuid.UUID) *domain.WorkflowEdge {
	edge, _ := domain.NewWorkflowEdge(fromNodeId, toNodeId, fromPortId, toPortId, "test")
	return edge
}

func TestEntWorkflowStore_Save(t *testing.T) {
	client := createEntTestClient(t)
	user := createTestUser(t, client)

	nodes := make(domain.WorkflowNodes, 0, 1)
	nodes = append(nodes, createWorkflowNode("test", "test", "test"))
	edges := make(domain.WorkflowEdges, 0, 1)
	edges = append(edges, createWorkflowEdge(uuid.New(), uuid.New(), uuid.New(), uuid.New()))

	newWorkflow, err := domain.NewWorkflow(
		"test", "test", domain.WorkflowStatusActive, user.ID, createTestIcon(), domain.NewDefaultWorkflowViewport(), nodes, edges,
	)
	require.NoError(t, err)

	transactionMgr := infrastructure.NewEntTransactionManager(client)
	workflowStore := NewEntWorkflowStore(client)

	err = transactionMgr.WithTx(t.Context(), func(ctx context.Context) error {
		return workflowStore.Save(ctx, newWorkflow)
	})
	require.NoError(t, err)
	foundWorkflow, err := client.Workflow.Query().WithNodes().WithWorkflowEdges().Where(workflow.ID(newWorkflow.ID())).First(t.Context())
	assert.NoError(t, err)
	assert.Equal(t, newWorkflow.ID(), foundWorkflow.ID)
	assert.Equal(t, newWorkflow.Name(), foundWorkflow.Name)
	assert.Equal(t, newWorkflow.Description(), foundWorkflow.Description)
	assert.Equal(t, newWorkflow.Status().Int(), foundWorkflow.Status)
	assert.Equal(t, newWorkflow.UserId(), foundWorkflow.UserID)
	assert.Equal(t, string(newWorkflow.Icon().Type()), foundWorkflow.IconType)
	assert.Equal(t, newWorkflow.Icon().BackgroundColor(), foundWorkflow.IconBgColor)
	assert.Equal(t, newWorkflow.Icon().IconData(), foundWorkflow.IconData)
	wantViewport, _ := sonic.Marshal(newWorkflow.Viewport())
	assert.Equal(t, string(wantViewport), foundWorkflow.Viewport)
	assert.Equal(t, newWorkflow.CreatedAt().Unix(), foundWorkflow.CreatedAt.Unix())
	assert.Equal(t, newWorkflow.UpdatedAt().Unix(), foundWorkflow.UpdatedAt.Unix())

	// 比较节点
	assert.Equal(t, len(newWorkflow.Nodes()), len(foundWorkflow.Edges.Nodes))
	exceptedNode := newWorkflow.Nodes()[0]
	actualNode := foundWorkflow.Edges.Nodes[0]
	assert.Equal(t, exceptedNode.ID(), actualNode.ID)
	assert.Equal(t, exceptedNode.Name(), actualNode.Name)
	assert.Equal(t, exceptedNode.Description(), actualNode.Description)
	assert.Equal(t, exceptedNode.Type(), actualNode.Type)
	assert.Equal(t, exceptedNode.Version(), actualNode.Version)
	assert.Equal(t, exceptedNode.PluginName(), actualNode.PluginName)
	assert.Equal(t, exceptedNode.PluginVersion(), actualNode.PluginVersion)
	assert.Equal(t, exceptedNode.Icon().Type().String(), actualNode.IconType)
	assert.Equal(t, exceptedNode.Icon().BackgroundColor(), actualNode.IconBgColor)
	assert.Equal(t, exceptedNode.Icon().IconData(), actualNode.IconData)
	wantInputParams, _ := sonic.Marshal(exceptedNode.InputParams())
	assert.Equal(t, string(wantInputParams), actualNode.InputParams)
	wantOutputParams, _ := sonic.Marshal(exceptedNode.OutputParams())
	assert.Equal(t, string(wantOutputParams), actualNode.OutputParams)
	wantInputPorts, _ := sonic.Marshal(exceptedNode.InputPorts())
	assert.Equal(t, string(wantInputPorts), actualNode.InputPorts)
	wantOutputPorts, _ := sonic.Marshal(exceptedNode.OutputPorts())
	assert.Equal(t, string(wantOutputPorts), actualNode.OutputPorts)
	wantPosition, _ := sonic.Marshal(exceptedNode.Position())
	assert.Equal(t, string(wantPosition), actualNode.Position)
	wantConfigData, _ := sonic.Marshal(exceptedNode.ConfigData())
	assert.Equal(t, string(wantConfigData), actualNode.ConfigData)

	assert.Equal(t, exceptedNode.CreatedAt().Unix(), actualNode.CreatedAt.Unix())
	assert.Equal(t, exceptedNode.UpdatedAt().Unix(), actualNode.UpdatedAt.Unix())

	// 比较边
	assert.Equal(t, len(newWorkflow.Edges()), len(foundWorkflow.Edges.WorkflowEdges))
	exceptedEdge := newWorkflow.Edges()[0]
	actualEdge := foundWorkflow.Edges.WorkflowEdges[0]
	assert.Equal(t, exceptedEdge.ID(), actualEdge.ID)
	assert.Equal(t, exceptedEdge.FromNodeId(), actualEdge.FromNodeID)
	assert.Equal(t, exceptedEdge.ToNodeId(), actualEdge.ToNodeID)
	assert.Equal(t, exceptedEdge.FromPortId(), actualEdge.FromPortID)
	assert.Equal(t, exceptedEdge.ToPortId(), actualEdge.ToPortID)
	assert.Equal(t, exceptedEdge.Type(), actualEdge.Type)
	assert.Equal(t, exceptedEdge.CreatedAt().Unix(), actualEdge.CreatedAt.Unix())
	assert.Equal(t, exceptedEdge.UpdatedAt().Unix(), actualEdge.UpdatedAt.Unix())
}
