package infrastructure

import (
	"context"
	"resflow/ent"
	"resflow/ent/workflow"
	"resflow/ent/workflownode"
	"resflow/ent/workflowsedge"
	"resflow/internal/workflow/domain"

	"github.com/bytedance/sonic"
	"github.com/google/uuid"
)

type EntWorkflowStore struct {
	client *ent.Client
}

func NewEntWorkflowStore(client *ent.Client) *EntWorkflowStore {
	return &EntWorkflowStore{client: client}
}

func (s EntWorkflowStore) Save(ctx context.Context, workflowEntity *domain.Workflow) error {
	// 判断是否存在workflow
	workflowExists, err := ent.TxFromContext(ctx).Workflow.Query().Where(workflow.ID(workflowEntity.ID())).First(ctx)
	if err != nil && !ent.IsNotFound(err) {
		return err
	}

	icon := workflowEntity.Icon()
	viewportJSON, err := sonic.Marshal(workflowEntity.Viewport())
	if err != nil {
		return err
	}

	if workflowExists == nil {
		_, err = ent.
			TxFromContext(ctx).Workflow.Create().
			SetID(workflowEntity.ID()).
			SetName(workflowEntity.Name()).
			SetDescription(workflowEntity.Description()).
			SetStatus(workflowEntity.Status().Int()).
			SetUserID(workflowEntity.UserId()).
			SetIconType(string(icon.Type())).
			SetIconBgColor(icon.BackgroundColor()).
			SetIconData(icon.IconData()).
			SetViewport(string(viewportJSON)).
			SetCreatedAt(workflowEntity.CreatedAt()).
			SetUpdatedAt(workflowEntity.UpdatedAt()).
			Save(ctx)
		if err != nil {
			return err
		}
	} else {
		_, err = ent.TxFromContext(ctx).Workflow.Update().Where(workflow.ID(workflowEntity.ID())).
			SetName(workflowEntity.Name()).
			SetDescription(workflowEntity.Description()).
			SetStatus(workflowEntity.Status().Int()).
			SetUserID(workflowEntity.UserId()).
			SetIconType(string(icon.Type())).
			SetIconBgColor(icon.BackgroundColor()).
			SetIconData(icon.IconData()).
			SetViewport(string(viewportJSON)).
			SetUpdatedAt(workflowEntity.UpdatedAt()).
			Save(ctx)
		if err != nil {
			return err
		}
	}

	// 处理节点
	for _, workflowNodeEntity := range workflowEntity.Nodes() {
		// 判断节点是否存在
		nodeExists, err := ent.TxFromContext(ctx).WorkflowNode.Query().Where(workflownode.ID(workflowNodeEntity.ID())).First(ctx)
		if err != nil && !ent.IsNotFound(err) {
			return err
		}

		nodeIcon := workflowNodeEntity.Icon()
		inputParamsJSON, err := sonic.Marshal(workflowNodeEntity.InputParams())
		if err != nil {
			return err
		}
		outputParamsJSON, err := sonic.Marshal(workflowNodeEntity.OutputParams())
		if err != nil {
			return err
		}
		inputPortsJSON, err := sonic.Marshal(workflowNodeEntity.InputPorts())
		if err != nil {
			return err
		}
		outputPortsJSON, err := sonic.Marshal(workflowNodeEntity.OutputPorts())
		if err != nil {
			return err
		}
		positionJSON, err := sonic.Marshal(workflowNodeEntity.Position())
		if err != nil {
			return err
		}
		configDataJSON, err := workflowNodeEntity.ConfigDataString()
		if err != nil {
			return err
		}

		if nodeExists == nil {
			_, err := ent.TxFromContext(ctx).WorkflowNode.Create().
				SetID(workflowNodeEntity.ID()).
				SetWorkflowID(workflowEntity.ID()).
				SetName(workflowNodeEntity.Name()).
				SetDescription(workflowNodeEntity.Description()).
				SetIconType(nodeIcon.Type().String()).
				SetIconBgColor(nodeIcon.BackgroundColor()).
				SetIconData(nodeIcon.IconData()).
				SetType(workflowNodeEntity.Type()).
				SetVersion(workflowNodeEntity.Version()).
				SetPluginName(workflowNodeEntity.PluginName()).
				SetPluginVersion(workflowNodeEntity.PluginVersion()).
				SetInputParams(string(inputParamsJSON)).
				SetOutputParams(string(outputParamsJSON)).
				SetInputPorts(string(inputPortsJSON)).
				SetOutputPorts(string(outputPortsJSON)).
				SetPosition(string(positionJSON)).
				SetConfigData(configDataJSON).
				SetCreatedAt(workflowNodeEntity.CreatedAt()).
				SetUpdatedAt(workflowNodeEntity.UpdatedAt()).
				Save(ctx)
			if err != nil {
				return err
			}
		} else {
			_, err := ent.TxFromContext(ctx).WorkflowNode.Update().Where(workflownode.ID(workflowNodeEntity.ID())).
				SetName(workflowNodeEntity.Name()).
				SetDescription(workflowNodeEntity.Description()).
				SetIconType(nodeIcon.Type().String()).
				SetIconBgColor(nodeIcon.BackgroundColor()).
				SetIconData(nodeIcon.IconData()).
				SetType(workflowNodeEntity.Type()).
				SetVersion(workflowNodeEntity.Version()).
				SetPluginName(workflowNodeEntity.PluginName()).
				SetPluginVersion(workflowNodeEntity.PluginVersion()).
				SetInputParams(string(inputParamsJSON)).
				SetOutputParams(string(outputParamsJSON)).
				SetInputPorts(string(inputPortsJSON)).
				SetOutputPorts(string(outputPortsJSON)).
				SetPosition(string(positionJSON)).
				SetConfigData(configDataJSON).
				SetUpdatedAt(workflowNodeEntity.UpdatedAt()).
				Save(ctx)
			if err != nil {
				return err
			}
		}
	}

	// 处理边
	for _, workflowEdgeEntity := range workflowEntity.Edges() {
		// 判断边是否存在
		edgeExists, err := ent.TxFromContext(ctx).WorkflowsEdge.Query().Where(workflowsedge.ID(workflowEdgeEntity.ID())).First(ctx)
		if err != nil && !ent.IsNotFound(err) {
			return err
		}

		if edgeExists == nil {
			_, err := ent.TxFromContext(ctx).WorkflowsEdge.Create().
				SetID(workflowEdgeEntity.ID()).
				SetWorkflowID(workflowEntity.ID()).
				SetFromNodeID(workflowEdgeEntity.FromNodeId()).
				SetToNodeID(workflowEdgeEntity.ToNodeId()).
				SetFromPortID(workflowEdgeEntity.FromPortId()).
				SetToPortID(workflowEdgeEntity.ToPortId()).
				SetType(workflowEdgeEntity.Type()).
				SetCreatedAt(workflowEdgeEntity.CreatedAt()).
				SetUpdatedAt(workflowEdgeEntity.UpdatedAt()).
				Save(ctx)
			if err != nil {
				return err
			}
		} else {
			_, err := ent.TxFromContext(ctx).WorkflowsEdge.Update().Where(workflowsedge.ID(workflowEdgeEntity.ID())).
				SetWorkflowID(workflowEntity.ID()).
				SetFromNodeID(workflowEdgeEntity.FromNodeId()).
				SetToNodeID(workflowEdgeEntity.ToNodeId()).
				SetFromPortID(workflowEdgeEntity.FromPortId()).
				SetToPortID(workflowEdgeEntity.ToPortId()).
				SetType(workflowEdgeEntity.Type()).
				SetUpdatedAt(workflowEdgeEntity.UpdatedAt()).
				Save(ctx)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (s EntWorkflowStore) Find(ctx context.Context, id uuid.UUID) (*domain.Workflow, error) {
	workflowDO, err := s.client.Workflow.Query().WithNodes().WithWorkflowEdges().Where(workflow.ID(id)).First(ctx)
	if err != nil && !ent.IsNotFound(err) {
		return nil, err
	}
	if workflowDO == nil {
		return nil, nil
	}

	icon := domain.ReconstructIcon(domain.IconType(workflowDO.IconType), workflowDO.IconBgColor, workflowDO.IconData)

	viewport := domain.ReconstructWorkflowViewport(workflowDO.Viewport)

	nodes := make([]*domain.WorkflowNode, 0, len(workflowDO.Edges.Nodes))
	for _, nodeDO := range workflowDO.Edges.Nodes {
		workflowNode, err := entWorkflowNodeToDomain(nodeDO)
		if err != nil {
			return nil, err
		}
		nodes = append(nodes, workflowNode)
	}
	edges := make([]*domain.WorkflowEdge, 0, len(workflowDO.Edges.WorkflowEdges))
	for _, edgeDO := range workflowDO.Edges.WorkflowEdges {
		workflowEdge, err := entWorkflowEdgeToDomain(edgeDO)
		if err != nil {
			return nil, err
		}
		edges = append(edges, workflowEdge)
	}
	return domain.ReconstructWorkflow(
		workflowDO.ID,
		workflowDO.CreatedAt,
		workflowDO.UpdatedAt,
		workflowDO.Name,
		workflowDO.Description,
		workflowDO.Status,
		workflowDO.UserID,
		icon,
		viewport,
		nodes,
		edges,
	), nil
}

func (s EntWorkflowStore) Remove(ctx context.Context, id uuid.UUID) error {
	_, err := ent.TxFromContext(ctx).WorkflowNode.Delete().Where(workflownode.WorkflowID(id)).Exec(ctx)
	if err != nil {
		return err
	}
	_, err = ent.TxFromContext(ctx).WorkflowsEdge.Delete().Where(workflowsedge.WorkflowID(id)).Exec(ctx)
	if err != nil {
		return err
	}
	_, err = ent.TxFromContext(ctx).Workflow.Delete().Where(workflow.ID(id)).Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}

func entWorkflowNodeToDomain(nodeDO *ent.WorkflowNode) (*domain.WorkflowNode, error) {
	icon := domain.ReconstructIcon(domain.IconType(nodeDO.IconType), nodeDO.IconBgColor, nodeDO.IconData)

	nodePosition, err := domain.ReconstructNodePosition(nodeDO.Position)
	if err != nil {
		return nil, err
	}

	inputParams, err := domain.ReconstructNodeParams(nodeDO.InputParams)
	if err != nil {
		return nil, err
	}
	outputParams, err := domain.ReconstructNodeParams(nodeDO.OutputParams)
	if err != nil {
		return nil, err
	}
	inputPorts, err := domain.ReconstructNodePorts(nodeDO.InputPorts)
	if err != nil {
		return nil, err
	}
	outputPorts, err := domain.ReconstructNodePorts(nodeDO.OutputPorts)
	if err != nil {
		return nil, err
	}
	configData := make(map[string]interface{})
	if nodeDO.ConfigData != "" {
		err = sonic.Unmarshal([]byte(nodeDO.ConfigData), &configData)
		if err != nil {
			return nil, err
		}
	}

	return domain.ReconstructWorkflowNode(
		nodeDO.ID,
		nodeDO.CreatedAt,
		nodeDO.UpdatedAt,
		nodeDO.Name,
		nodeDO.Description,
		nodeDO.Type,
		nodeDO.Version,
		icon,
		nodePosition,
		nodeDO.PluginName,
		nodeDO.PluginVersion,
		inputParams,
		outputParams,
		inputPorts,
		outputPorts,
		configData,
	), nil
}

func entWorkflowEdgeToDomain(edgeDO *ent.WorkflowsEdge) (*domain.WorkflowEdge, error) {
	return domain.NewWorkflowEdge(
		edgeDO.FromNodeID,
		edgeDO.ToNodeID,
		edgeDO.FromPortID,
		edgeDO.ToPortID,
		edgeDO.Type,
	)
}
