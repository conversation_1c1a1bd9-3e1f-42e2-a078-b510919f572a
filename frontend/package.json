{"name": "resflow-frontend", "private": true, "version": "0.0.0", "type": "module", "packageManager": "pnpm@10.14.0", "engines": {"node": ">=20", "pnpm": ">=10"}, "scripts": {"dev": "turbo run dev --parallel", "build": "turbo run build", "test": "turbo run test", "clean": "turbo run clean", "coverage": "turbo run coverage", "lint": "turbo run lint", "prepare": "husky"}, "devDependencies": {"@eslint/js": "^9.22.0", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "3.5.3", "turbo": "^2.5.6", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}