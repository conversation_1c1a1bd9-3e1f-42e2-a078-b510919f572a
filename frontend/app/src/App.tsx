import { StrictMode } from "react";
import "./App.css";
import { RouterProvider } from "react-router";
import router from "@/router.tsx";
import { Toaster } from "@/components/ui/sonner.tsx";
import { ThemeProvider } from "@/contexts/theme-context.tsx";
import { createContainer } from "@/container.ts";

// eslint-disable-next-line react-refresh/only-export-components
function App() {
  return (
    <>
      <StrictMode>
        <ThemeProvider defaultTheme="system" storageKey="reflow-theme">
          <RouterProvider router={router} />
          <Toaster />
        </ThemeProvider>
      </StrictMode>
    </>
  );
}

const container = createContainer(App);
export default container;
