import { AppWindowMac, Workflow } from "lucide-react";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar.tsx";
import { Badge } from "@/components/ui/badge.tsx";
import { Link, useLocation } from "react-router";

export function NavWorkflow() {
  const location = useLocation();
  return (
    <SidebarGroup>
      <SidebarGroupLabel>工作流</SidebarGroupLabel>
      <SidebarMenu>
        <SidebarMenuItem key="工作流">
          <SidebarMenuButton
            tooltip="所有工作流"
            asChild
            isActive={location.pathname === "/workflow/index"}
          >
            <Link to="/workflow/index">
              <Workflow />
              <span>所有工作流</span>
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
        <SidebarMenuItem key="正在执行">
          <SidebarMenuButton
            tooltip="正在执行"
            asChild
            isActive={location.pathname === "/workflow/running"}
          >
            <Link to="/workflow/running">
              <AppWindowMac />
              <span>正在执行</span>
              <Badge>12</Badge>
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroup>
  );
}
