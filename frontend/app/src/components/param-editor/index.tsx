import {
  InitialConfigType,
  LexicalComposer,
} from "@lexical/react/LexicalComposer";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { $getRoot, EditorState, LexicalNode } from "lexical";
import { OnChangePlugin } from "@lexical/react/LexicalOnChangePlugin";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import {
  ParamPlugin,
  ParamType,
} from "@/components/param-editor/param-plugin/index.tsx";
import { ParamNode } from "@/components/param-editor/param-plugin/node.tsx";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";

const placeholder = '输入"/"选择参数';

const onError = (error: Error) => {
  console.log(error);
};

interface ParamEditorProps {
  editorState?: string;
  onChange?: (v: string) => void;
  params?: ParamType[];
}

export function ParamEditor({
  editorState,
  onChange,
  params = [],
}: ParamEditorProps) {
  const initialConfig: InitialConfigType = {
    namespace: "resflow",
    editorState,
    nodes: [ParamNode],
    onError,
  };

  const handleChange = (editorState: EditorState) => {
    editorState.read(() => {
      onChange?.(
        $getRoot()
          .getChildren()
          .map((child: LexicalNode) => child.getTextContent())
          .join("\n"),
      );
    });
  };

  return (
    <div className="relative">
      <LexicalComposer initialConfig={initialConfig}>
        <RichTextPlugin
          contentEditable={
            <ContentEditable
              className="outline-none text-sm"
              aria-placeholder={placeholder}
              placeholder={
                <div className="absolute left-0 top-0 pointer-events-none text-sm text-muted-foreground">
                  {placeholder}
                </div>
              }
            />
          }
          ErrorBoundary={LexicalErrorBoundary}
        />
        <HistoryPlugin />
        <OnChangePlugin onChange={handleChange} />
        <ParamPlugin params={params} />
      </LexicalComposer>
    </div>
  );
}
