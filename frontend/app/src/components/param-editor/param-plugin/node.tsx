import {
  CLICK_COMMAND,
  COMMAND_PRIORITY_LOW,
  DecoratorNode,
  EditorConfig,
  LexicalEditor,
  LexicalNode,
  NodeKey,
  SerializedLexicalNode,
  Spread,
} from "lexical";
import React, { JSX, useEffect } from "react";
import { useLexicalNodeSelection } from "@lexical/react/useLexicalNodeSelection";
import { cn } from "@/lib/utils.ts";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { mergeRegister } from "@lexical/utils";

export type SerializedParamNode = Spread<
  {
    nodeId: string;
    nodeName: string;
    paramId: string;
    paramName: string;
  },
  SerializedLexicalNode
>;

export class ParamNode extends DecoratorNode<JSX.Element> {
  __nodeId: string;
  __nodeName: string;
  __paramId: string;
  __paramName: string;

  static getType(): string {
    return "param";
  }

  static clone(node: ParamNode): ParamNode {
    return new ParamNode(
      node.__nodeId,
      node.__nodeName,
      node.__paramId,
      node.__paramName,
      node.__key,
    );
  }

  static importJSON(serializedNode: SerializedParamNode): ParamNode {
    const { nodeId, nodeName, paramId, paramName } = serializedNode;
    return new ParamNode(nodeId, nodeName, paramId, paramName);
  }

  exportJSON(): SerializedParamNode {
    return {
      ...super.exportJSON(),
      type: "param",
      version: 1,
      nodeId: this.__nodeId,
      nodeName: this.__nodeName,
      paramId: this.__paramId,
      paramName: this.__paramName,
    };
  }

  constructor(
    nodeId: string,
    nodeName: string,
    paramId: string,
    paramName: string,
    key?: NodeKey,
  ) {
    super(key);
    this.__nodeId = nodeId;
    this.__nodeName = nodeName;
    this.__paramId = paramId;
    this.__paramName = paramName;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  createDOM(_config: EditorConfig, _editor: LexicalEditor): HTMLElement {
    return document.createElement("span");
  }

  updateDOM() {
    return false;
  }

  setNodeId(nodeId: string) {
    this.__nodeId = nodeId;
  }

  setNodeName(nodeName: string) {
    this.__nodeName = nodeName;
  }

  setParamId(paramId: string) {
    this.__paramId = paramId;
  }

  setParamName(paramName: string) {
    this.__paramName = paramName;
  }

  getTextContent(): string {
    return `{{${this.__nodeId}.${this.__paramId}}}`;
  }

  decorate(): React.JSX.Element {
    return (
      <ParamNodeComponent
        nodeKey={this.__key}
        nodeName={this.__nodeName}
        paramName={this.__paramName}
      />
    );
  }
}

function ParamNodeComponent({
  nodeName,
  paramName,
  nodeKey,
}: {
  nodeKey: NodeKey;
  nodeName: string;
  paramName: string;
}) {
  const [editor] = useLexicalComposerContext();
  const [isSelected, setSelected, clearSelected] =
    useLexicalNodeSelection(nodeKey);

  useEffect(() => {
    return mergeRegister(
      editor.registerCommand(
        CLICK_COMMAND,
        (event: MouseEvent) => {
          const node = editor.getElementByKey(nodeKey);

          if (event.target === node?.firstChild) {
            if (!event.shiftKey) {
              clearSelected();
            }
            setSelected(!isSelected);
            return true;
          }

          return false;
        },
        COMMAND_PRIORITY_LOW,
      ),
    );
  }, [clearSelected, editor, isSelected, nodeKey, setSelected]);

  return (
    <div
      className={cn(
        "inline-block select-none border rounded px-2 py-1 mx-0.5",
        isSelected && "border-blue-500 border inset-1",
      )}
    >
      {nodeName}
      {"->"}
      {paramName}
    </div>
  );
}

export function $createParamNode(
  nodeId: string,
  nodeName: string,
  paramId: string,
  paramName: string,
): ParamNode {
  return new ParamNode(nodeId, nodeName, paramId, paramName);
}

export function $isParamNode(
  node: LexicalNode | null | undefined,
): node is ParamNode {
  return node instanceof ParamNode;
}
