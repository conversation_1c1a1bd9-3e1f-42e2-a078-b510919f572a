import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import {
  LexicalTypeaheadMenuPlugin,
  MenuOption,
  PUNCTUATION,
} from "@lexical/react/LexicalTypeaheadMenuPlugin";
import React, { JSX, useCallback, useEffect, useMemo, useState } from "react";
import "./style.css";
import {
  $createParagraphNode,
  $insertNodes,
  $isRootOrShadowRoot,
  COMMAND_PRIORITY_HIGH,
  createCommand,
  LexicalCommand,
  TextNode,
} from "lexical";
import ReactDOM from "react-dom";
import {
  $createParamNode,
  ParamNode,
} from "@/components/param-editor/param-plugin/node.tsx";
import { $wrapNodeInElement } from "@lexical/utils";

export interface ParamType {
  nodeId: string;
  nodeName: string;
  paramId: string;
  paramName: string;
}

export const INSERT_PARAM_COMMAND: LexicalCommand<ParamType> = createCommand(
  "INSERT_PARAM_COMMAND",
);

class ParamPickerOption extends MenuOption {
  nodeId: string;
  nodeName: string;
  paramId: string;
  paramName: string;

  onSelect: (querystring: string) => void;

  constructor(
    nodeId: string,
    nodeName: string,
    paramId: string,
    paramName: string,
    onSelect: (querystring: string) => void,
  ) {
    super(nodeId + "." + paramId);
    this.nodeId = nodeId;
    this.nodeName = nodeName;
    this.paramId = paramId;
    this.paramName = paramName;
    this.onSelect = onSelect.bind(this);
  }
}

function ParamPickerMenuItem({
  index,
  isSelected,
  onClick,
  onMouseEnter,
  option,
}: {
  index: number;
  isSelected: boolean;
  onClick: () => void;
  onMouseEnter: () => void;
  option: ParamPickerOption;
}) {
  let className = "item";
  if (isSelected) {
    className += " selected";
  }
  return (
    <li
      key={option.key}
      tabIndex={-1}
      className={className}
      ref={option.setRefElement}
      role="option"
      aria-selected={isSelected}
      id={"typeahead-item-" + index}
      onMouseEnter={onMouseEnter}
      onClick={onClick}
    >
      <span className="text">
        {option.nodeName}
        {"->"}
        {option.paramName}
      </span>
    </li>
  );
}

function useBasicTypeaheadTriggerMatch(
  trigger: string,
  {
    minLength = 1,
    maxLength = 75,
    punctuation = PUNCTUATION,
    allowWhitespace = false,
  },
) {
  return React.useCallback(
    (text: string) => {
      const validCharsSuffix = allowWhitespace ? "" : "\\s";
      const validChars = "[^" + trigger + punctuation + validCharsSuffix + "]";
      const TypeaheadTriggerRegex = new RegExp(
        "(" +
          "[" +
          trigger +
          "]" +
          "((?:" +
          validChars +
          "){0," +
          maxLength +
          "})" +
          ")$",
      );
      const match = TypeaheadTriggerRegex.exec(text);
      if (match !== null) {
        const matchingString = match[2];
        if (matchingString.length >= minLength) {
          return {
            leadOffset: match.index,
            matchingString,
            replaceableString: match[1],
          };
        }
      }
      return null;
    },
    [allowWhitespace, trigger, punctuation, maxLength, minLength],
  );
}

interface ParamPluginProps {
  params: ParamType[];
}

export function ParamPlugin({ params }: ParamPluginProps): JSX.Element {
  const [editor] = useLexicalComposerContext();
  const [queryString, setQueryString] = useState<string | null>("");

  const checkForTriggerMatch = useBasicTypeaheadTriggerMatch("/", {
    allowWhitespace: true,
    minLength: 0,
  });

  useEffect(() => {
    if (!editor.hasNodes([ParamNode])) {
      throw new Error("param node is not registered");
    }
    return editor.registerCommand(
      INSERT_PARAM_COMMAND,
      ({
        nodeId,
        nodeName,
        paramId,
        paramName,
      }: {
        nodeId: string;
        nodeName: string;
        paramId: string;
        paramName: string;
      }) => {
        const paramNode = $createParamNode(
          nodeId,
          nodeName,
          paramId,
          paramName,
        );
        $insertNodes([paramNode]);
        if ($isRootOrShadowRoot(paramNode.getParentOrThrow())) {
          $wrapNodeInElement(paramNode, $createParagraphNode).selectEnd();
        }
        return true;
      },
      COMMAND_PRIORITY_HIGH,
    );
  }, [editor]);

  const baseOptions = useMemo(
    () =>
      params.map(
        (param) =>
          new ParamPickerOption(
            param.nodeId,
            param.nodeName,
            param.paramId,
            param.paramName,
            () => {
              editor.dispatchCommand(INSERT_PARAM_COMMAND, {
                nodeId: param.nodeId,
                nodeName: param.nodeName,
                paramId: param.paramId,
                paramName: param.paramName,
              });
            },
          ),
      ),
    [editor, params],
  );

  const options = useMemo(
    () =>
      baseOptions.filter((option) =>
        new RegExp(queryString || "").test(option.paramName),
      ),
    [baseOptions, queryString],
  );

  const onSelectOption = useCallback(
    (
      selectedOption: ParamPickerOption,
      nodeToReplace: TextNode | null,
      closeMenu: () => void,
    ) => {
      editor.update(() => {
        const paramNode = $createParamNode(
          selectedOption.nodeId,
          selectedOption.nodeName,
          selectedOption.paramId,
          selectedOption.paramName,
        );
        if (paramNode) {
          nodeToReplace?.replace(paramNode);
        }
        paramNode.selectEnd();
        closeMenu();
      });
    },
    [editor],
  );

  return (
    <LexicalTypeaheadMenuPlugin<ParamPickerOption>
      onQueryChange={setQueryString}
      onSelectOption={onSelectOption}
      triggerFn={checkForTriggerMatch}
      options={options}
      menuRenderFn={(
        anchorElementRef,
        { selectedIndex, selectOptionAndCleanUp, setHighlightedIndex },
      ) =>
        anchorElementRef.current && options.length
          ? ReactDOM.createPortal(
              <div className="typeahead-popover component-picker-menu">
                <ul>
                  {options.map((option, i: number) => (
                    <ParamPickerMenuItem
                      index={i}
                      isSelected={selectedIndex === i}
                      onClick={() => {
                        setHighlightedIndex(i);
                        selectOptionAndCleanUp(option);
                      }}
                      onMouseEnter={() => {
                        setHighlightedIndex(i);
                      }}
                      key={option.key}
                      option={option}
                    />
                  ))}
                </ul>
              </div>,
              anchorElementRef.current,
            )
          : null
      }
    />
  );
}
