import { describe, expect, test } from "vitest";
import { parseExpression, SyntaxRule } from "./param-string-parser.ts";

interface OutputParam {
  kind: "output";
  nodeId: string;
  paramId: string;
}

interface FuncCall {
  kind: "function";
  name: string;
  arg: string;
}

const mustacheRule: SyntaxRule<OutputParam> = {
  startToken: "{{",
  endToken: "}}",
  parse(v) {
    const [nodeId, paramId] = v.split(".");
    if (!nodeId || !paramId) {
      return new Error("must be in node.param format");
    }
    return { kind: "output", nodeId, paramId };
  },
};

const fnRule: SyntaxRule<FuncCall> = {
  startToken: "$(",
  endToken: ")",
  parse(v) {
    const [name, arg] = v.split(",");
    if (!name || !arg) return new Error("must be function,arg");
    return { kind: "function", name: name.trim(), arg: arg.trim() };
  },
};

const errorRule: SyntaxRule<any> = {
  startToken: "",
  endToken: "",
};

describe("parseExpression", () => {
  test("单个 Mustache 表达式", () => {
    const input = "{{ user.name }}";
    const result = parseExpression(input, [mustacheRule]);
    expect(result).toStrictEqual([
      {
        type: "param",
        value: {
          kind: "output",
          nodeId: "user",
          paramId: "name",
        },
      },
    ]);
  });

  test("文本 + 表达式混合", () => {
    const input = "Hello {{ user.name }}!";
    const result = parseExpression(input, [mustacheRule]);
    expect(result).toStrictEqual([
      { type: "text", value: "Hello " },
      {
        type: "param",
        value: {
          kind: "output",
          nodeId: "user",
          paramId: "name",
        },
      },
      { type: "text", value: "!" },
    ]);
  });

  test("多个 Mustache 表达式", () => {
    const input = "{{ user.name }} & {{ user.age }}";
    const result = parseExpression(input, [mustacheRule]);
    expect(result).toStrictEqual([
      {
        type: "param",
        value: { kind: "output", nodeId: "user", paramId: "name" },
      },
      { type: "text", value: " & " },
      {
        type: "param",
        value: { kind: "output", nodeId: "user", paramId: "age" },
      },
    ]);
  });

  test.fails("规则错误", () => {
    const input = "Hello {{ user.name }}!";
    expect(parseExpression(input, [errorRule])).toBe(
      Error("startToken and endToken must be non-empty"),
    );
  });

  test("表达式未闭合应返回 error", () => {
    const input = "Hello {{ user.name";
    const result = parseExpression(input, [mustacheRule]);
    expect(result).toStrictEqual([
      {
        type: "text",
        value: "Hello ",
      },
      {
        type: "error",
        message: "variable not closed at {{ user.name",
      },
    ]);
  });

  test("表达式 parse 错误应返回 error", () => {
    const input = "{{ invalid }}";
    const result = parseExpression(input, [mustacheRule]);
    expect(result).toStrictEqual([
      {
        type: "error",
        message: "variable parse error: must be in node.param format",
      },
    ]);
  });

  test("函数表达式 $()", () => {
    const input = "Result is $(sum, 100)";
    const result = parseExpression(input, [fnRule]);
    expect(result).toStrictEqual([
      { type: "text", value: "Result is " },
      {
        type: "param",
        value: {
          kind: "function",
          name: "sum",
          arg: "100",
        },
      },
    ]);
  });

  test("混合使用两种规则1", () => {
    const input = "User: {{ user.name }}, Score: $(score, 99)";
    const result = parseExpression<OutputParam | FuncCall>(input, [
      mustacheRule,
      fnRule,
    ]);
    expect(result).toStrictEqual([
      { type: "text", value: "User: " },
      {
        type: "param",
        value: {
          kind: "output",
          nodeId: "user",
          paramId: "name",
        },
      },
      { type: "text", value: ", Score: " },
      {
        type: "param",
        value: {
          kind: "function",
          name: "score",
          arg: "99",
        },
      },
    ]);
  });

  test("混合使用两种规则2", () => {
    const input = "Score: $(score, 99), User: {{ user.name }}";
    const result = parseExpression<OutputParam | FuncCall>(input, [
      mustacheRule,
      fnRule,
    ]);
    expect(result).toStrictEqual([
      { type: "text", value: "Score: " },
      {
        type: "param",
        value: {
          kind: "function",
          name: "score",
          arg: "99",
        },
      },
      { type: "text", value: ", User: " },
      {
        type: "param",
        value: {
          kind: "output",
          nodeId: "user",
          paramId: "name",
        },
      },
    ]);
  });
});
