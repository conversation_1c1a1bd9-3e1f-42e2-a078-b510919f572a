import {
  parseExpression,
  SyntaxRuleSet,
} from "@/components/param-editor/param-string-parser.ts";

interface NodeOutputParam {
  nodeId: string;
  paramId: string;
}

const rules: SyntaxRuleSet<NodeOutputParam> = [
  {
    startToken: "{{",
    endToken: "}}",
    parse(v) {
      const [nodeId, paramId] = v.split(".");
      if (!nodeId || !paramId) {
        return new Error("must be in node.param format");
      }
      return { nodeId, paramId };
    },
  },
];

export function parseToLexicalEditorState(value: string): string {
  const paragraphs = value.split("\n");
  const editorState = createRoot();
  for (const paragraph of paragraphs) {
    const nodes = parseExpression(paragraph, rules);
    const paragraphNodes = [];
    for (const node of nodes) {
      if (node.type === "param") {
        paragraphNodes.push(
          createParamNode(node.value.nodeId, node.value.paramId),
        );
      } else if (node.type === "text") {
        paragraphNodes.push(createTextNode(node.value));
      }
    }
    editorState.root.children.push(createParagraph(paragraphNodes));
  }
  return JSON.stringify(editorState);
}

function createRoot(children: any[] = []) {
  return {
    root: {
      children,
      direction: null,
      format: "",
      indent: 0,
      type: "root",
      version: 1,
    },
  };
}

function createParagraph(children: any[] = []) {
  return {
    children,
    direction: null,
    format: "",
    indent: 0,
    type: "paragraph",
    version: 1,
    textFormat: 0,
    textStyle: "",
  };
}

function createTextNode(text: string) {
  return {
    detail: 0,
    format: 0,
    mode: "normal",
    style: "",
    text,
    type: "text",
    version: 1,
  };
}

function createParamNode(nodeId: string, paramId: string) {
  return {
    type: "param",
    version: 1,
    nodeId,
    nodeName: "",
    paramId,
    paramName: "",
  };
}
