import { PropsWithChildren, ReactNode } from "react";
import { IconType } from "@/components/workflow/types/workflow.ts";
import { RiQuestionMark } from "@remixicon/react";

interface NodeIconProps extends PropsWithChildren {
  icon?: IconType;
}

export function NodeIcon({ icon }: NodeIconProps): ReactNode {
  if (icon === undefined) {
    return (
      <div className="p-1">
        <RiQuestionMark size={16} />
      </div>
    );
  }
  if (typeof icon === "string") {
    return <img src={icon} />;
  }
  const Icon = icon;
  return <Icon />;
}
