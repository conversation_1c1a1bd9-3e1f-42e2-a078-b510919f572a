import { ComponentProps, useEffect, useState } from "react";
import { cn } from "@/lib/utils.ts";
import { NodeIcon } from "./node-icon";
import {
  NodeHandle,
  NodeSchema,
  WorkflowNodeProps,
} from "@/components/workflow/types/workflow.ts";
import { NodeSchemaUndefined } from "@/components/workflow/node/node-schema-undefined.tsx";
import { BaseHandle } from "@/components/workflow/node/base-handle.tsx";
import { useGetNodeDefinitionByTypeAndVersion } from "@/api/node-definition.ts";

export function BaseNode({
  children,
  className,
  selected,
  ...props
}: WorkflowNodeProps & ComponentProps<"div">) {
  const {
    data: { type, version, name },
  } = props;

  const [requestNodeSchema] = useGetNodeDefinitionByTypeAndVersion();
  const [nodeSchema, setNodeSchema] = useState<NodeSchema | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      const schema = await requestNodeSchema(type, version);
      setNodeSchema(schema);
    };
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, version]);

  return (
    <div
      className={cn(
        "group min-w-3xs p-2 rounded-2xl shadow-lg bg-white dark:bg-zinc-800 text-foreground transition-[color,box-shadow] text-sm",
        className,
        selected ? "ring-2 ring-black dark:ring-white" : "",
      )}
    >
      <NodeHandles
        handles={nodeSchema?.input_ports || []}
        selected={selected}
      />
      <NodeHandles
        handles={nodeSchema?.output_ports || []}
        selected={selected}
      />
      <div className="flex items-center">
        <NodeIcon icon={nodeSchema?.icon} />
        <div className="ml-2 text-lg font-bold">
          {name || nodeSchema?.name || type}
        </div>
      </div>
      {nodeSchema ? children : <NodeSchemaUndefined />}
    </div>
  );
}

function NodeHandles({
  handles,
  selected,
}: {
  handles: NodeHandle[];
  selected: boolean;
}) {
  return (
    <>
      {handles.map((handle) => (
        <BaseHandle
          key={handle.type + handle.position}
          type={handle.type}
          position={handle.position}
          selected={selected}
        />
      ))}
    </>
  );
}
