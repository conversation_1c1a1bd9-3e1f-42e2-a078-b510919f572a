import { Handle, HandleProps } from "@xyflow/react";
import { HTMLAttributes, useCallback } from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip.tsx";
import { Plus } from "lucide-react";
import { cn } from "@/lib/utils.ts";

type BaseHandleProps = HandleProps &
  Omit<HTMLAttributes<HTMLDivElement>, "id"> & {
    selected?: boolean;
  };

export function BaseHandle({ type, position, selected }: BaseHandleProps) {
  const handleClick = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
  }, []);

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Handle
            className={cn(
              "w-[20px]! h-[20px]! min-w-0! min-h-0! bg-transparent! after:absolute after:w-[2px] after:h-[6px] after:bg-gray-500 after:top-1/2 after:transform-[translateY(-50%)] z-[2000]",
              type === "source" ? "after:left-1/2" : "after:right-1/2",
            )}
            type={type}
            position={position}
            onClick={handleClick}
          >
            <Plus
              className={cn(
                "absolute hidden group-hover:flex bg-black text-white rounded-full p-[1px] font-bold pointer-events-none z-[2000]",
                {
                  block: selected,
                },
              )}
              size={20}
            />
          </Handle>
        </TooltipTrigger>
        <TooltipContent>
          <p>点击添加节点</p>
          <p>拖拽连接节点</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
