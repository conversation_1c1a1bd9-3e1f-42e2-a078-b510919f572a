import { ComponentProps, useCallback, useRef } from "react";
import { cn } from "@/lib/utils.ts";

interface NodeNameProps {
  name?: string;
  onNameChange?: (name: string) => void;
}

export function NodeName({
  name,
  onNameChange,
  className,
}: NodeNameProps & ComponentProps<"div">) {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      onNameChange?.(e.currentTarget.value);
    },
    [onNameChange],
  );

  return (
    <input
      className={cn(
        "outline-none focus:border-b text-lg font-bold pl-1",
        className,
      )}
      type="text"
      placeholder="节点名称"
      value={name}
      onChange={handleChange}
      ref={inputRef}
    />
  );
}
