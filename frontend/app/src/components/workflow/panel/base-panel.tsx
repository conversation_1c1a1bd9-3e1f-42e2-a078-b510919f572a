import { cn } from "@/lib/utils.ts";
import {
  PanelProps,
  WorkflowNode,
} from "@/components/workflow/types/workflow.ts";
import { PropsWithChildren, useCallback } from "react";
import { NodeIcon } from "@/components/workflow/node/node-icon.tsx";
import { NodeName } from "@/components/workflow/node/node-name.tsx";
import { useNodeOperations } from "@/components/workflow/hooks/use-node-operations.tsx";
import { Button } from "@/components/ui/button.tsx";
import { X } from "lucide-react";

interface BasePanelProps extends PropsWithChildren {
  className?: string;
}

export function BasePanel({
  id,
  children,
  className,
  schema,
  data: { name },
}: BasePanelProps & WorkflowNode & PanelProps) {
  const { handleUnselectNode } = useNodeOperations();

  const { handleUpdateNodeData } = useNodeOperations();

  const handleNameChange = useCallback(
    (newName: string) => {
      handleUpdateNodeData(id, {
        name: newName,
      });
    },
    [handleUpdateNodeData, id],
  );

  if (!schema) {
    return (
      <section
        className={cn(
          "bg-background rounded-lg p-4 shadow-lg text-foreground w-lg",
          className,
        )}
      >
        节点定义不存在
      </section>
    );
  }

  return (
    <section
      className={cn(
        "bg-background rounded-lg px-4 shadow-lg text-foreground w-md flex flex-col",
        className,
      )}
    >
      <header className="flex items-center py-4">
        <NodeIcon icon={schema?.icon} />
        <NodeName
          className="flex-1 mx-2"
          name={name || schema?.name}
          onNameChange={handleNameChange}
        />
        <Button
          variant="secondary"
          size="sm"
          className="size-8"
          onClick={() => handleUnselectNode(id)}
        >
          <X />
        </Button>
      </header>
      <div className="overflow-y-scroll flex-1">{children}</div>
    </section>
  );
}
