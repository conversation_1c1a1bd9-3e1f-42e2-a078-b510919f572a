import { Divider } from "@/components/divider.tsx";
import { PanelProps } from "@/components/workflow/types/workflow.ts";
import { InputParams } from "@/components/workflow/node-params/input-params.tsx";
import { OutputParams } from "@/components/workflow/node-params/output-params.tsx";
import { useNodeOperations } from "@/components/workflow/hooks/use-node-operations.tsx";
import { useCallback } from "react";
import { useUpstreamOutputParams } from "@/components/workflow/hooks/use-upstream-output-params.ts";

export function CustomNodePanel({
  schema,
  id,
  data: { input_values },
}: PanelProps) {
  const { handleUpdateNodeData } = useNodeOperations();

  const handleInputChange = useCallback(
    (paramId: string, paramValue: string) => {
      handleUpdateNodeData(id, {
        input_values: {
          [paramId]: paramValue,
        },
      });
    },
    [handleUpdateNodeData, id],
  );

  const params = useUpstreamOutputParams(id);

  if (!schema) {
    return <div>节点定义不存在</div>;
  }

  return (
    <div className="mb-8">
      {schema.input_params.length > 0 && (
        <InputParams
          {...schema}
          input_values={input_values}
          onParamChange={handleInputChange}
          upstreamOutputParams={params}
        />
      )}
      <Divider />
      {schema.output_params.length > 0 && <OutputParams {...schema} />}
    </div>
  );
}
