import { BasePanel } from "./base-panel.tsx";
import { useMemo } from "react";
import { NodePanelComponentMap } from "@/components/workflow/nodes/constants.ts";
import { WorkflowNode } from "@/components/workflow/types/workflow.ts";
import { useNodeSchema } from "@/components/workflow/hooks/use-node-schema.tsx";

export function NodePanel(props: WorkflowNode) {
  const {
    type: nodeType,
    data: { type, version },
  } = props;
  const { nodeSchema } = useNodeSchema(type, version);
  const PanelComponent = useMemo(() => {
    if (!nodeType) {
      return () => null;
    }
    const panel = NodePanelComponentMap[nodeType];
    if (panel) return panel;
    return () => null;
  }, [nodeType]);

  return (
    <BasePanel
      className="absolute z-10 top-10 right-4 bottom-4"
      {...props}
      schema={nodeSchema}
    >
      <PanelComponent {...props} schema={nodeSchema} />
    </BasePanel>
  );
}
