import { createWorkflowSlice, WorkflowShape } from "./workflow-slice.ts";
import { createStore } from "zustand/vanilla";
import { Edge, Viewport } from "@xyflow/react";
import { NodeSchema, WorkflowNode } from "../types/workflow";

export type WorkflowStore = WorkflowShape;

export interface WorkflowStoreProps {
  nodes: WorkflowNode[];
  edges: Edge[];
  availableNodes: NodeSchema[];
  viewport: Viewport;
}

export const createWorkflowStore = (props: WorkflowStoreProps) => {
  return createStore<WorkflowStore>((...args) => ({
    ...createWorkflowSlice(...args),
    ...props,
  }));
};
