import { StateCreator } from "zustand/vanilla";
import { EdgeType, WorkflowNode, NodeSchema } from "../types/workflow";
import { Viewport } from "@xyflow/react";

export interface WorkflowShape {
  nodes: WorkflowNode[];
  setNodes: (
    nodes: WorkflowNode[] | ((prevNodes: WorkflowNode[]) => WorkflowNode[]),
  ) => void;
  edges: EdgeType[];
  setEdges: (
    edges: EdgeType[] | ((prevEdges: EdgeType[]) => EdgeType[]),
  ) => void;
  getSelectedNode: () => WorkflowNode | undefined | null;
  availableNodes: NodeSchema[];
  viewport: Viewport;
  setViewport: (viewport: Viewport) => void;
}

export const createWorkflowSlice: StateCreator<WorkflowShape> = (set, get) => ({
  nodes: [],
  setNodes: (nodes) => {
    if (typeof nodes === "function") {
      set((state) => ({ nodes: nodes(state.nodes) }));
    } else {
      set({ nodes });
    }
  },
  edges: [],
  setEdges: (edges) => {
    if (typeof edges === "function") {
      set((state) => ({ edges: edges(state.edges) }));
    } else {
      set({ edges });
    }
  },
  getSelectedNode: () => get().nodes.find((n) => n.selected),
  availableNodes: [],
  viewport: {
    x: 0,
    y: 0,
    zoom: 1,
  },
  setViewport: (viewport: Viewport) => {
    set({ viewport });
  },
});
