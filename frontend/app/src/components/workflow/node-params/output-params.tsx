import { NodeParam, NodeSchema } from "@/components/workflow/types/workflow.ts";
import { Badge } from "@/components/ui/badge.tsx";
import { Label } from "@/components/ui/label.tsx";

export function OutputParams({ output_params }: NodeSchema) {
  return (
    <div className="grid w-full items-center gap-3">
      <Label>输出参数</Label>
      {output_params.map(({ id, type, label, description }) => (
        <Param
          id={id}
          type={type}
          label={label}
          description={description}
          required={false}
          key={label}
        />
      ))}
    </div>
  );
}

function Param({ label, description, type }: NodeParam) {
  return (
    <div className="flex flex-col gap-2">
      <div className="flex gap-2 items-center">
        <div className="text-sm">{label}</div>
        <Badge variant="secondary">
          <code>{type}</code>
        </Badge>
      </div>
      <div className="text-sm text-muted-foreground">{description}</div>
    </div>
  );
}
