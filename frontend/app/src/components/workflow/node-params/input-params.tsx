import {
  NodeParam,
  NodeSchema,
  WorkflowNode,
} from "@/components/workflow/types/workflow.ts";
import { Label } from "@/components/ui/label.tsx";
import { Badge } from "@/components/ui/badge.tsx";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip.tsx";
import { CircleHelp } from "lucide-react";
import { ParamInput } from "@/components/workflow/node-params/param-input.tsx";
import { Merge } from "react-hook-form";
import { ParamType } from "@/components/param-editor/param-plugin";

export function InputParams({
  input_params,
  input_values = {},
  onParamChange,
  upstreamOutputParams,
}: Merge<NodeSchema, WorkflowNode["data"]> & {
  onParamChange?: (paramId: string, value: string) => void;
  upstreamOutputParams: ParamType[];
}) {
  return (
    <div className="w-full flex flex-col gap-3">
      <Label>输入参数</Label>
      {input_params.map((param: NodeParam) => (
        <ParamField
          key={param.id}
          {...param}
          value={input_values[param.id] || ""}
          onChange={(v) => onParamChange?.(param.id, v)}
          params={upstreamOutputParams}
        />
      ))}
    </div>
  );
}

function ParamField({
  label,
  description,
  type,
  value = "",
  onChange,
  params,
}: NodeParam & {
  value: string;
  onChange?: (v: string) => void;
  params: ParamType[];
}) {
  return (
    <div className="flex flex-col gap-1">
      <div className="flex gap-2 items-center">
        <div className="text-sm">{label}</div>
        <Tooltip>
          <TooltipTrigger asChild>
            <CircleHelp size="16" />
          </TooltipTrigger>
          <TooltipContent>
            <p>{description}</p>
          </TooltipContent>
        </Tooltip>
        <Badge variant="secondary">
          <code>{type}</code>
        </Badge>
      </div>
      <ParamInput value={value} onValueChange={onChange} params={params} />
    </div>
  );
}
