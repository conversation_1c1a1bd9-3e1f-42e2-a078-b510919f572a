import { useWorkflowStore } from "../context/workflow-context.tsx";
import { useCallback } from "react";
import { useShallow } from "zustand/react/shallow";
import {
  addEdge,
  applyEdgeChanges,
  EdgeMouseHandler,
  OnConnect,
  OnEdgesChange,
} from "@xyflow/react";
import { produce } from "immer";
import { EdgeType } from "../types/workflow.ts";
import { v4 as uuidv4 } from "uuid";

export function useEdgeOperations() {
  const { setEdges } = useWorkflowStore(
    useShallow((s) => ({
      setEdges: s.setEdges,
    })),
  );

  const handleRemoveEdge = useCallback(
    (id: string) => {
      setEdges((prevEdges) => prevEdges.filter((e) => e.id !== id));
    },
    [setEdges],
  );

  const handleEdgesChange = useCallback<OnEdgesChange>(
    (changes) => {
      setEdges((prevEdges) => applyEdgeChanges(changes, prevEdges));
    },
    [setEdges],
  );

  const handleConnect = useCallback<OnConnect>(
    (connection) => {
      setEdges((prevEdges) =>
        addEdge({ ...connection, type: "CustomEdge", id: uuidv4() }, prevEdges),
      );
    },
    [setEdges],
  );

  const handleEdgeMouseEnter = useCallback<EdgeMouseHandler>(
    (_, edge: EdgeType) => {
      setEdges((prevEdges) => {
        return produce(prevEdges, (draft) => {
          const findEdge = draft.find((e) => e.id === edge.id);
          if (findEdge) {
            findEdge.hover = true;
          }
        });
      });
    },
    [setEdges],
  );

  const handleEdgeMouseLeave = useCallback<EdgeMouseHandler>(
    (_, edge: EdgeType) => {
      setEdges((prevEdges) => {
        return produce(prevEdges, (draft) => {
          const findEdge = draft.find((e) => e.id === edge.id);
          if (findEdge) {
            findEdge.hover = false;
          }
        });
      });
    },
    [setEdges],
  );

  return {
    handleRemoveEdge,
    handleEdgesChange,
    handleConnect,
    handleEdgeMouseEnter,
    handleEdgeMouseLeave,
  };
}
