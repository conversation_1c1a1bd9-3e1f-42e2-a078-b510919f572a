import { useCallback } from "react";
import {
  apply<PERSON><PERSON><PERSON><PERSON><PERSON>,
  NodeMouseHandler,
  NodeSelectionChange,
  OnNodesChange,
  XYPosition,
} from "@xyflow/react";
import { produce } from "immer";
import { useWorkflowStore } from "../context/workflow-context.tsx";
import { useShallow } from "zustand/react/shallow";
import {
  CommonNodeData,
  NodeData,
  NodeSchema,
  WorkflowNode,
} from "../types/workflow.ts";
import { v4 as uuidv4 } from "uuid";
import { NodeTypeEnum } from "@/components/workflow/nodes/constants.ts";

export function useNodeOperations() {
  const { setNodes } = useWorkflowStore(
    useShallow((s) => ({
      setNodes: s.setNodes,
    })),
  );

  const handleSelectNode = useCallback(
    (id: string, cancelSelect?: boolean) => {
      setNodes((prevNodes) => {
        const selectedNode = prevNodes.find((n) => n.selected);
        if (!cancelSelect && selectedNode?.id === id) return prevNodes;
        return produce(prevNodes, (draft) => {
          for (const node of draft) {
            if (node.id === id) {
              node.selected = !cancelSelect;
            } else {
              node.selected = false;
            }
          }
        });
      });
    },
    [setNodes],
  );

  const handleUnselectNode = useCallback<(nodeId: string) => void>(
    (nodeId) => {
      handleSelectNode(nodeId, true);
    },
    [handleSelectNode],
  );

  const handleNodeClick = useCallback<NodeMouseHandler>(
    (_, node) => {
      setNodes((prevNodes) => {
        const selectedNode = prevNodes.find((n) => n.selected);
        if (selectedNode && selectedNode.id === node.id) {
          return produce(prevNodes, (draft) => {
            for (const n of draft) {
              if (n.id === node.id) {
                n.selected = false;
              }
            }
          });
        } else {
          return produce(prevNodes, (draft) => {
            for (const n of draft) {
              n.selected = n.id === node.id;
            }
          });
        }
      });
    },
    [setNodes],
  );

  const handleNodesChange = useCallback<OnNodesChange>(
    (changes) => {
      setNodes((prevNodes) => {
        return applyNodeChanges(
          changes.filter(
            (c) =>
              c.type !== "select" ||
              !(c as unknown as NodeSelectionChange).selected,
          ),
          prevNodes,
        ) as WorkflowNode[];
      });
    },
    [setNodes],
  );

  const handleAddNode = useCallback(
    (nodeDef: NodeSchema, position: XYPosition) => {
      setNodes((prevNodes) => {
        return produce(prevNodes, (draft) => {
          draft.push({
            id: uuidv4(),
            type: NodeTypeEnum.CustomNode,
            data: {
              name: nodeDef.name,
              icon: nodeDef.icon,
              version: nodeDef.version,
              type: nodeDef.type,
              description: "",
              input_params: [],
              input_values: {},
              output_params: [],
              output_values: {},
              input_ports: [],
              output_ports: [],
            },
            position,
          });
        });
      });
    },
    [setNodes],
  );

  const handleUpdateNodeData = useCallback<
    <
      InputData extends NodeData = NodeData,
      OutputData extends NodeData = NodeData,
    >(
      nodeId: string,
      newNodeData: Partial<CommonNodeData<InputData, OutputData>>,
    ) => void
  >(
    (nodeId, newNodeData) => {
      setNodes((prevNodes) => {
        return prevNodes.map((pn) => {
          if (pn.id === nodeId) {
            return {
              ...pn,
              data: {
                ...pn.data,
                ...newNodeData,
              },
            };
          }
          return pn;
        });
      });
    },
    [setNodes],
  );

  return {
    handleSelectNode,
    handleUnselectNode,
    handleNodeClick,
    handleNodesChange,
    handleAddNode,
    handleUpdateNodeData,
  };
}
