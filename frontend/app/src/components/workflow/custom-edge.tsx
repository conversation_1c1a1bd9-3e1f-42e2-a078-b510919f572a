import {
  BaseEdge,
  EdgeLabelRenderer,
  EdgeProps,
  getBezierPath,
  useReactFlow,
} from "@xyflow/react";
import { cn } from "@/lib/utils.ts";
import { X } from "lucide-react";
import { useCallback } from "react";
import { useEdgeOperations } from "./hooks/use-edge-operations.tsx";
import { WorkflowEdge } from "./types/workflow.ts";

export function CustomEdge({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
}: EdgeProps) {
  const { hover } = useReactFlow().getEdge(id) as unknown as WorkflowEdge<
    Record<string, unknown>
  >;
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX: sourceX - 10,
    sourceY,
    sourcePosition,
    targetX: targetX + 10,
    targetY,
    targetPosition,
  });
  const { handleRemoveEdge } = useEdgeOperations();

  const handleDelClick = useCallback(() => {
    handleRemoveEdge(id);
  }, [handleRemoveEdge, id]);

  return (
    <>
      <BaseEdge
        id={id}
        path={edgePath}
        markerEnd={markerEnd}
        style={{ ...style, strokeWidth: 2 }}
      />
      <EdgeLabelRenderer>
        <div
          className={cn(
            "absolute nodrag nopan z-[2000] origin-center",
            hover ? "flex" : "hidden",
          )}
          style={{
            pointerEvents: "all",
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
          }}
        >
          <X
            className={cn(
              "bg-black text-white rounded-full p-[2px] font-bold cursor-pointer  hover:scale-125 transition-transform",
            )}
            size={10}
            onClick={handleDelClick}
          />
        </div>
      </EdgeLabelRenderer>
    </>
  );
}
