import { BaseNode } from "@/components/workflow/node/base-node.tsx";
import { WorkflowNodeProps } from "@/components/workflow/types/workflow.ts";
import {
  EventTriggerInputData,
  EventTriggerOutputData,
} from "@/components/workflow/nodes/event-trigger/type.ts";

export function Node(
  props: WorkflowNodeProps<EventTriggerInputData, EventTriggerOutputData>,
) {
  return (
    <BaseNode className="p-4" {...props}>
      <div className="flex items-center mt-2 bg-neutral-100 dark:bg-zinc-700 p-1 rounded nodrag nopan">
        通过
        <div className="hover:bg-yellow-200 rounded transition-all px-1">
          {props.data.input_values.event_name}
        </div>
        触发
      </div>
    </BaseNode>
  );
}
