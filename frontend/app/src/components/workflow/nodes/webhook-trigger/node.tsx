import { WorkflowNodeProps } from "@/components/workflow/types/workflow.ts";
import { BaseNode } from "@/components/workflow/node/base-node.tsx";
import {
  WebhookTriggerInputData,
  WebhookTriggerOutputData,
} from "@/components/workflow/nodes/webhook-trigger/type.ts";

export function Node(
  props: WorkflowNodeProps<WebhookTriggerInputData, WebhookTriggerOutputData>,
) {
  return <BaseNode className="p-4" {...props}></BaseNode>;
}
