import { ComponentType } from "react";
import { CronTriggerPanel } from "@/components/workflow/nodes/cron-trigger";
import { EndNodePanel } from "@/components/workflow/nodes/end-node";
import { EventTriggerPanel } from "./event-trigger";
import { WebhookTriggerPanel } from "@/components/workflow/nodes/webhook-trigger";
import { Viewport } from "@xyflow/react";
import { PanelProps } from "@/components/workflow/types/workflow.ts";
import { CustomNodePanel } from "@/components/workflow/panel/custom-node-panel.tsx";

export const DEFAULT_VIEWPORT: Viewport = {
  x: 0,
  y: 0,
  zoom: 1,
};

export enum NodeTypeEnum {
  CronTrigger = "cron-trigger",
  EventTrigger = "event-trigger",
  WebhookTrigger = "webhook-trigger",
  EndNode = "end-node",
  CustomNode = "custom",
}

export const NodePanelComponentMap: Record<
  string,
  ComponentType<PanelProps<any, any>>
> = {
  [NodeTypeEnum.CronTrigger]: CronTriggerPanel,
  [NodeTypeEnum.EventTrigger]: EventTriggerPanel,
  [NodeTypeEnum.WebhookTrigger]: WebhookTriggerPanel,
  [NodeTypeEnum.EndNode]: EndNodePanel,
  [NodeTypeEnum.CustomNode]: CustomNodePanel,
};