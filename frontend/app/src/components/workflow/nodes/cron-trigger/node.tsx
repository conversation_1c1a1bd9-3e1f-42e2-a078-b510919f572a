import { BaseNode } from "../../node/base-node.tsx";
import { WorkflowNodeProps } from "../../types/workflow.ts";
import {
  CronTriggerInputData,
  CronTriggerOutputData,
} from "@/components/workflow/nodes/cron-trigger/type.ts";

export function Node(
  props: WorkflowNodeProps<CronTriggerInputData, CronTriggerOutputData>,
) {
  return (
    <BaseNode className="p-4" {...props}>
      <div className="flex items-center mt-2 p-1 rounded text-sm gap-1">
        每
        <div className="bg-zinc-100 dark:bg-zinc-600 rounded transition-all px-1 nodrag nopan cursor-pointer">
          {props.data.input_values.cron_expression}
        </div>
        定时触发
      </div>
    </BaseNode>
  );
}
