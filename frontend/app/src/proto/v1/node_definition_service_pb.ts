// @generated by protoc-gen-es v2.6.2 with parameter "target=ts"
// @generated from file v1/node_definition_service.proto (package v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { NodeParam, NodePort } from "./node_pb";
import { file_v1_node } from "./node_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file v1/node_definition_service.proto.
 */
export const file_v1_node_definition_service: GenFile = /*@__PURE__*/
  fileDesc("CiB2MS9ub2RlX2RlZmluaXRpb25fc2VydmljZS5wcm90bxICdjEi1QIKDk5vZGVEZWZpbml0aW9uEgoKAmlkGAEgASgJEgwKBG5hbWUYBCABKAkSDgoGYXV0aG9yGAUgASgJEhMKC2Rlc2NyaXB0aW9uGAYgASgJEgwKBGljb24YByABKAkSDAoEdHlwZRgIIAEoCRIPCgd2ZXJzaW9uGAkgASgJEhAKCGNhdGVnb3J5GAogASgJEiMKDGlucHV0X3BhcmFtcxgLIAMoCzINLnYxLk5vZGVQYXJhbRIkCg1vdXRwdXRfcGFyYW1zGAwgAygLMg0udjEuTm9kZVBhcmFtEiEKC2lucHV0X3BvcnRzGA0gAygLMgwudjEuTm9kZVBvcnQSIgoMb3V0cHV0X3BvcnRzGA4gAygLMgwudjEuTm9kZVBvcnQSEQoJZXhjZXB0aW9uGA8gASgIEg8KB2J1aWx0aW4YECABKAgSDwoHZW5hYmxlZBgRIAEoCCIcChpMaXN0Tm9kZURlZmluaXRpb25zUmVxdWVzdCJLChtMaXN0Tm9kZURlZmluaXRpb25zUmVzcG9uc2USLAoQbm9kZV9kZWZpbml0aW9ucxgBIAMoCzISLnYxLk5vZGVEZWZpbml0aW9uIkkKKEdldE5vZGVEZWZpbml0aW9uQnlUeXBlQW5kVmVyc2lvblJlcXVlc3QSDAoEdHlwZRgBIAEoCRIPCgd2ZXJzaW9uGAIgASgJIlgKKUdldE5vZGVEZWZpbml0aW9uQnlUeXBlQW5kVmVyc2lvblJlc3BvbnNlEisKD25vZGVfZGVmaW5pdGlvbhgBIAEoCzISLnYxLk5vZGVEZWZpbml0aW9uMtQBChVOb2RlRGVmaW5pdGlvblNlcnZpY2USRwoETGlzdBIeLnYxLkxpc3ROb2RlRGVmaW5pdGlvbnNSZXF1ZXN0Gh8udjEuTGlzdE5vZGVEZWZpbml0aW9uc1Jlc3BvbnNlEnIKE0dldEJ5VHlwZUFuZFZlcnNpb24SLC52MS5HZXROb2RlRGVmaW5pdGlvbkJ5VHlwZUFuZFZlcnNpb25SZXF1ZXN0Gi0udjEuR2V0Tm9kZURlZmluaXRpb25CeVR5cGVBbmRWZXJzaW9uUmVzcG9uc2VCH1odcmVzZmxvdy9wcm90by9nZW5lcmF0ZWRfZ28vdjFiBnByb3RvMw", [file_v1_node]);

/**
 * @generated from message v1.NodeDefinition
 */
export type NodeDefinition = Message<"v1.NodeDefinition"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 4;
   */
  name: string;

  /**
   * @generated from field: string author = 5;
   */
  author: string;

  /**
   * @generated from field: string description = 6;
   */
  description: string;

  /**
   * @generated from field: string icon = 7;
   */
  icon: string;

  /**
   * @generated from field: string type = 8;
   */
  type: string;

  /**
   * @generated from field: string version = 9;
   */
  version: string;

  /**
   * @generated from field: string category = 10;
   */
  category: string;

  /**
   * @generated from field: repeated v1.NodeParam input_params = 11;
   */
  inputParams: NodeParam[];

  /**
   * @generated from field: repeated v1.NodeParam output_params = 12;
   */
  outputParams: NodeParam[];

  /**
   * @generated from field: repeated v1.NodePort input_ports = 13;
   */
  inputPorts: NodePort[];

  /**
   * @generated from field: repeated v1.NodePort output_ports = 14;
   */
  outputPorts: NodePort[];

  /**
   * @generated from field: bool exception = 15;
   */
  exception: boolean;

  /**
   * @generated from field: bool builtin = 16;
   */
  builtin: boolean;

  /**
   * @generated from field: bool enabled = 17;
   */
  enabled: boolean;
};

/**
 * Describes the message v1.NodeDefinition.
 * Use `create(NodeDefinitionSchema)` to create a new message.
 */
export const NodeDefinitionSchema: GenMessage<NodeDefinition> = /*@__PURE__*/
  messageDesc(file_v1_node_definition_service, 0);

/**
 * @generated from message v1.ListNodeDefinitionsRequest
 */
export type ListNodeDefinitionsRequest = Message<"v1.ListNodeDefinitionsRequest"> & {
};

/**
 * Describes the message v1.ListNodeDefinitionsRequest.
 * Use `create(ListNodeDefinitionsRequestSchema)` to create a new message.
 */
export const ListNodeDefinitionsRequestSchema: GenMessage<ListNodeDefinitionsRequest> = /*@__PURE__*/
  messageDesc(file_v1_node_definition_service, 1);

/**
 * @generated from message v1.ListNodeDefinitionsResponse
 */
export type ListNodeDefinitionsResponse = Message<"v1.ListNodeDefinitionsResponse"> & {
  /**
   * @generated from field: repeated v1.NodeDefinition node_definitions = 1;
   */
  nodeDefinitions: NodeDefinition[];
};

/**
 * Describes the message v1.ListNodeDefinitionsResponse.
 * Use `create(ListNodeDefinitionsResponseSchema)` to create a new message.
 */
export const ListNodeDefinitionsResponseSchema: GenMessage<ListNodeDefinitionsResponse> = /*@__PURE__*/
  messageDesc(file_v1_node_definition_service, 2);

/**
 * @generated from message v1.GetNodeDefinitionByTypeAndVersionRequest
 */
export type GetNodeDefinitionByTypeAndVersionRequest = Message<"v1.GetNodeDefinitionByTypeAndVersionRequest"> & {
  /**
   * @gotags: validate:"required" msg_required:"节点类型不能为空"
   *
   * @generated from field: string type = 1;
   */
  type: string;

  /**
   * @gotags: validate:"required" msg_required:"节点版本不能为空"
   *
   * @generated from field: string version = 2;
   */
  version: string;
};

/**
 * Describes the message v1.GetNodeDefinitionByTypeAndVersionRequest.
 * Use `create(GetNodeDefinitionByTypeAndVersionRequestSchema)` to create a new message.
 */
export const GetNodeDefinitionByTypeAndVersionRequestSchema: GenMessage<GetNodeDefinitionByTypeAndVersionRequest> = /*@__PURE__*/
  messageDesc(file_v1_node_definition_service, 3);

/**
 * @generated from message v1.GetNodeDefinitionByTypeAndVersionResponse
 */
export type GetNodeDefinitionByTypeAndVersionResponse = Message<"v1.GetNodeDefinitionByTypeAndVersionResponse"> & {
  /**
   * @generated from field: v1.NodeDefinition node_definition = 1;
   */
  nodeDefinition?: NodeDefinition;
};

/**
 * Describes the message v1.GetNodeDefinitionByTypeAndVersionResponse.
 * Use `create(GetNodeDefinitionByTypeAndVersionResponseSchema)` to create a new message.
 */
export const GetNodeDefinitionByTypeAndVersionResponseSchema: GenMessage<GetNodeDefinitionByTypeAndVersionResponse> = /*@__PURE__*/
  messageDesc(file_v1_node_definition_service, 4);

/**
 * @generated from service v1.NodeDefinitionService
 */
export const NodeDefinitionService: GenService<{
  /**
   * @generated from rpc v1.NodeDefinitionService.List
   */
  list: {
    methodKind: "unary";
    input: typeof ListNodeDefinitionsRequestSchema;
    output: typeof ListNodeDefinitionsResponseSchema;
  },
  /**
   * @generated from rpc v1.NodeDefinitionService.GetByTypeAndVersion
   */
  getByTypeAndVersion: {
    methodKind: "unary";
    input: typeof GetNodeDefinitionByTypeAndVersionRequestSchema;
    output: typeof GetNodeDefinitionByTypeAndVersionResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_v1_node_definition_service, 0);

