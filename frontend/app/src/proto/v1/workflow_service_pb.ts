// @generated by protoc-gen-es v2.6.2 with parameter "target=ts"
// @generated from file v1/workflow_service.proto (package v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_google_protobuf_struct } from "@bufbuild/protobuf/wkt";
import type { CommonResponseSchema, OnlyIdRequestSchema, Status } from "./common_pb";
import { file_v1_common } from "./common_pb";
import type { NodeParam, NodePort } from "./node_pb";
import { file_v1_node } from "./node_pb";
import type { User } from "./user_service_pb";
import { file_v1_user_service } from "./user_service_pb";
import type { JsonObject, Message } from "@bufbuild/protobuf";

/**
 * Describes the file v1/workflow_service.proto.
 */
export const file_v1_workflow_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_struct, file_v1_common, file_v1_node, file_v1_user_service]);

/**
 * @generated from message v1.Workflow
 */
export type Workflow = Message<"v1.Workflow"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: optional v1.User user = 2;
   */
  user?: User;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @generated from field: string icon = 4;
   */
  icon: string;

  /**
   * @generated from field: string description = 5;
   */
  description: string;

  /**
   * @generated from field: v1.Status status = 6;
   */
  status: Status;

  /**
   * @generated from field: v1.WorkflowViewport viewport = 7;
   */
  viewport?: WorkflowViewport;

  /**
   * @generated from field: repeated v1.WorkflowNode nodes = 8;
   */
  nodes: WorkflowNode[];

  /**
   * @generated from field: repeated v1.WorkflowLink links = 9;
   */
  links: WorkflowLink[];

  /**
   * @generated from field: string created_at = 20;
   */
  createdAt: string;

  /**
   * @generated from field: string updated_at = 21;
   */
  updatedAt: string;
};

/**
 * Describes the message v1.Workflow.
 * Use `create(WorkflowSchema)` to create a new message.
 */
export const WorkflowSchema: GenMessage<Workflow> = /*@__PURE__*/
  messageDesc(file_v1_workflow_service, 0);

/**
 * @generated from message v1.WorkflowViewport
 */
export type WorkflowViewport = Message<"v1.WorkflowViewport"> & {
  /**
   * @generated from field: float x = 1;
   */
  x: number;

  /**
   * @generated from field: float y = 2;
   */
  y: number;

  /**
   * @generated from field: float zoom = 3;
   */
  zoom: number;
};

/**
 * Describes the message v1.WorkflowViewport.
 * Use `create(WorkflowViewportSchema)` to create a new message.
 */
export const WorkflowViewportSchema: GenMessage<WorkflowViewport> = /*@__PURE__*/
  messageDesc(file_v1_workflow_service, 1);

/**
 * @generated from message v1.WorkflowNodePosition
 */
export type WorkflowNodePosition = Message<"v1.WorkflowNodePosition"> & {
  /**
   * @generated from field: float x = 1;
   */
  x: number;

  /**
   * @generated from field: float y = 2;
   */
  y: number;
};

/**
 * Describes the message v1.WorkflowNodePosition.
 * Use `create(WorkflowNodePositionSchema)` to create a new message.
 */
export const WorkflowNodePositionSchema: GenMessage<WorkflowNodePosition> = /*@__PURE__*/
  messageDesc(file_v1_workflow_service, 2);

/**
 * @generated from message v1.WorkflowNode
 */
export type WorkflowNode = Message<"v1.WorkflowNode"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: optional v1.Workflow workflow = 2;
   */
  workflow?: Workflow;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @generated from field: string description = 4;
   */
  description: string;

  /**
   * @generated from field: string icon = 5;
   */
  icon: string;

  /**
   * @generated from field: string type = 6;
   */
  type: string;

  /**
   * @generated from field: string version = 7;
   */
  version: string;

  /**
   * @generated from field: string plugin_name = 8;
   */
  pluginName: string;

  /**
   * @generated from field: string plugin_version = 9;
   */
  pluginVersion: string;

  /**
   * @generated from field: repeated v1.NodeParam input_params = 15;
   */
  inputParams: NodeParam[];

  /**
   * @generated from field: google.protobuf.Struct input_values = 16;
   */
  inputValues?: JsonObject;

  /**
   * @generated from field: repeated v1.NodeParam output_params = 17;
   */
  outputParams: NodeParam[];

  /**
   * @generated from field: google.protobuf.Struct output_values = 18;
   */
  outputValues?: JsonObject;

  /**
   * @generated from field: repeated v1.NodePort input_ports = 19;
   */
  inputPorts: NodePort[];

  /**
   * @generated from field: repeated v1.NodePort output_ports = 20;
   */
  outputPorts: NodePort[];

  /**
   * @generated from field: v1.WorkflowNodePosition position = 21;
   */
  position?: WorkflowNodePosition;

  /**
   * @generated from field: google.protobuf.Struct data = 22;
   */
  data?: JsonObject;

  /**
   * @generated from field: string created_at = 30;
   */
  createdAt: string;

  /**
   * @generated from field: string updated_at = 31;
   */
  updatedAt: string;
};

/**
 * Describes the message v1.WorkflowNode.
 * Use `create(WorkflowNodeSchema)` to create a new message.
 */
export const WorkflowNodeSchema: GenMessage<WorkflowNode> = /*@__PURE__*/
  messageDesc(file_v1_workflow_service, 3);

/**
 * @generated from message v1.WorkflowLink
 */
export type WorkflowLink = Message<"v1.WorkflowLink"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: optional v1.Workflow workflow = 2;
   */
  workflow?: Workflow;

  /**
   * @generated from field: string from_node_id = 3;
   */
  fromNodeId: string;

  /**
   * @generated from field: string to_node_id = 4;
   */
  toNodeId: string;

  /**
   * @generated from field: string from_port_id = 5;
   */
  fromPortId: string;

  /**
   * @generated from field: string to_port_id = 6;
   */
  toPortId: string;

  /**
   * @generated from field: string type = 7;
   */
  type: string;
};

/**
 * Describes the message v1.WorkflowLink.
 * Use `create(WorkflowLinkSchema)` to create a new message.
 */
export const WorkflowLinkSchema: GenMessage<WorkflowLink> = /*@__PURE__*/
  messageDesc(file_v1_workflow_service, 4);

/**
 * @generated from message v1.WorkflowNodeRequest
 */
export type WorkflowNodeRequest = Message<"v1.WorkflowNodeRequest"> & {
  /**
   * @gotags: validate:"required,uuid4"
   *
   * @generated from field: string id = 2;
   */
  id: string;

  /**
   * @gotags: validate:"required,max=50" msg_max:"节点名称不能超过50个字"
   *
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @gotags: validate:"omitempty,max=200" msg_max:"节点描述不能超过200个字"
   *
   * @generated from field: string description = 4;
   */
  description: string;

  /**
   * @gotags: validate:"omitempty"
   *
   * @generated from field: string icon = 5;
   */
  icon: string;

  /**
   * @gotags: validate:"required" msg_required:"节点类型不能为空"
   *
   * @generated from field: string type = 6;
   */
  type: string;

  /**
   * @gotags: validate:"required" msg_required:"节点版本不能为空"
   *
   * @generated from field: string version = 7;
   */
  version: string;

  /**
   * @gotags: validate:"omitempty,required"
   *
   * @generated from field: optional string plugin_name = 8;
   */
  pluginName?: string;

  /**
   * @gotags: validate:"omitempty,required"
   *
   * @generated from field: optional string plugin_version = 9;
   */
  pluginVersion?: string;

  /**
   * @gotags: validate:"omitempty,dive,required"
   *
   * @generated from field: repeated v1.NodeParam input_params = 15;
   */
  inputParams: NodeParam[];

  /**
   * @gotags: validate:"omitempty,required"
   *
   * @generated from field: google.protobuf.Struct input_values = 16;
   */
  inputValues?: JsonObject;

  /**
   * @gotags: validate:"omitempty,dive,required"
   *
   * @generated from field: repeated v1.NodeParam output_params = 17;
   */
  outputParams: NodeParam[];

  /**
   * @gotags: validate:"omitempty,required"
   *
   * @generated from field: google.protobuf.Struct output_values = 18;
   */
  outputValues?: JsonObject;

  /**
   * @gotags: validate:"omitempty,dive,required"
   *
   * @generated from field: repeated v1.NodePort input_ports = 19;
   */
  inputPorts: NodePort[];

  /**
   * @gotags: validate:"omitempty,dive,required"
   *
   * @generated from field: repeated v1.NodePort output_ports = 20;
   */
  outputPorts: NodePort[];

  /**
   * @gotags: validate:"required"
   *
   * @generated from field: v1.WorkflowNodePosition position = 21;
   */
  position?: WorkflowNodePosition;

  /**
   * @gotags: validate:"required"
   *
   * @generated from field: google.protobuf.Struct data = 22;
   */
  data?: JsonObject;
};

/**
 * Describes the message v1.WorkflowNodeRequest.
 * Use `create(WorkflowNodeRequestSchema)` to create a new message.
 */
export const WorkflowNodeRequestSchema: GenMessage<WorkflowNodeRequest> = /*@__PURE__*/
  messageDesc(file_v1_workflow_service, 5);

/**
 * @generated from message v1.WorkflowLinkRequest
 */
export type WorkflowLinkRequest = Message<"v1.WorkflowLinkRequest"> & {
  /**
   * @gotags: validate:"required,uuid4"
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @gotags: validate:"required,uuid4" msg_required:"来源节点不能为空" msg_uuid4:"来源节点id错误"
   *
   * @generated from field: string from_node_id = 3;
   */
  fromNodeId: string;

  /**
   * @gotags: validate:"required,uuid4" msg_required:"目标节点不能为空" msg_uuid4:"目标节点id错误"
   *
   * @generated from field: string to_node_id = 4;
   */
  toNodeId: string;

  /**
   * @generated from field: string from_port_id = 5;
   */
  fromPortId: string;

  /**
   * @generated from field: string to_port_id = 6;
   */
  toPortId: string;

  /**
   * @gotags: validate:"required" msg_required:"边类型错误"
   *
   * @generated from field: string type = 7;
   */
  type: string;
};

/**
 * Describes the message v1.WorkflowLinkRequest.
 * Use `create(WorkflowLinkRequestSchema)` to create a new message.
 */
export const WorkflowLinkRequestSchema: GenMessage<WorkflowLinkRequest> = /*@__PURE__*/
  messageDesc(file_v1_workflow_service, 6);

/**
 * @generated from message v1.CreateWorkflowRequest
 */
export type CreateWorkflowRequest = Message<"v1.CreateWorkflowRequest"> & {
  /**
   * @gotags: validate:"required,max=50" label:"名称" msg_required:"请填写名称" msg_max:"名称不能超过50个字"
   *
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @gotags: validate:"omitempty,max=200" msg_max:"描述不能超过200个字"
   *
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @gotags: validate:"omitempty"
   *
   * @generated from field: string icon = 3;
   */
  icon: string;

  /**
   * @gotags: validate:"omitempty,dive,required"
   *
   * @generated from field: repeated v1.WorkflowNodeRequest nodes = 4;
   */
  nodes: WorkflowNodeRequest[];

  /**
   * @gotags: validate:"omitempty,dive,required"
   *
   * @generated from field: repeated v1.WorkflowLinkRequest links = 5;
   */
  links: WorkflowLinkRequest[];

  /**
   * @generated from field: v1.WorkflowViewport viewport = 6;
   */
  viewport?: WorkflowViewport;
};

/**
 * Describes the message v1.CreateWorkflowRequest.
 * Use `create(CreateWorkflowRequestSchema)` to create a new message.
 */
export const CreateWorkflowRequestSchema: GenMessage<CreateWorkflowRequest> = /*@__PURE__*/
  messageDesc(file_v1_workflow_service, 7);

/**
 * @generated from message v1.WorkflowResponse
 */
export type WorkflowResponse = Message<"v1.WorkflowResponse"> & {
  /**
   * @generated from field: v1.Workflow workflow = 1;
   */
  workflow?: Workflow;
};

/**
 * Describes the message v1.WorkflowResponse.
 * Use `create(WorkflowResponseSchema)` to create a new message.
 */
export const WorkflowResponseSchema: GenMessage<WorkflowResponse> = /*@__PURE__*/
  messageDesc(file_v1_workflow_service, 8);

/**
 * @generated from message v1.UpdateWorkflowRequest
 */
export type UpdateWorkflowRequest = Message<"v1.UpdateWorkflowRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @gotags: validate:"required,max=50" label:"名称" msg_required:"请填写名称" msg_max:"名称不能超过50个字"
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @gotags: validate:"omitempty,dive,required"
   *
   * @generated from field: repeated v1.WorkflowNodeRequest nodes = 3;
   */
  nodes: WorkflowNodeRequest[];

  /**
   * @gotags: validate:"omitempty,dive,required"
   *
   * @generated from field: repeated v1.WorkflowLinkRequest links = 4;
   */
  links: WorkflowLinkRequest[];

  /**
   * @generated from field: v1.WorkflowViewport viewport = 5;
   */
  viewport?: WorkflowViewport;
};

/**
 * Describes the message v1.UpdateWorkflowRequest.
 * Use `create(UpdateWorkflowRequestSchema)` to create a new message.
 */
export const UpdateWorkflowRequestSchema: GenMessage<UpdateWorkflowRequest> = /*@__PURE__*/
  messageDesc(file_v1_workflow_service, 9);

/**
 * @generated from message v1.ListWorkflowsRequest
 */
export type ListWorkflowsRequest = Message<"v1.ListWorkflowsRequest"> & {
};

/**
 * Describes the message v1.ListWorkflowsRequest.
 * Use `create(ListWorkflowsRequestSchema)` to create a new message.
 */
export const ListWorkflowsRequestSchema: GenMessage<ListWorkflowsRequest> = /*@__PURE__*/
  messageDesc(file_v1_workflow_service, 10);

/**
 * @generated from message v1.ListWorkflowsResponse
 */
export type ListWorkflowsResponse = Message<"v1.ListWorkflowsResponse"> & {
  /**
   * @generated from field: repeated v1.Workflow workflows = 1;
   */
  workflows: Workflow[];
};

/**
 * Describes the message v1.ListWorkflowsResponse.
 * Use `create(ListWorkflowsResponseSchema)` to create a new message.
 */
export const ListWorkflowsResponseSchema: GenMessage<ListWorkflowsResponse> = /*@__PURE__*/
  messageDesc(file_v1_workflow_service, 11);

/**
 * @generated from enum v1.WorkflowState
 */
export enum WorkflowState {
  /**
   * @generated from enum value: CLOSE = 0;
   */
  CLOSE = 0,

  /**
   * @generated from enum value: OPEN = 1;
   */
  OPEN = 1,
}

/**
 * Describes the enum v1.WorkflowState.
 */
export const WorkflowStateSchema: GenEnum<WorkflowState> = /*@__PURE__*/
  enumDesc(file_v1_workflow_service, 0);

/**
 * @generated from service v1.WorkflowService
 */
export const WorkflowService: GenService<{
  /**
   * @generated from rpc v1.WorkflowService.GetById
   */
  getById: {
    methodKind: "unary";
    input: typeof OnlyIdRequestSchema;
    output: typeof WorkflowResponseSchema;
  },
  /**
   * @generated from rpc v1.WorkflowService.Create
   */
  create: {
    methodKind: "unary";
    input: typeof CreateWorkflowRequestSchema;
    output: typeof WorkflowResponseSchema;
  },
  /**
   * @generated from rpc v1.WorkflowService.Update
   */
  update: {
    methodKind: "unary";
    input: typeof UpdateWorkflowRequestSchema;
    output: typeof WorkflowResponseSchema;
  },
  /**
   * @generated from rpc v1.WorkflowService.DeleteById
   */
  deleteById: {
    methodKind: "unary";
    input: typeof OnlyIdRequestSchema;
    output: typeof CommonResponseSchema;
  },
  /**
   * @generated from rpc v1.WorkflowService.List
   */
  list: {
    methodKind: "unary";
    input: typeof ListWorkflowsRequestSchema;
    output: typeof ListWorkflowsResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_v1_workflow_service, 0);

