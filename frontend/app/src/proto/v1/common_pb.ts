// @generated by protoc-gen-es v2.6.2 with parameter "target=ts"
// @generated from file v1/common.proto (package v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file v1/common.proto.
 */
export const file_v1_common: GenFile = /*@__PURE__*/
  fileDesc("Cg92MS9jb21tb24ucHJvdG8SAnYxIi8KDkNvbW1vblJlc3BvbnNlEgwKBGNvZGUYASABKAUSDwoHbWVzc2FnZRgCIAEoCSIbCg1Pbmx5SWRSZXF1ZXN0EgoKAmlkGAEgASgJKiIKBlN0YXR1cxIMCghJTkFDVElWRRAAEgoKBkFDVElWRRABQh9aHXJlc2Zsb3cvcHJvdG8vZ2VuZXJhdGVkX2dvL3YxYgZwcm90bzM");

/**
 * @generated from message v1.CommonResponse
 */
export type CommonResponse = Message<"v1.CommonResponse"> & {
  /**
   * @generated from field: int32 code = 1;
   */
  code: number;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message v1.CommonResponse.
 * Use `create(CommonResponseSchema)` to create a new message.
 */
export const CommonResponseSchema: GenMessage<CommonResponse> = /*@__PURE__*/
  messageDesc(file_v1_common, 0);

/**
 * @generated from message v1.OnlyIdRequest
 */
export type OnlyIdRequest = Message<"v1.OnlyIdRequest"> & {
  /**
   * @gotags: validate:"required" msg_required:"id不能为空"
   *
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message v1.OnlyIdRequest.
 * Use `create(OnlyIdRequestSchema)` to create a new message.
 */
export const OnlyIdRequestSchema: GenMessage<OnlyIdRequest> = /*@__PURE__*/
  messageDesc(file_v1_common, 1);

/**
 * @generated from enum v1.Status
 */
export enum Status {
  /**
   * @generated from enum value: INACTIVE = 0;
   */
  INACTIVE = 0,

  /**
   * @generated from enum value: ACTIVE = 1;
   */
  ACTIVE = 1,
}

/**
 * Describes the enum v1.Status.
 */
export const StatusSchema: GenEnum<Status> = /*@__PURE__*/
  enumDesc(file_v1_common, 0);

