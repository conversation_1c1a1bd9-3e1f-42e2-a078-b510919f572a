// @generated by protoc-gen-es v2.6.2 with parameter "target=ts"
// @generated from file v1/plugin_service.proto (package v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file v1/plugin_service.proto.
 */
export const file_v1_plugin_service: GenFile = /*@__PURE__*/
  fileDesc("Chd2MS9wbHVnaW5fc2VydmljZS5wcm90bxICdjEi1AEKBlBsdWdpbhIKCgJpZBgBIAEoCRIMCgRuYW1lGAIgASgJEg8KB3ZlcnNpb24YAyABKAkSDgoGYXV0aG9yGAQgASgJEhQKDGRpc3BsYXlfbmFtZRgFIAEoCRITCgtkZXNjcmlwdGlvbhgGIAEoCRIMCgRpY29uGAcgASgJEgwKBHBhdGgYCCABKAkSDwoHYnVpbHRpbhgJIAEoCBIPCgdlbmFibGVkGAogASgIEhIKCmNyZWF0ZWRfYXQYFCABKAkSEgoKdXBkYXRlZF9hdBgVIAEoCSIUChJMaXN0UGx1Z2luc1JlcXVlc3QiMgoTTGlzdFBsdWdpbnNSZXNwb25zZRIbCgdwbHVnaW5zGAEgAygLMgoudjEuUGx1Z2luMkgKDVBsdWdpblNlcnZpY2USNwoETGlzdBIWLnYxLkxpc3RQbHVnaW5zUmVxdWVzdBoXLnYxLkxpc3RQbHVnaW5zUmVzcG9uc2VCH1odcmVzZmxvdy9wcm90by9nZW5lcmF0ZWRfZ28vdjFiBnByb3RvMw");

/**
 * @generated from message v1.Plugin
 */
export type Plugin = Message<"v1.Plugin"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string version = 3;
   */
  version: string;

  /**
   * @generated from field: string author = 4;
   */
  author: string;

  /**
   * @generated from field: string display_name = 5;
   */
  displayName: string;

  /**
   * @generated from field: string description = 6;
   */
  description: string;

  /**
   * @generated from field: string icon = 7;
   */
  icon: string;

  /**
   * @generated from field: string path = 8;
   */
  path: string;

  /**
   * @generated from field: bool builtin = 9;
   */
  builtin: boolean;

  /**
   * @generated from field: bool enabled = 10;
   */
  enabled: boolean;

  /**
   * @generated from field: string created_at = 20;
   */
  createdAt: string;

  /**
   * @generated from field: string updated_at = 21;
   */
  updatedAt: string;
};

/**
 * Describes the message v1.Plugin.
 * Use `create(PluginSchema)` to create a new message.
 */
export const PluginSchema: GenMessage<Plugin> = /*@__PURE__*/
  messageDesc(file_v1_plugin_service, 0);

/**
 * @generated from message v1.ListPluginsRequest
 */
export type ListPluginsRequest = Message<"v1.ListPluginsRequest"> & {
};

/**
 * Describes the message v1.ListPluginsRequest.
 * Use `create(ListPluginsRequestSchema)` to create a new message.
 */
export const ListPluginsRequestSchema: GenMessage<ListPluginsRequest> = /*@__PURE__*/
  messageDesc(file_v1_plugin_service, 1);

/**
 * @generated from message v1.ListPluginsResponse
 */
export type ListPluginsResponse = Message<"v1.ListPluginsResponse"> & {
  /**
   * @generated from field: repeated v1.Plugin plugins = 1;
   */
  plugins: Plugin[];
};

/**
 * Describes the message v1.ListPluginsResponse.
 * Use `create(ListPluginsResponseSchema)` to create a new message.
 */
export const ListPluginsResponseSchema: GenMessage<ListPluginsResponse> = /*@__PURE__*/
  messageDesc(file_v1_plugin_service, 2);

/**
 * @generated from service v1.PluginService
 */
export const PluginService: GenService<{
  /**
   * @generated from rpc v1.PluginService.List
   */
  list: {
    methodKind: "unary";
    input: typeof ListPluginsRequestSchema;
    output: typeof ListPluginsResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_v1_plugin_service, 0);

