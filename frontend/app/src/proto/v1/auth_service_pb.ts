// @generated by protoc-gen-es v2.6.2 with parameter "target=ts"
// @generated from file v1/auth_service.proto (package v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { User } from "./user_service_pb";
import { file_v1_user_service } from "./user_service_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file v1/auth_service.proto.
 */
export const file_v1_auth_service: GenFile = /*@__PURE__*/
  fileDesc("ChV2MS9hdXRoX3NlcnZpY2UucHJvdG8SAnYxIjIKDExvZ2luUmVxdWVzdBIQCgh1c2VybmFtZRgBIAEoCRIQCghwYXNzd29yZBgCIAEoCSI2Cg1Mb2dpblJlc3BvbnNlEhYKBHVzZXIYASABKAsyCC52MS5Vc2VyEg0KBXRva2VuGAIgASgJKigKC0F1dGhFcnJDb2RlEgYKAk9LEAASEQoMUEFTU1dPUkRfRVJSEOgHMjsKC0F1dGhTZXJ2aWNlEiwKBUxvZ2luEhAudjEuTG9naW5SZXF1ZXN0GhEudjEuTG9naW5SZXNwb25zZUIfWh1yZXNmbG93L3Byb3RvL2dlbmVyYXRlZF9nby92MWIGcHJvdG8z", [file_v1_user_service]);

/**
 * @generated from message v1.LoginRequest
 */
export type LoginRequest = Message<"v1.LoginRequest"> & {
  /**
   * @generated from field: string username = 1;
   */
  username: string;

  /**
   * @generated from field: string password = 2;
   */
  password: string;
};

/**
 * Describes the message v1.LoginRequest.
 * Use `create(LoginRequestSchema)` to create a new message.
 */
export const LoginRequestSchema: GenMessage<LoginRequest> = /*@__PURE__*/
  messageDesc(file_v1_auth_service, 0);

/**
 * @generated from message v1.LoginResponse
 */
export type LoginResponse = Message<"v1.LoginResponse"> & {
  /**
   * @generated from field: v1.User user = 1;
   */
  user?: User;

  /**
   * @generated from field: string token = 2;
   */
  token: string;
};

/**
 * Describes the message v1.LoginResponse.
 * Use `create(LoginResponseSchema)` to create a new message.
 */
export const LoginResponseSchema: GenMessage<LoginResponse> = /*@__PURE__*/
  messageDesc(file_v1_auth_service, 1);

/**
 * @generated from enum v1.AuthErrCode
 */
export enum AuthErrCode {
  /**
   * @generated from enum value: OK = 0;
   */
  OK = 0,

  /**
   * @generated from enum value: PASSWORD_ERR = 1000;
   */
  PASSWORD_ERR = 1000,
}

/**
 * Describes the enum v1.AuthErrCode.
 */
export const AuthErrCodeSchema: GenEnum<AuthErrCode> = /*@__PURE__*/
  enumDesc(file_v1_auth_service, 0);

/**
 * @generated from service v1.AuthService
 */
export const AuthService: GenService<{
  /**
   * @generated from rpc v1.AuthService.Login
   */
  login: {
    methodKind: "unary";
    input: typeof LoginRequestSchema;
    output: typeof LoginResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_v1_auth_service, 0);

