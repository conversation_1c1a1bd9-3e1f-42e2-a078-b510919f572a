import { createStore } from "zustand/vanilla";
import { UserViewModel } from "@resflow/user";
import { SessionViewModel } from "../../../packages/session/src/viewmodel/session.ts";

export interface SessionStoreType {
  session: SessionViewModel | null;
  setSession: (session: SessionViewModel) => void;
  user: () => UserViewModel | undefined;
}

export const createSessionStore = (session: SessionViewModel | null = null) => {
  return createStore<SessionStoreType>((set, get) => ({
    session: session,
    setSession: (session) => set({ session }),
    user: () => get().session?.user,
  }));
};
