import { create<PERSON>ontext, use<PERSON>ontext, useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router";
import { useDependency } from "@wendellhu/redi/react-bindings";
import { SessionController } from "@resflow/session";
import { SessionViewModel } from "../../../packages/session/src/viewmodel/session.ts";
import { UserViewModel } from "@resflow/user";

interface SessionContextType {
  session: SessionViewModel | null;
  user: UserViewModel | undefined;
  loading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
}

const SessionContext = createContext<SessionContextType | undefined>(undefined);

export const SessionProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [session, setSession] = useState<SessionViewModel | null>(null);
  const navigate = useNavigate();
  const [loading, setLoading] = useState<boolean>(true);
  const sessionController = useDependency(SessionController);

  useEffect(() => {
    const restoreSession = async () => {
      try {
        const session = await sessionController.restore();
        setSession(session);
      } catch (err) {
        console.log((err as Error).message);
        navigate("/login");
      } finally {
        setLoading(false);
      }
    };

    restoreSession();
  }, []);

  async function login(username: string, password: string) {
    const session = await sessionController.create({ username, password });
    setSession(session);
  }

  function logout() {
    sessionController.destroy();
    setSession(null);
  }

  const user = useMemo(() => session?.user, [session]);

  return (
    <SessionContext value={{ session, user, loading, login, logout }}>
      {children}
    </SessionContext>
  );
};

export function useSession() {
  const context = useContext(SessionContext);
  if (!context) {
    throw new Error("useSession must be used within a SessionProvider");
  }
  return context;
}
