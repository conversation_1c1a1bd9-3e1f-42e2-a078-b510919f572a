import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>er,
  IndexRouteObject,
  Navigate,
  NonIndexRouteObject,
  Outlet,
} from "react-router";
import { LoginPage } from "@/features/login";
import { Layout } from "@/components/layout";
import { Dashboard } from "@/features/dashboard";
import { NotFound } from "@/features/not-found";
import { PublicRoute, RequireAuth } from "@/guards/route-guards.tsx";
import { SessionProvider } from "@/contexts/session-context.tsx";
import { RunningWorkflows } from "@/features/workflow/pages/running-workflows.tsx";
import { WorkflowList } from "@/features/workflow/pages/workflow-list.tsx";
import { WorkflowEditor } from "@/features/workflow/pages/workflow-editor.tsx";
import { PluginIndex } from "@/features/plugin/pages/plugin-index.tsx";

export interface HandleType {
  breadcrumb: boolean;
  title?: string;
  breadcrumbDisabled?: boolean;
}

type CustomIndexRouteObject = Omit<IndexRouteObject, "handle"> & {
  handle?: HandleType;
};

type CustomNonIndexRouteObject = Omit<
  NonIndexRouteObject,
  "handle" | "children"
> & {
  handle?: HandleType;
  children?: Array<CustomRouteObject>;
};

type CustomRouteObject = CustomIndexRouteObject | CustomNonIndexRouteObject;

const routes: CustomRouteObject[] = [
  {
    errorElement: <NotFound />,
    element: (
      <SessionProvider>
        <Outlet />
      </SessionProvider>
    ),
    children: [
      {
        path: "login",
        element: (
          <PublicRoute>
            <LoginPage />
          </PublicRoute>
        ),
      },
      {
        path: "workflow/:workflowId",
        element: (
          <RequireAuth>
            <WorkflowEditor />
          </RequireAuth>
        ),
        handle: {
          title: "创建工作流",
          breadcrumb: true,
        },
      },
      {
        path: "/",
        element: (
          <RequireAuth>
            <Layout />
          </RequireAuth>
        ),
        children: [
          {
            index: true,
            element: <Dashboard />,
          },
          {
            path: "workflow",
            handle: {
              title: "工作流",
              breadcrumb: true,
              breadcrumbDisabled: true,
            },
            children: [
              {
                index: true,
                element: <Navigate to="/workflow/index" />,
              },
              {
                path: "index",
                element: <WorkflowList />,
                handle: {
                  title: "所有工作流",
                  breadcrumb: true,
                },
              },
              {
                path: "running",
                element: <RunningWorkflows />,
                handle: {
                  title: "正在运行",
                  breadcrumb: true,
                },
              },
            ],
          },
          {
            path: "plugin",
            handle: {
              title: "插件",
              breadcrumb: true,
              breadcrumbDisabled: true,
            },
            children: [
              {
                path: "index",
                element: <PluginIndex />,
                handle: {
                  title: "插件管理",
                  breadcrumb: true,
                },
              },
            ],
          },
        ],
      },
    ],
  },
];

const router = createBrowserRouter(routes);

export default router;
