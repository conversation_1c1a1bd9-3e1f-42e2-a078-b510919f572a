import { Navigate } from "react-router";
import { useSession } from "@/contexts/session-context.tsx";
import PageLoading from "@/components/page-loading.tsx";
import React from "react";

export const PublicRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { session, loading } = useSession();
  if (loading) {
    return <PageLoading />;
  }
  if (session) {
    return <Navigate to="/" />;
  }
  return <>{children}</>;
};

export const RequireAuth: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const { session, loading } = useSession();
  if (loading) {
    return <PageLoading />;
  }
  if (!session) {
    return <Navigate to="/login" />;
  }
  return <>{children}</>;
};
