import { connectDependencies } from "@wendellhu/redi/react-bindings";
import { CoreDependencies } from "@resflow/core";
import { AuthDependencies, ITokenStorage } from "../../packages/session";
import {
  ApiDependencies,
  ErrorInterceptor,
  IClient,
  ITokenInterceptor,
  TokenInterceptor,
} from "@resflow/api";
import { HttpClient, Interceptor } from "@resflow-infra/http-client";
import { ComponentType } from "react";
import { UserDependencies } from "@resflow/user";

export function createContainer(app: ComponentType) {
  return connectDependencies(app, [
    ...CoreDependencies,
    ...AuthDependencies,
    [
      ITokenInterceptor,
      {
        useFactory: (tokenStorage: ITokenStorage) =>
          new TokenInterceptor(tokenStorage).getInterceptor(),
        deps: [ITokenStorage],
      },
    ],
    [
      IClient,
      {
        useFactory: (tokenInterceptor: Interceptor) =>
          new HttpClient([
            tokenInterceptor,
            new ErrorInterceptor().getInterceptor(),
          ]),
        deps: [ITokenInterceptor],
      },
    ],
    ...ApiDependencies,
    ...UserDependencies,
  ]);
}
