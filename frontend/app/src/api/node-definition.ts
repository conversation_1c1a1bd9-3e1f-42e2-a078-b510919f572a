import {
  GetNodeDefinitionByTypeAndVersionRequest,
  GetNodeDefinitionByTypeAndVersionResponse,
  ListNodeDefinitionsRequest,
  ListNodeDefinitionsResponse,
  NodeDefinition,
  NodeDefinitionService,
} from "@/proto/v1/node_definition_service_pb.ts";
import { NodeParam as NodeParamPb } from "@/proto/v1/node_pb.ts";

import { CreateClient } from "@/lib/grpc.ts";
import {
  NodeParam,
  NodeParamType,
  NodeSchema,
} from "@/components/workflow/types/workflow.ts";
import { useGrpcRequestWithToken } from "@/hooks/use-grpc-request.ts";

const client = CreateClient(NodeDefinitionService);

function convertNodeDefinitionToNodeSchema(
  nodeDefinition: NodeDefinition,
): NodeSchema {
  const inputs_params = nodeDefinition.inputParams.map((param) => {
    return convertNodeParamToWorkflowNodeParam(param);
  });
  const outputs_params = nodeDefinition.outputParams.map((param) => {
    return convertNodeParamToWorkflowNodeParam(param);
  });

  return {
    id: nodeDefinition.id,
    name: nodeDefinition.name,
    author: nodeDefinition.author,
    description: nodeDefinition.description,
    icon: nodeDefinition.icon,
    type: nodeDefinition.type,
    version: nodeDefinition.version,
    category: nodeDefinition.category,
    input_params: inputs_params,
    output_params: outputs_params,
    input_ports: nodeDefinition.inputPorts,
    output_ports: nodeDefinition.outputPorts,
    exception: false,
  };
}

function convertNodeParamToWorkflowNodeParam(
  nodeParam: NodeParamPb,
): NodeParam {
  return {
    id: nodeParam.id,
    label: nodeParam.label,
    description: nodeParam.description,
    type: nodeParam.type as NodeParamType,
    value: nodeParam.value,
    required: nodeParam.required,
  };
}

export function useListNodeDefinitions() {
  const [request, loading] = useGrpcRequestWithToken<
    ListNodeDefinitionsRequest,
    ListNodeDefinitionsResponse
  >(client.list);
  const requestAndTransform = async () => {
    const response = await request({});
    const nodeSchemas: NodeSchema[] = [];
    for (const nodeDefinition of response.nodeDefinitions) {
      nodeSchemas.push(convertNodeDefinitionToNodeSchema(nodeDefinition));
    }
    return nodeSchemas;
  };
  return [requestAndTransform, loading] as const;
}

export function useGetNodeDefinitionByTypeAndVersion() {
  const [request, loading] = useGrpcRequestWithToken<
    GetNodeDefinitionByTypeAndVersionRequest,
    GetNodeDefinitionByTypeAndVersionResponse
  >(client.getByTypeAndVersion);
  const requestAndTransform = async (type: string, version: string) => {
    const response = await request({ type, version });
    return convertNodeDefinitionToNodeSchema(response.nodeDefinition!);
  };
  return [requestAndTransform, loading] as const;
}
