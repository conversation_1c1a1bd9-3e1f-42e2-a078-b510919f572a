import {
  CreateWorkflowRequest,
  Workflow,
  WorkflowResponse,
  WorkflowService,
} from "@/proto/v1/workflow_service_pb.ts";
import {
  ChangeEventHandler,
  Ref,
  useCallback,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { useClient } from "@/hooks/use-client.ts";
import { useGrpcRequestWithToken } from "@/hooks/use-grpc-request.ts";
import { useNavigate } from "react-router";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog.tsx";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar.tsx";
import { Image, Loader2Icon } from "lucide-react";
import { Input } from "@/components/ui/input.tsx";
import { Textarea } from "@/components/ui/textarea.tsx";
import { Button } from "@/components/ui/button.tsx";
import * as z from "zod/v4";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { file2base64 } from "@/lib/file.ts";
import { errorToast } from "@/lib/toast.ts";

export interface CreateDialogRefObject {
  open: (workflow?: Workflow) => void;
}

interface CreateDialogProps {
  ref: Ref<CreateDialogRefObject>;
}

const formSchema = z
  .object({
    name: z
      .string()
      .min(1, "工作流名称不能为空")
      .max(50, "工作流名称不能超过50个字符"),
    description: z.string().max(200, "描述不能超过200个字符"),
    icon: z.string(),
  })
  .required();

export function CreateDialog({ ref }: CreateDialogProps) {
  const [open, setOpen] = useState(false);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      icon: "",
    },
  });
  const avatarInputRef = useRef<HTMLInputElement>(null);
  const workflowClient = useClient(WorkflowService);
  const [requestCreateWorkflow, createLoading] = useGrpcRequestWithToken<
    CreateWorkflowRequest,
    WorkflowResponse
  >(workflowClient.create);
  const navigate = useNavigate();

  useImperativeHandle(ref, () => ({
    open(workflow?: Workflow) {
      setOpen(true);
      form.setValue("name", workflow?.name || "");
      form.setValue("description", workflow?.description || "");
      form.setValue("icon", workflow?.icon || "");
    },
  }));

  const handleAvatarClick = useCallback(() => {
    avatarInputRef.current?.click();
  }, []);

  const handleAvatarInputChange = useCallback<
    ChangeEventHandler<HTMLInputElement>
  >(
    async (e) => {
      if (e.target.files?.length) {
        const fileBase64String = await file2base64(e.target.files[0]);
        form.setValue("icon", fileBase64String);
      }
    },
    [form],
  );

  const handleClose = useCallback(() => {
    setOpen(false);
    form.reset();
  }, [form]);

  const onSubmit = useCallback(
    async (values: z.infer<typeof formSchema>) => {
      try {
        const res = await requestCreateWorkflow({
          ...values,
          nodes: [],
          links: [],
        });
        navigate(`/workflow/${res.workflow!.id}`);
      } catch (err) {
        console.log(err);
        errorToast((err as Error).message);
      }
    },
    [navigate, requestCreateWorkflow],
  );

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent onInteractOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>创建工作流</DialogTitle>
          <VisuallyHidden>
            <DialogDescription>创建工作流</DialogDescription>
          </VisuallyHidden>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="flex items-center">
              <FormField
                control={form.control}
                name="icon"
                render={({ field }) => (
                  <Avatar
                    className="rounded-lg w-32 h-32 mr-4 cursor-pointer"
                    onClick={handleAvatarClick}
                  >
                    <AvatarImage alt="图标" src={field.value} />
                    <AvatarFallback className="rounded-lg">
                      <Image />
                    </AvatarFallback>
                    <input
                      type="file"
                      hidden
                      ref={avatarInputRef}
                      onChange={handleAvatarInputChange}
                    />
                  </Avatar>
                )}
              />

              <div className="flex flex-col flex-1 h-32 justify-between">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input placeholder="工作流名称" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Textarea
                          className="h-20"
                          placeholder="描述"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <div className="flex justify-end gap-4">
              <Button type="button" variant="secondary" onClick={handleClose}>
                取消
              </Button>
              <Button type="submit" disabled={createLoading}>
                {createLoading && <Loader2Icon className="animate-spin" />}
                创建
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
