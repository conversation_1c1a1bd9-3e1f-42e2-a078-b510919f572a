import { Workflow, WorkflowService } from "@/proto/v1/workflow_service_pb.ts";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip.tsx";
import { But<PERSON> } from "@/components/ui/button.tsx";
import { RiPlayLine, RiShare2Line, RiToolsLine } from "@remixicon/react";
import { Badge } from "@/components/ui/badge.tsx";
import * as React from "react";
import {
  MouseEventHandler,
  Ref,
  useCallback,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { cn } from "@/lib/utils.ts";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Copy,
  Ellipsis,
  Image,
  Loader2Icon,
  Pencil,
  Tag,
  Tags,
  Trash2,
} from "lucide-react";
import {
  CreateDialog,
  CreateDialogRefObject,
} from "@/features/workflow/components/create-dialog.tsx";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import dayjs from "dayjs";
import { useNavigate } from "react-router";
import { Tag as WorkflowTag } from "@/types/workflow";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useClient } from "@/hooks/use-client.ts";
import { useGrpcRequestWithToken } from "@/hooks/use-grpc-request.ts";
import { CommonResponse, OnlyIdRequest } from "@/proto/v1/common_pb.ts";
import { successToast } from "@/lib/toast.ts";

export interface WorkflowItemProps {
  workflow: Workflow;
  onDeleted?: () => void;
}

export function WorkflowItem({
  workflow,
  onDeleted,
  className,
}: WorkflowItemProps & React.ComponentProps<"div">) {
  const createDialogRef = useRef<CreateDialogRefObject>(null);
  const deleteAlertRef = useRef<DeleteAlertRefObject>(null);
  const [isMenuOpen, setMenuOpen] = useState(false);
  const navigate = useNavigate();
  const tags: WorkflowTag[] = [];

  const handleDuplicate = useCallback(() => {
    createDialogRef.current?.open(workflow);
  }, [workflow]);

  const handleConfigClick = useCallback(() => {
    navigate(`/workflow/${workflow.id}`);
  }, [navigate, workflow.id]);

  const handleDeleteClick = useCallback(() => {
    setTimeout(() => deleteAlertRef.current?.open(workflow), 10);
  }, [workflow]);

  return (
    <div className={cn("p-2", className)}>
      <section className="flex flex-col rounded-lg border px-4 py-2 overflow-hidden group">
        <header className="flex w-full items-center">
          <div className="flex items-center flex-1 min-w-0">
            <Avatar className="rounded-lg mr-2">
              <AvatarImage src={workflow.icon} alt={workflow.name} />
              <AvatarFallback className="rounded-lg">
                <Image size={18} />
              </AvatarFallback>
            </Avatar>
            <span className="text-sm truncate mx-1">{workflow.name}</span>
          </div>
          <div
            className={cn(
              "operations flex items-center gap-2 transition-opacity duration-200",
              "opacity-0 pointer-events-none group-hover:opacity-100 group-hover:pointer-events-auto",
              isMenuOpen && "opacity-100 pointer-events-auto",
            )}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm">
                  <RiPlayLine />
                </Button>
              </TooltipTrigger>
              <TooltipContent>运行</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={handleConfigClick}>
                  <RiToolsLine />
                </Button>
              </TooltipTrigger>
              <TooltipContent>配置</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm">
                  <RiShare2Line />
                </Button>
              </TooltipTrigger>
              <TooltipContent>分享</TooltipContent>
            </Tooltip>
            <DropdownMenu onOpenChange={setMenuOpen}>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Ellipsis />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-32" align="end" sideOffset={10}>
                <DropdownMenuItem onClick={handleDuplicate}>
                  <Copy />
                  创建副本
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Pencil />
                  编辑信息
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Tags />
                  编辑标签
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleDeleteClick}>
                  <Trash2 className="text-destructive" />
                  <span className="text-destructive">删除</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>
        <section className="w-full h-20 my-2 line-clamp-4">
          <div className="text-muted-foreground text-sm">
            {workflow.description}
          </div>
        </section>
        <footer className="flex gap-2">
          <div className="flex flex-1 px-2 gap-2 shrink overflow-scroll">
            <Badge variant="secondary" className="cursor-pointer">
              <Tag />
              添加
            </Badge>
            {tags.map((tag) => (
              <Badge key={tag.name} style={{ background: tag.color }}>
                <Tag />
                {tag.name}
              </Badge>
            ))}
          </div>
          <Tooltip>
            <TooltipTrigger>
              <span className="text-xs text-muted-foreground">
                编辑于{dayjs(workflow.updatedAt).format("HH:mm:ss")}
              </span>
            </TooltipTrigger>
            <TooltipContent>{workflow.updatedAt}</TooltipContent>
          </Tooltip>
        </footer>
        <CreateDialog ref={createDialogRef} />
        <DeleteAlert ref={deleteAlertRef} onDeleted={onDeleted} />
      </section>
    </div>
  );
}

interface DeleteAlertRefObject {
  open: (workflow?: Workflow) => void;
}

interface DeleteAlertProps {
  ref: Ref<DeleteAlertRefObject>;
  onDeleted?: () => void;
}

function DeleteAlert({ ref, onDeleted }: DeleteAlertProps) {
  const [open, setOpen] = useState(false);
  const [workflow, setWorkflow] = useState<Workflow>();
  const workflowClient = useClient(WorkflowService);
  const [requestDeleteWorkflow, deleteLoading] = useGrpcRequestWithToken<
    OnlyIdRequest,
    CommonResponse
  >(workflowClient.deleteById);

  useImperativeHandle(ref, () => ({
    open: (workflow?: Workflow) => {
      setOpen(true);
      setWorkflow(workflow);
    },
  }));

  const handleConfirm = useCallback<MouseEventHandler>(
    async (event) => {
      event.preventDefault();
      if (!workflow) return;
      try {
        const res = await requestDeleteWorkflow({
          id: workflow?.id,
        });
        successToast(res.message);
        setOpen(false);
        onDeleted?.();
      } catch (err) {
        console.log(err);
      }
    },
    [onDeleted, requestDeleteWorkflow, workflow],
  );

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确定删除此工作流？</AlertDialogTitle>
          <AlertDialogDescription>删除后无法恢复</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>取消</AlertDialogCancel>
          <AlertDialogAction onClick={handleConfirm} disabled={deleteLoading}>
            {deleteLoading && <Loader2Icon className="animate-spin" />}
            确定
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
