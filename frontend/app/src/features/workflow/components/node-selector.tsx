import { But<PERSON> } from "@/components/ui/button.tsx";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ag<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ag<PERSON>, useState } from "react";
import { Plus, X } from "lucide-react";
import { cn } from "@/lib/utils.ts";
import {
  <PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/drawer";
import { Input } from "@/components/ui/input";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip.tsx";
import { useNodeSelectorDrag } from "@/components/workflow/hooks/use-node-selector-drag.tsx";
import { NodeIcon } from "@/components/workflow/node/node-icon.tsx";
import { NodeSchema } from "@/components/workflow/types/workflow.ts";

interface CategoryNodes {
  category: string;
  nodes: NodeSchema[];
}

function getCategoryNodes(nodes: NodeSchema[]): CategoryNodes[] {
  const category: { [index: string]: NodeSchema[] } = {};
  for (const node of nodes) {
    if (category[node.category] !== undefined) {
      category[node.category].push(node);
    } else {
      category[node.category] = [node];
    }
  }
  const categories: CategoryNodes[] = [];
  for (const categoryKey in category) {
    categories.push({
      category: categoryKey,
      nodes: category[categoryKey],
    });
  }
  return categories;
}

interface NodeSelectorProps {
  availableNodes: NodeSchema[];
}

export function NodeSelector({
  availableNodes,
  className,
}: NodeSelectorProps & ComponentProps<"div">) {
  const [open, setOpen] = useState(false);

  const { handleDragStart } = useNodeSelectorDrag();

  const categoryNodes = getCategoryNodes(availableNodes);

  return (
    <div className={cn("", className)}>
      <Drawer
        direction="left"
        modal={false}
        dismissible={false}
        open={open}
        onOpenChange={setOpen}
      >
        <DrawerTrigger asChild>
          <Button className="rounded-full w-9 h-9 p-4">
            <Plus />
          </Button>
        </DrawerTrigger>
        <DrawerContent className="mt-20 mb-4 outline-none border-none rounded-lg shadow-xl">
          <div className="mx-auto w-full max-w-sm">
            <DrawerHeader className="flex flex-row items-center">
              <VisuallyHidden asChild inert>
                <DrawerTitle>节点选择</DrawerTitle>
              </VisuallyHidden>
              <VisuallyHidden inert>
                <DrawerDescription>用于添加节点</DrawerDescription>
              </VisuallyHidden>
              <Input type="text" placeholder="搜索"></Input>
              <Button variant="ghost" onClick={() => setOpen(false)}>
                <X />
              </Button>
            </DrawerHeader>
            <div className="p-4 pb-0">
              {categoryNodes.map((categoryNode) => (
                <Fragment key={categoryNode.category}>
                  <div className="mb-2">{categoryNode.category}</div>
                  <ul className="flex flex-wrap">
                    {categoryNode.nodes.map((node) => (
                      <NodeItem
                        node={node}
                        key={node.name}
                        onDragStart={handleDragStart}
                      />
                    ))}
                  </ul>
                </Fragment>
              ))}
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </div>
  );
}

function NodeItem({
  node,
  onDragStart,
}: {
  node: NodeSchema;
  onDragStart: (node: NodeSchema) => DragEventHandler;
}) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <li
          className="flex items-center w-1/2 my-1 p-2 hover:bg-gray-100 dark:hover:bg-zinc-600 rounded-md cursor-pointer transition-all duration-75"
          draggable
          onDragStart={onDragStart(node)}
        >
          <NodeIcon icon={node.icon} />
          <span className="text-sm ml-2">{node.name}</span>
        </li>
      </TooltipTrigger>
      <TooltipContent>
        <p>{node.description}</p>
      </TooltipContent>
    </Tooltip>
  );
}
