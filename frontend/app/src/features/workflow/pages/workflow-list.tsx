import {
  Option,
  SearchFilter,
} from "@/features/workflow/components/search-filter.tsx";
import { useCallback, useEffect, useState } from "react";
import { useClient } from "@/hooks/use-client.ts";
import {
  ListWorkflowsRequest,
  ListWorkflowsResponse,
  Workflow,
  WorkflowService,
} from "@/proto/v1/workflow_service_pb.ts";
import { useGrpcRequestWithToken } from "@/hooks/use-grpc-request.ts";
import { ArrowDownUp, Loader2 } from "lucide-react";
import { WorkflowItem } from "@/features/workflow/components/workflow-item.tsx";
import { Button } from "@/components/ui/button.tsx";
import { useHeaderSlot } from "@/hooks/use-header-slot.ts";
import { TemplateSelectDialog } from "@/features/workflow/components/template-select-dialog.tsx";

function HeaderSlot() {
  return (
    <div className="flex gap-2">
      <TemplateSelectDialog />
    </div>
  );
}

export function WorkflowList() {
  const workflowClient = useClient(WorkflowService);
  const [requestWorkflowList, listLoading] = useGrpcRequestWithToken<
    ListWorkflowsRequest,
    ListWorkflowsResponse
  >(workflowClient.list);
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  useHeaderSlot(<HeaderSlot />);

  function handleTagsChange(tags: Option[]) {
    console.log(tags);
  }

  const loadData = useCallback(async () => {
    const res = await requestWorkflowList({});
    setWorkflows(res.workflows);
  }, [requestWorkflowList]);

  useEffect(() => {
    loadData();
  }, []);

  return (
    <>
      <header className="flex items-center gap-2 mt-1">
        <SearchFilter defaultOptions={[]} onSelectedChange={handleTagsChange} />
        <Button className="cursor-pointer" variant="outline">
          <ArrowDownUp />
        </Button>
      </header>
      <section className="flex items-center flex-wrap">
        {listLoading && <Loader2 />}
        {workflows.length > 0 &&
          workflows.map((workflow) => (
            <WorkflowItem
              className="w-full sm:w-1/2 xl:w-1/3 2xl:w-1/4"
              key={workflow.id}
              workflow={workflow}
              onDeleted={loadData}
            />
          ))}
      </section>
    </>
  );
}
