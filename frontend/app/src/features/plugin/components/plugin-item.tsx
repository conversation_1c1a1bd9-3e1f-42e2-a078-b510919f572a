import { Plugin } from "@/proto/v1/plugin_service_pb.ts";
import { cn } from "@/lib/utils.ts";
import { ComponentProps, useState } from "react";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar.tsx";
import { ArrowLeftRight, Ellipsis, Image, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button.tsx";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu.tsx";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip.tsx";
import { Badge } from "@/components/ui/badge.tsx";

interface PluginItemProps {
  plugin: Plugin;
}

export function PluginItem({
  plugin,
  className,
}: PluginItemProps & ComponentProps<"div">) {
  const [isMenuOpen, setMenuOpen] = useState(false);

  return (
    <div className={cn("p-2", className)}>
      <section className="flex flex-col rounded-lg border px-4 py-2 overflow-hidden group">
        <header className="flex w-full items-center">
          <div className="flex items-center flex-1 min-w-0">
            <Avatar className="rounded-lg mr-2">
              <AvatarImage
                src={`http://localhost:3000/plugin/icon/${plugin.name}/${plugin.version}`}
                alt={plugin.name}
              />
              <AvatarFallback className="rounded-lg">
                <Image size={18} />
              </AvatarFallback>
            </Avatar>
            <div>
              <span className="text-sm truncate mx-1 mr-2">
                {plugin.displayName}
              </span>
              <Badge variant="outline">{plugin.version}</Badge>
            </div>
          </div>
          <div
            className={cn(
              "operations flex items-center gap-2 transition-opacity duration-200",
              "opacity-0 pointer-events-none group-hover:opacity-100 group-hover:pointer-events-auto",
              isMenuOpen && "opacity-100 pointer-events-auto",
            )}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm">
                  <Trash2 />
                </Button>
              </TooltipTrigger>
              <TooltipContent>卸载</TooltipContent>
            </Tooltip>
            <DropdownMenu onOpenChange={setMenuOpen}>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Ellipsis />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-32" align="end" sideOffset={10}>
                <DropdownMenuItem>
                  <ArrowLeftRight />
                  切换版本
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>关于</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>
        <section className="w-full h-12 my-2 line-clamp-4">
          <div className="text-muted-foreground text-sm">
            {plugin.description}
          </div>
        </section>
        <footer className="flex gap-2"></footer>
      </section>
    </div>
  );
}
