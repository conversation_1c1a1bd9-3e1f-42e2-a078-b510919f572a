import { Plugin } from "@/proto/v1/plugin_service_pb.ts";
import { useEffect, useState } from "react";
import { Loading } from "@/components/loading.tsx";
import { PluginItem } from "@/features/plugin/components/plugin-item.tsx";
import { useListPlugins } from "@/features/plugin/api/plugin.ts";

export function PluginIndex() {
  const [plugins, setPlugins] = useState<Plugin[]>([]);
  const [requestPluginList, listLoading] = useListPlugins();

  async function loadData() {
    const response = await requestPluginList({});
    setPlugins(response.plugins);
  }

  useEffect(() => {
    loadData();
  }, []);

  if (listLoading) {
    return <Loading />;
  }

  return (
    <>
      <header></header>
      <section className="flex">
        {plugins.map((plugin) => (
          <PluginItem
            className="w-full sm:w-1/2 xl:w-1/3 2xl:w-1/4"
            plugin={plugin}
            key={plugin.id}
          />
        ))}
      </section>
    </>
  );
}
