import { CreateClient } from "@/lib/grpc.ts";
import {
  ListPluginsRequest,
  ListPluginsResponse,
  PluginService,
} from "@/proto/v1/plugin_service_pb.ts";
import { useGrpcRequestWithToken } from "@/hooks/use-grpc-request.ts";

const client = CreateClient(PluginService);

export function useListPlugins() {
  return useGrpcRequestWithToken<ListPluginsRequest, ListPluginsResponse>(
    client.list,
  );
}
