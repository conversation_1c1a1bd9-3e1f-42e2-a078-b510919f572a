import type { DescService } from "@bufbuild/protobuf";
import { useMemo } from "react";
import { CreateClient } from "@/lib/grpc.ts";

const grpcClients = new Map<DescService, ReturnType<typeof CreateClient>>();

export function useClient<T extends DescService>(service: T) {
  return useMemo(() => {
    if (!grpcClients.has(service)) {
      grpcClients.set(service, CreateClient(service));
    }
    return grpcClients.get(service)!;
  }, [service]);
}
