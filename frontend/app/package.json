{"name": "app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "test": "vitest", "clean": "rm -rf ./dist", "coverage": "vitest run --coverage", "lint": "eslint .", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@bufbuild/protobuf": "^2.5.2", "@connectrpc/connect": "^2.0.2", "@connectrpc/connect-web": "^2.0.2", "@hookform/resolvers": "^5.1.1", "@hugeicons/core-free-icons": "^1.0.14", "@hugeicons/react": "^1.0.5", "@lexical/react": "^0.33.1", "@lexical/utils": "^0.33.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.4", "@radix-ui/react-visually-hidden": "^1.2.2", "@remixicon/react": "^4.6.0", "@resflow/core": "workspace:*", "@resflow/session": "workspace:*", "@resflow/user": "workspace:*", "@resflow/api": "workspace:*", "@resflow-infra/http-client": "workspace:*", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.85.3", "@wendellhu/redi": "^1.0.0", "@xyflow/react": "^12.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dayjs": "^1.11.13", "immer": "^10.1.1", "lexical": "^0.33.1", "lucide-react": "^0.503.0", "nanoid": "^5.1.5", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-js-cron": "^5.2.0", "react-router": "^7.5.1", "react-use": "^17.6.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "utility-types": "^3.11.0", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.63", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/node": "^22.14.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "3.2.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "google-protobuf": "^3.21.4", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "3.5.3", "turbo": "^2.5.6", "tw-animate-css": "^1.2.8", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1", "vitest": "^3.2.4"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "packageManager": "pnpm@10.14.0"}