import { Interceptor } from "@resflow-infra/http-client";
import { ITokenStorage } from "@resflow/session";
import { createIdentifier } from "@wendellhu/redi";

export const ITokenInterceptor =
  createIdentifier<TokenInterceptor>("token-interceptor");

export class TokenInterceptor {
  private readonly _tokenStorage: ITokenStorage;

  constructor(@ITokenStorage tokenStorage: ITokenStorage) {
    this._tokenStorage = tokenStorage;
  }

  getInterceptor(): Interceptor {
    return {
      request: {
        onFulfilled: (config) => {
          const token = this._tokenStorage.getToken();
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
          return config;
        },
      },
    };
  }
}
