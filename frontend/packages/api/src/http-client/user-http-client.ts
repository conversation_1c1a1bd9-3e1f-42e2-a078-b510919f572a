import { <PERSON><PERSON>serClient, User } from "@resflow/user";
import { HttpClient } from "@resflow-infra/http-client";
import { IClient } from "../basic";

export class UserHttpClient implements IUserClient {
  private readonly _client: HttpClient;

  constructor(@IClient client: HttpClient) {
    this._client = client;
  }

  public async getCurrentUser(): Promise<User> {
    const res = await this._client.get<User>("/api/user/current");
    return res.data;
  }
}
