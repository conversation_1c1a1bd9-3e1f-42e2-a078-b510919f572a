import { HttpClient } from "@resflow-infra/http-client";
import { IClient } from "../basic";
import { IAuthClient, JWTTokenString, SessionToken } from "@resflow/session";

interface LoginRequest {
  username: string;
  password: string;
}

interface LoginResponse {
  token: string;
  refresh_token: string;
}

interface RefreshTokenResponse {
  token: string;
  refresh_token: string;
}

export class AuthHttpClient implements IAuthClient {
  private readonly _client: HttpClient;

  constructor(@IClient _client: HttpClient) {
    this._client = _client;
  }

  public async login(req: LoginRequest): Promise<SessionToken> {
    const res = await this._client.post<LoginResponse>("/api/login", {
      data: req,
    });
    return new SessionToken(
      new JWTTokenString(res.data.token),
      new JWTTokenString(res.data.refresh_token),
    );
  }

  async refreshToken(refreshToken: string): Promise<SessionToken> {
    const res = await this._client.post<RefreshTokenResponse>(
      "/api/token/refresh",
      {
        data: {
          refresh_token: refreshToken,
        },
      },
    );
    return new SessionToken(
      new JWTTokenString(res.data.token),
      new JWTTokenString(res.data.refresh_token),
    );
  }
}
