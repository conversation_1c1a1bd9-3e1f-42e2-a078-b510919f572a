import { Dependency } from "@wendellhu/redi";
import { AuthHttpClient } from "./http-client";
import { IAuthClient } from "@resflow/session";
import { IUserClient } from "@resflow/user";
import { UserHttpClient } from "./http-client/user-http-client.ts";

export const ApiDependencies: Dependency[] = [
  [IAuthClient, { useClass: AuthHttpClient, lazy: true }],
  [IUserClient, { useClass: UserHttpClient, lazy: true }],
];
