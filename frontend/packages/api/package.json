{"name": "@resflow/api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "vite build", "clean": "rm -rf ./lib", "test": "echo \"Error: no test specified\" && exit 1"}, "exports": {".": "./src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.14.0", "devDependencies": {"typescript": "^5.9.2", "vite": "^7.1.2", "vitest": "^3.2.4"}, "dependencies": {"@resflow-infra/http-client": "workspace:*", "@resflow-infra/shared": "workspace:*", "@resflow/session": "workspace:*", "@resflow/user": "workspace:*", "@wendellhu/redi": "^1.0.0"}}