import { createIdentifier } from "@wendellhu/redi";
import { ITokenString } from "../model/token-string.ts";
import { JWTTokenString } from "./jwt-token-string.ts";

export interface ITokenStorage {
  getToken(): ITokenString | null;
  getRefreshToken(): ITokenString | null;
  storeToken(token: ITokenString): void;
  storeRefreshToken(refreshToken: ITokenString): void;
  clearToken(): void;
}

export const ITokenStorage =
  createIdentifier<ITokenStorage>("auth.token-storage");

export class LocalStorageJWTTokenStorage implements ITokenStorage {
  private readonly _storageKey: string;
  private readonly _refreshTokenStorageKey: string;

  constructor(storageKey: string, refreshTokenStorageKey: string) {
    this._storageKey = storageKey;
    this._refreshTokenStorageKey = refreshTokenStorageKey;
  }

  getToken(): ITokenString | null {
    const item = localStorage.getItem(this._storageKey);
    if (!item) return null;
    return new JWTTokenString(item);
  }

  getRefreshToken(): ITokenString | null {
    const item = localStorage.getItem(this._refreshTokenStorageKey);
    if (!item) return null;
    return new JWTTokenString(item);
  }

  storeToken(token: ITokenString): void {
    localStorage.setItem(this._storageKey, token.toString());
  }

  storeRefreshToken(refreshToken: ITokenString) {
    localStorage.setItem(this._refreshTokenStorageKey, refreshToken.toString());
  }

  clearToken(): void {
    localStorage.removeItem(this._storageKey);
    localStorage.removeItem(this._refreshTokenStorageKey);
  }
}
