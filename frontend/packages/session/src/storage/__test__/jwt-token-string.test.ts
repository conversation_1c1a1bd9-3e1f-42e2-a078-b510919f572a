import { expect, test } from "vitest";
import { JWTTokenString } from "../jwt-token-string.ts";
import dayjs from "dayjs";

const expiredToken =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjVjNzEyYzU0LTg1N2YtNDJkMi1iZjdlLWVmYmM3MDk5NmQyNCIsInVzZXJuYW1lIjoiYWRtaW4iLCJuaWNrbmFtZSI6ImFkbWluIiwic3ViIjoiUmVzZmxvdyIsImV4cCI6MTc1NTc3MzA0MywibmJmIjoxNzU1NzY1ODQzLCJpYXQiOjE3NTU3NjU4NDN9.olzZrfxhk8Cg_j9Bjtl6pqlpm8UoktZ_rQ2A33OTWNo"; // expired at Thu Aug 21 2025 18:44:03 GMT+0800

test("token未过期", () => {
  const token = new JWTTokenString(expiredToken);
  expect(token.expired(dayjs("2025-08-21 18:44:02").toDate())).toBe(false);
});

test("token过期", () => {
  const token = new JWTTokenString(expiredToken);
  expect(token.expired(dayjs("2025-08-21 18:44:04").toDate())).toBe(true);
});
