import { JWTClaim } from "../type.ts";
import { parseToken } from "../util/jwt.ts";
import dayjs from "dayjs";
import { ITokenString } from "../model/token-string.ts";

export class JWTTokenString implements ITokenString {
  private readonly _token: string;

  constructor(token: string) {
    this._token = token;
  }

  expired(time: Date): boolean {
    try {
      const tokenClaim: JWTClaim = parseToken(this._token);
      return dayjs(time).isAfter(tokenClaim.exp * 1000);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (err) {
      return true;
    }
  }

  toString(): string {
    return this._token;
  }
}
