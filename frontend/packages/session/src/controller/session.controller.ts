import { IAuthClient } from "../client/auth-client.ts";
import { ITokenStorage } from "../storage";
import { SessionToViewModel, SessionViewModel } from "../viewmodel/session.ts";
import { IUserClient } from "@resflow/user";
import { Session, SessionToken } from "../model";

export interface CreateSessionCommand {
  username: string;
  password: string;
}

export class SessionController {
  private readonly _authenticationClient: IAuthClient;
  private readonly _userClient: IUserClient;
  private readonly _tokenStorage: ITokenStorage;

  constructor(
    @IAuthClient authenticationClient: IAuthClient,
    @IUserClient userClient: IUserClient,
    @ITokenStorage tokenStorage: ITokenStorage,
  ) {
    this._authenticationClient = authenticationClient;
    this._userClient = userClient;
    this._tokenStorage = tokenStorage;
  }

  async create(cmd: CreateSessionCommand): Promise<SessionViewModel> {
    // 登录
    const tokenInfo = await this._authenticationClient.login(cmd);
    // 保存token
    tokenInfo.store(this._tokenStorage);
    // 获取登录用户信息
    const user = await this._userClient.getCurrentUser();
    const session = new Session(user, tokenInfo);
    return SessionToViewModel(session);
  }

  async restore(): Promise<SessionViewModel | null> {
    // 读取token
    const token = this._tokenStorage.getToken();
    if (!token) {
      throw new Error("token不存在");
    }
    // 读取refreshToken
    const refreshToken = this._tokenStorage.getRefreshToken();
    const tokenInfo = new SessionToken(token, refreshToken);
    if (tokenInfo.tokenExpired) {
      if (tokenInfo.refreshTokenExpired) {
        throw new Error("Token expired");
      }
      // 刷新token
      const newToken = await this._authenticationClient.refreshToken(
        tokenInfo.refreshToken?.toString() || "",
      );
      newToken.store(this._tokenStorage);
    }
    // 获取登录用户信息
    const user = await this._userClient.getCurrentUser();
    const session = new Session(user, tokenInfo);
    return SessionToViewModel(session);
  }

  destroy() {
    this._tokenStorage.clearToken();
  }
}
