import { ITokenStorage } from "../storage";
import { ITokenString } from "./token-string.ts";
import dayjs from "dayjs";

export class SessionToken {
  private readonly _token: ITokenString;
  private readonly _refreshToken: ITokenString | null;

  constructor(token: ITokenString, refreshToken: ITokenString | null = null) {
    this._token = token;
    this._refreshToken = refreshToken;
  }

  get token(): ITokenString {
    return this._token;
  }

  get refreshToken(): ITokenString | null {
    return this._refreshToken;
  }

  get tokenExpired() {
    return this._token.expired(dayjs().toDate());
  }

  get refreshTokenExpired() {
    if (!this._refreshToken) {
      return true;
    }
    return this._refreshToken.expired(dayjs().toDate());
  }

  store(storage: ITokenStorage) {
    storage.storeToken(this._token);
    if (this._refreshToken) {
      storage.storeRefreshToken(this._refreshToken);
    }
  }
}
