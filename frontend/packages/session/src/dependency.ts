import { SessionController } from "./controller";
import { ITokenStorage, LocalStorageJWTTokenStorage } from "./storage";
import { Dependency } from "@wendellhu/redi";

export const AuthDependencies: Dependency[] = [
  [SessionController],

  [
    ITokenStorage,
    {
      useFactory() {
        return new LocalStorageJWTTokenStorage(
          "resflow.token",
          "resflow.refresh-token",
        );
      },
      lazy: true,
    },
  ],
];
