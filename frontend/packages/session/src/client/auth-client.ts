import { createIdentifier } from "@wendellhu/redi";
import { SessionToken } from "../model";

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RefreshRequest {
  refresh_token: string;
}

export interface IAuthClient {
  login(req: LoginRequest): Promise<SessionToken>;
  refreshToken(refreshToken: string): Promise<SessionToken>;
}

export const IAuthClient = createIdentifier<IAuthClient>("auth-client");
