{"name": "@resflow/session", "version": "1.0.0", "description": "", "scripts": {"build": "vite build", "clean": "rm -rf ./lib", "test": "vitest run"}, "main": "./src/index.ts", "exports": {".": "./src/index.ts"}, "keywords": [], "author": "resflow", "license": "ISC", "packageManager": "pnpm@10.14.0", "devDependencies": {"typescript": "~5.7.3", "vite": "^7.1.2", "vitest": "^3.2.4"}, "dependencies": {"@resflow-infra/shared": "workspace:*", "@resflow/user": "workspace:*", "@wendellhu/redi": "^1.0.0", "dayjs": "^1.11.13"}}