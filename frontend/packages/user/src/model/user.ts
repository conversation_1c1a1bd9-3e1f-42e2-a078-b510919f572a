import { UserStatus } from "./user-status.ts";

export class User {
  private readonly _id: string;
  private readonly _username: string;
  private readonly _nickname: string;
  private readonly _status: UserStatus;

  constructor(
    id: string,
    username: string,
    nickname: string,
    status: UserStatus,
  ) {
    this._id = id;
    this._username = username;
    this._nickname = nickname;
    this._status = status;
  }


  get id(): string {
    return this._id;
  }

  get username(): string {
    return this._username;
  }

  get nickname(): string {
    return this._nickname;
  }

  get status(): UserStatus {
    return this._status;
  }
}
