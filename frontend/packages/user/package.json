{"name": "@resflow/user", "version": "1.0.0", "description": "", "main": "./src/index.ts", "exports": {".": "./src/index.ts"}, "scripts": {"build": "vite build", "clean": "rm -rf ./lib", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "resflow", "license": "ISC", "packageManager": "pnpm@10.14.0", "devDependencies": {"typescript": "~5.7.3", "vite": "^6.3.3", "vitest": "^3.2.4"}, "dependencies": {"@resflow-infra/shared": "workspace:*", "@wendellhu/redi": "^1.0.0"}}