import { resolve } from "node:path";
import { defineConfig } from "vite";
import viteReact from "@vitejs/plugin-react";

export function createLibConfig() {
  const __dirname = process.cwd();

  return defineConfig({
    define: {
      "process.env.NODE_ENV": JSON.stringify("production"),
    },
    build: {
      outDir: "lib",
      lib: {
        entry: resolve(__dirname, "src/index.ts"),
        fileName: (format, entryName) => `${entryName}.${format}.js`,
        formats: ["es"],
      },
    },
    plugins: [viteReact()],
  });
}
