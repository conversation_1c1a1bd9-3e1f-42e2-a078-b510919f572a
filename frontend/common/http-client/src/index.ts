import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from "axios";

interface ResponseData<T extends object = object> {
  code?: string;
  msg: string;
  data: T;
}

export type Interceptor = {
  request?: {
    onFulfilled?: (
      config: InternalAxiosRequestConfig,
    ) => InternalAxiosRequestConfig | Promise<InternalAxiosRequestConfig>;
    onRejected?: (error: any) => any;
  };
  response?: {
    onFulfilled?: (
      response: AxiosResponse,
    ) => AxiosResponse | Promise<AxiosResponse>;
    onRejected?: (error: any) => any;
  };
};

export class HttpClient {
  private readonly _client: AxiosInstance;

  constructor(interceptors: Array<Interceptor> = []) {
    this._client = axios.create();

    this.__registerInterceptor(interceptors);
  }

  private __registerInterceptor(interceptors: Array<Interceptor>) {
    for (const interceptor of interceptors) {
      if (interceptor.request !== undefined) {
        this._client.interceptors.request.use(
          interceptor.request.onFulfilled,
          interceptor.request.onRejected,
        );
      } else if (interceptor.response !== undefined) {
        this._client.interceptors.response.use(
          interceptor.response.onFulfilled,
          interceptor.response.onRejected,
        );
      }
    }
  }

  private async request<
    R extends object,
    Response extends ResponseData<R> = ResponseData<R>,
  >(url: string, options: Omit<AxiosRequestConfig, "url">): Promise<Response> {
    const res = await this._client.request<Response>({
      url,
      ...options,
    });
    return res.data;
  }

  public async get<R extends object>(
    url: string,
    options?: AxiosRequestConfig,
  ) {
    return this.request<R>(url, { method: "GET", ...options });
  }

  public async post<R extends object>(
    url: string,
    options?: AxiosRequestConfig,
  ) {
    return this.request<R>(url, { method: "POST", ...options });
  }

  public async put<R extends object>(
    url: string,
    options?: AxiosRequestConfig,
  ) {
    return this.request<R>(url, { method: "PUT", ...options });
  }

  public async delete<R extends object>(
    url: string,
    options?: AxiosRequestConfig,
  ) {
    return this.request<R>(url, { method: "DELETE", ...options });
  }
}
